# Insights Formatting Solution

## Problem Solved
The Knowledge Base responses were being incorrectly parsed, causing individual lines of content to appear as separate clickable buttons in the UI, rather than cohesive recommendations with supporting information.

## Solution Implemented

### 1. Structured Prompt Templates
Updated the Knowledge Base prompts to use a consistent structure:
- **PRIMARY RECOMMENDATION**: Single, actionable statement
- **MEASUREMENT DETAILS**: Implementation specifics
- **RATIONALE**: Scientific justification
- **ALTERNATIVE OPTIONS**: Other viable choices
- **SAMPLE SIZE ESTIMATE**: Statistical calculations
- **SIMILAR STUDIES**: NCT references

### 2. Enhanced Response Parsing
The `bedrock-kb-client.ts` now:
- Identifies section types (recommendation, details, rationale, etc.)
- Marks sections as actionable or informational
- Properly formats content without breaking it into fragments
- Assigns confidence scores based on section type

### 3. Type System Updates
Added to `trial-design.ts`:
```typescript
export interface InsightSection {
  title: string;
  content: string;
  type?: 'recommendation' | 'information' | 'details' | 'rationale' | 'alternatives' | 'calculation' | 'references' | 'considerations';
  actionable?: boolean;
  citations: Citation[];
  confidence?: number;
}
```

### 4. UI Component Improvements
The `InsightsPanel` now:
- Groups sections into "Recommendations" and "Supporting Information"
- Only shows "Apply" button for actionable recommendations
- Uses different icons and styling for different section types
- Displays recommendations prominently with primary styling
- Shows supporting information as secondary cards

## How It Works

1. **Query Sent**: User clicks "Get Insights" button
2. **Structured Prompt**: Lambda uses formatted prompt template
3. **Knowledge Base Response**: Returns markdown with clear section headers
4. **Parsing**: Sections are identified by type and actionability
5. **UI Display**: 
   - Recommendations appear as primary cards with "Apply" buttons
   - Supporting information appears as secondary cards without actions
   - No more fragmented button lists

## Testing
To test the new format:
```bash
curl -X POST http://localhost:3001/query \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What primary endpoints for Phase 2 drug trial?",
    "field": "primary-endpoint",
    "context": {
      "studyType": "drug",
      "condition": "Major Depressive Disorder",
      "phase": "Phase 2"
    }
  }'
```

## Results
- Clear separation between actionable recommendations and background information
- Clean, organized UI without excessive buttons
- Users can easily identify what can be applied vs what is informational
- Proper markdown formatting preserved in content display