# TriaLynx Insights

An AI-powered clinical trial protocol generation platform that leverages AWS Bedrock Knowledge Base to provide intelligent recommendations for trial design.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm
- AWS CLI configured with appropriate credentials (if using deployed backend)
- Docker (if running Lambda functions locally with SAM)
- Access to AWS Bedrock Knowledge Base (if using real AI insights)

## 🖥️ Local Development Setup

### Option 1: Quick Start (Using Deployed Backend)

This is the easiest way to run the app locally while using the deployed AWS infrastructure.

#### 1. <PERSON><PERSON> and Install

```bash
git clone <repository-url>
cd trialynx-insights/apps/web
npm install
```

#### 2. Environment Setup

**📋 Copy the example file:**
```bash
cp .env.local.example .env.local
```

**✏️ Edit `.env.local` in `apps/web/` with your values:**

```bash
# Authentication - generate with: npx auth secret
AUTH_SECRET="your-auth-secret-here"

# Google OAuth (get from Google Cloud Console)
AUTH_GOOGLE_ID="your-google-oauth-client-id"
AUTH_GOOGLE_SECRET="your-google-oauth-client-secret"

# AWS Configuration - IMPORTANT: AWS Profile for local development
# This must match your AWS SSO profile name. Get it with: aws configure list-profiles
AWS_PROFILE=your-aws-sso-profile-name
AWS_REGION=us-west-2

# DynamoDB Tables (using deployed infrastructure)
AUTH_TABLE_NAME=trialynx-insights-dev-next-auth
ALLOWLIST_TABLE_NAME=trialynx-insights-dev-allowlist
WAITLIST_TABLE_NAME=trialynx-insights-dev-waitlist
STUDY_SESSIONS_TABLE_NAME=trialynx-insights-dev-study-design-sessions

# AWS Services
BEDROCK_KNOWLEDGE_BASE_ID=M0KKCP0UI9
LAMBDA_ENDPOINT_URL=https://81cjr0fh5l.execute-api.us-west-2.amazonaws.com/dev

# Feature Flags
NEXT_PUBLIC_USE_MOCK_DATA=false
NEXT_PUBLIC_ENABLE_AWS=true

# NextAuth URLs
AUTH_URL="http://localhost:3000"
NEXTAUTH_URL="http://localhost:3000"

# Node Environment
NODE_ENV=development
```

> ⚠️ **Important**: The `AWS_PROFILE` is required for local development to access DynamoDB and other AWS services. Without it, you'll see "Configuration" errors during authentication.

#### 3. Run the Application

```bash
cd apps/web
npm run dev
```

🎉 **The app will be available at http://localhost:3000 (or next available port)**

### Option 2: Full Local Development (with Local Lambda)

This setup runs everything locally including Lambda functions.

#### 1. Install All Dependencies

```bash
# Install web app dependencies
cd apps/web
npm install

# Install Lambda dependencies  
cd ../lambda
npm install
```

#### 2. Web App Environment Configuration

**📋 Copy and edit environment:**
```bash
cp .env.local.example .env.local
```

Edit `.env.local` in `apps/web/` for local Lambda development:

```bash
# Authentication - generate with: npx auth secret
AUTH_SECRET="your-auth-secret-here"

# AWS Configuration - IMPORTANT for local development
AWS_PROFILE=your-aws-sso-profile-name  # Get with: aws configure list-profiles
AWS_REGION=us-west-2
LAMBDA_ENDPOINT_URL=http://localhost:3001  # SAM local endpoint

# DynamoDB Tables
AUTH_TABLE_NAME=trialynx-insights-dev-next-auth
ALLOWLIST_TABLE_NAME=trialynx-insights-dev-allowlist
WAITLIST_TABLE_NAME=trialynx-insights-dev-waitlist
STUDY_SESSIONS_TABLE_NAME=trialynx-insights-dev-study-design-sessions

# Feature Flags
NEXT_PUBLIC_USE_MOCK_DATA=false
NEXT_PUBLIC_ENABLE_AWS=true

# NextAuth URLs
AUTH_URL="http://localhost:3000"
NEXTAUTH_URL="http://localhost:3000"

# Node Environment
NODE_ENV=development
```

#### 3. Lambda Environment Configuration

Create `env.json` in `apps/lambda/`:

```json
{
  "Parameters": {
    "BEDROCK_KNOWLEDGE_BASE_ID": "your-knowledge-base-id",
    "BEDROCK_MODEL_ID": "us.anthropic.claude-sonnet-4-20250514-v1:0", 
    "S3_BUCKET_NAME": "trialynx-clinical-trials-gov",
    "AWS_REGION": "us-west-2",
    "CORS_ORIGIN": "http://localhost:3000"
  }
}
```

**💡 Tip:** For testing without AWS Bedrock, set `BEDROCK_KNOWLEDGE_BASE_ID` to `"SKIP_FOR_LOCAL_TESTING"` to use mock data.

#### 4. Build and Start Lambda Functions

```bash
cd apps/lambda
npm run build
sam build
sam local start-api --port 3001 --env-vars env.json
```

#### 5. Start Web Application (in a new terminal)

```bash
cd apps/web
npm run dev
```

🎉 **The application will be available at:**
- **Web App**: http://localhost:3000 (or next available port)
- **Lambda API**: http://localhost:3001

---

## 🏃‍♂️ Quick Development Commands

```bash
# Start frontend only (using deployed backend)
cd apps/web && npm run dev

# Start everything locally (2 terminals needed)
# Terminal 1:
cd apps/lambda && sam local start-api --port 3001 --env-vars env.json
# Terminal 2: 
cd apps/web && npm run dev

# Build for production
cd apps/web && npm run build

# Run tests
cd apps/web && npm run lint
cd apps/web && npm run typecheck
```

## 📁 Project Structure

```
trialynx-insights/
├── apps/
│   ├── web/                    # Next.js web application
│   │   ├── src/
│   │   │   ├── app/            # App router pages
│   │   │   ├── components/     # React components
│   │   │   ├── server/         # tRPC API routes
│   │   │   └── store/          # Zustand state management
│   │   └── .env                # Web app environment variables
│   │
│   └── lambda/                 # AWS Lambda functions
│       ├── src/
│       │   ├── handlers/       # Lambda function handlers
│       │   └── utils/          # Shared utilities
│       ├── env.json            # Lambda environment variables
│       └── template.yaml       # SAM template
│
├── critical-questions-json/    # Protocol question definitions
└── analysis/                   # Coverage analysis documents
```

## 🔧 Key Features

- **AI-Powered Insights**: Leverages AWS Bedrock Knowledge Base for intelligent trial design recommendations
- **Multi-Step Protocol Builder**: Guided workflow for creating comprehensive clinical trial protocols
- **Real-Time Recommendations**: Context-aware suggestions based on similar successful trials
- **Document Viewer**: Browse and reference source clinical trials
- **Export Capabilities**: Generate protocol documents in various formats

## 🛠️ Development Tools

### Running Tests

```bash
# Run web app tests
cd apps/web
npm test

# Run Lambda tests
cd apps/lambda
npm test
```

### Building for Production

```bash
# Build web app
cd apps/web
npm run build

# Build Lambda functions
cd apps/lambda
npm run build
sam build
```

### Deploying to AWS

```bash
# Deploy Lambda functions
cd apps/lambda
sam deploy --guided

# Update web app environment with the API Gateway URL from SAM output
# Then deploy the web app to your preferred platform (Vercel, AWS Amplify, etc.)
```

## 🔑 Environment Variables Reference

### Web Application Variables

| Variable | Required | Description | Default/Example |
|----------|----------|-------------|-----------------|
| `AUTH_SECRET` | ✅ | Secret key for authentication | Generate with `npx auth secret` |
| `AWS_PROFILE` | ✅ | AWS CLI profile for local development | Your SSO profile name (get with `aws configure list-profiles`) |
| `AWS_REGION` | ✅ | AWS region for services | `us-west-2` |
| `LAMBDA_ENDPOINT_URL` | ✅ | Lambda endpoint URL | Local: `http://localhost:3001`<br/>Deployed: `https://api-gateway-url.com/dev` |
| `AUTH_TABLE_NAME` | ✅ | DynamoDB table for NextAuth | `trialynx-insights-dev-next-auth` |
| `ALLOWLIST_TABLE_NAME` | ✅ | DynamoDB table for allowlist | `trialynx-insights-dev-allowlist` |
| `WAITLIST_TABLE_NAME` | ✅ | DynamoDB table for waitlist | `trialynx-insights-dev-waitlist` |
| `STUDY_SESSIONS_TABLE_NAME` | ✅ | DynamoDB table for sessions | `trialynx-insights-dev-study-design-sessions` |
| `BEDROCK_KNOWLEDGE_BASE_ID` | ✅ | AWS Bedrock Knowledge Base ID | `M0KKCP0UI9` |
| `AUTH_GOOGLE_ID` | ❌ | Google OAuth client ID | From Google Cloud Console |
| `AUTH_GOOGLE_SECRET` | ❌ | Google OAuth client secret | From Google Cloud Console |
| `NEXTAUTH_URL` | ✅ | NextAuth base URL | `http://localhost:3000` |
| `AUTH_URL` | ✅ | Authentication URL | `http://localhost:3000` |
| `NEXT_PUBLIC_USE_MOCK_DATA` | ❌ | Use mock data instead of real APIs | `false` |
| `NEXT_PUBLIC_ENABLE_AWS` | ❌ | Enable AWS integrations | `true` |
| `NODE_ENV` | ❌ | Node environment | `development` |

### Lambda Function Variables

| Variable | Required | Description | Default/Example |
|----------|----------|-------------|-----------------|
| `BEDROCK_KNOWLEDGE_BASE_ID` | ✅ | AWS Bedrock Knowledge Base ID | Your KB ID or `"SKIP_FOR_LOCAL_TESTING"` |
| `BEDROCK_MODEL_ID` | ✅ | Bedrock model identifier | `us.anthropic.claude-sonnet-4-20250514-v1:0` |
| `S3_BUCKET_NAME` | ✅ | S3 bucket with clinical trials data | `trialynx-clinical-trials-gov` |
| `AWS_REGION` | ✅ | AWS region for services | `us-west-2` |
| `CORS_ORIGIN` | ❌ | Allowed CORS origins | `http://localhost:3000` |

## 🐛 Troubleshooting

### Common Issues

1. **"Module not found: Can't resolve 'nodemailer'"**
   - ✅ **Fixed!** Email provider is now disabled to avoid unnecessary dependency
   - Email authentication can be re-enabled when SES is implemented

2. **"Session creation loop" / Repeated session attempts**
   - ✅ **Fixed!** This was resolved by migrating to DynamoDB session storage
   - Sessions now persist across server restarts

3. **"Unauthorized" errors when using insights**
   - ✅ **Fixed!** JWT authentication is now working properly
   - Make sure you're signed in with Google SSO

4. **"Port 3000/3001 is already in use"**
   - Kill existing processes: `lsof -ti :3000 | xargs kill -9`
   - Next.js will automatically use the next available port
   - For SAM local, change port: `sam local start-api --port 3002`

4. **"Knowledge Base with id XXX does not exist"**
   - Verify your `BEDROCK_KNOWLEDGE_BASE_ID` is correct
   - Ensure your AWS profile has access to the Knowledge Base  
   - For testing, set ID to `"SKIP_FOR_LOCAL_TESTING"` in Lambda env.json

5. **"fetch failed" or API connection errors**
   - **Using deployed backend**: Check that `LAMBDA_ENDPOINT_URL` points to the correct API Gateway
   - **Using local SAM**: Ensure SAM local is running before starting web app
   - **CORS issues**: Verify `CORS_ORIGIN` in Lambda env.json matches your frontend URL

6. **"DynamoDB table not found" errors**
   - Ensure the DynamoDB table exists in AWS: `trialynx-insights-dev-study-design-sessions`
   - Check that `STUDY_SESSIONS_TABLE_NAME` environment variable is set correctly

7. **Missing environment variables**
   - Double-check all required variables are set in `.env.local`
   - Restart the web app after updating environment variables
   - For Lambda: verify `env.json` has all required parameters

8. **"Configuration" errors during authentication / AWS credential issues**
   - ✅ **Solution**: Add `AWS_PROFILE=your-sso-profile-name` to `.env.local`
   - Check your AWS profile name: `aws configure list-profiles`
   - Ensure AWS SSO session is active: `aws sso login --profile your-profile-name`
   - The DynamoDB adapter requires AWS credentials to function properly
   - Without AWS credentials, NextAuth will show "Configuration" errors that appear OAuth-related but are actually infrastructure authentication issues

### 🏥 Health Checks

```bash
# Test if backend is responding
curl https://81cjr0fh5l.execute-api.us-west-2.amazonaws.com/dev/health

# Test local SAM (if running locally)
curl http://localhost:3001/health

# Check DynamoDB table exists
aws dynamodb describe-table --table-name trialynx-insights-dev-study-design-sessions
```

## 📚 Documentation

- [AWS Bedrock Documentation](https://docs.aws.amazon.com/bedrock/)
- [AWS SAM Documentation](https://docs.aws.amazon.com/serverless-application-model/)
- [Next.js Documentation](https://nextjs.org/docs)
- [T3 Stack Documentation](https://create.t3.gg/)

## 🤝 Contributing

Please read our contributing guidelines before submitting pull requests.

## 📄 License

[License information here]