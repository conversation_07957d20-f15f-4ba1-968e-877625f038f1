# Terraform configuration for prod account DNS setup
# This should be deployed in the production AWS account that owns the trialynx.io domain

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# Configure the AWS Provider for prod account
provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Project     = "TriaLynx-Insights"
      Environment = "prod-dns"
      ManagedBy   = "Terraform"
      Component   = "DNS"
    }
  }
}

# Data source for existing Route 53 zone
data "aws_route53_zone" "main" {
  name         = var.domain_zone
  private_zone = false
}

# Create DNS validation records for ACM certificate
# These values come from the dev account Terraform outputs
resource "aws_route53_record" "cert_validation" {
  for_each = {
    for dvo in var.acm_domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }

  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = data.aws_route53_zone.main.zone_id
}

# Create Alias record pointing to dev account ALB
resource "aws_route53_record" "app_alias_a" {
  zone_id = data.aws_route53_zone.main.zone_id
  name    = var.app_domain_name
  type    = "A"

  alias {
    name                   = var.dev_alb_dns_name
    zone_id                = var.dev_alb_zone_id
    evaluate_target_health = false
  }
}

resource "aws_route53_record" "app_alias_aaaa" {
  zone_id = data.aws_route53_zone.main.zone_id
  name    = var.app_domain_name
  type    = "AAAA"

  alias {
    name                   = var.dev_alb_dns_name
    zone_id                = var.dev_alb_zone_id
    evaluate_target_health = false
  }
}

# SES domain verification record
resource "aws_route53_record" "ses_verification" {
  zone_id = data.aws_route53_zone.main.zone_id
  name    = "_amazonses.${var.ses_domain}"
  type    = "TXT"
  ttl     = 300
  records = [var.ses_verification_token]
}

# SES DKIM records
resource "aws_route53_record" "ses_dkim" {
  count   = 3
  zone_id = data.aws_route53_zone.main.zone_id
  name    = "${var.ses_dkim_tokens[count.index]}._domainkey.${var.ses_domain}"
  type    = "CNAME"
  ttl     = 300
  records = ["${var.ses_dkim_tokens[count.index]}.dkim.amazonses.com"]
}

# Optional: SPF record for email security
resource "aws_route53_record" "spf" {
  zone_id = data.aws_route53_zone.main.zone_id
  name    = var.ses_domain
  type    = "TXT"
  ttl     = 300
  records = ["v=spf1 include:amazonses.com ~all"]
}

# Optional: DMARC record for email security
resource "aws_route53_record" "dmarc" {
  zone_id = data.aws_route53_zone.main.zone_id
  name    = "_dmarc.${var.ses_domain}"
  type    = "TXT"
  ttl     = 300
  records = ["v=DMARC1; p=quarantine; rua=mailto:dmarc@${var.ses_domain}"]
}