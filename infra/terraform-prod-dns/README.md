# Production Account DNS Setup

This Terraform configuration is designed to be deployed in the **production AWS account** that owns the `trialynx.io` domain and Route 53 hosted zone.

## Purpose

This configuration creates the necessary DNS records to support the cross-account deployment pattern where:
- The application infrastructure is in the **dev account**
- The DNS zone is managed in the **prod account**

## Records Created

1. **ACM Certificate Validation Records**: CNAME records to validate the SSL certificate requested in the dev account
2. **Application Alias Records**: A and AAAA records pointing `dev.insights.trialynx.io` to the dev account's ALB
3. **SES Verification Records**: TXT and CNAME records for email domain verification and DKIM
4. **Email Security Records**: SPF and DMARC records for email security

## Setup Instructions

### 1. Get Values from Dev Account

After deploying the main Terraform configuration in the dev account, run:

```bash
cd ../terraform
terraform output
```

Copy the following output values:
- `alb_dns_name`
- `alb_zone_id` 
- `acm_domain_validation_options`
- `ses_domain_verification_token`
- `ses_dkim_tokens`

### 2. Configure Variables

```bash
cp terraform.tfvars.example terraform.tfvars
```

Edit `terraform.tfvars` with the values from step 1.

### 3. Deploy

```bash
# Initialize Terraform
terraform init

# Plan the deployment
terraform plan

# Apply the configuration
terraform apply
```

### 4. Verify

After deployment:
1. Check that the ACM certificate in the dev account shows as "Issued"
2. Verify that `https://dev.insights.trialynx.io` resolves to the dev account ALB
3. Confirm SES domain verification in the dev account

## Important Notes

- This must be deployed by someone with admin access to the **prod account**
- The Route 53 hosted zone for `trialynx.io` must already exist
- DNS propagation may take up to 48 hours, but typically completes within minutes
- The ACM certificate validation will fail until these DNS records are created

## Security Considerations

- Only creates DNS records, no infrastructure resources
- Does not grant any cross-account access
- SPF and DMARC records help prevent email spoofing

## Troubleshooting

If the ACM certificate doesn't validate:
1. Check that the CNAME records were created correctly
2. Wait 5-10 minutes for DNS propagation
3. Verify the domain validation options haven't changed

If SES verification fails:
1. Ensure the TXT record value matches exactly
2. Check that DKIM CNAME records point to the correct Amazon endpoints
3. Verify domain ownership in the SES console