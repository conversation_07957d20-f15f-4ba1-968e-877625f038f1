# Variables for prod account DNS setup

variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-east-1"
}

variable "domain_zone" {
  description = "The root domain zone (e.g., trialynx.io)"
  type        = string
  default     = "trialynx.io"
}

variable "app_domain_name" {
  description = "The application domain name (e.g., dev.insights.trialynx.io)"
  type        = string
  default     = "dev.insights.trialynx.io"
}

# These values should come from the dev account Terraform outputs
variable "dev_alb_dns_name" {
  description = "DNS name of the ALB in dev account"
  type        = string
}

variable "dev_alb_zone_id" {
  description = "Zone ID of the ALB in dev account"
  type        = string
}

variable "acm_domain_validation_options" {
  description = "Domain validation options from ACM certificate in dev account"
  type = set(object({
    domain_name           = string
    resource_record_name  = string
    resource_record_type  = string
    resource_record_value = string
  }))
}

# SES Configuration
variable "ses_domain" {
  description = "Domain for SES verification"
  type        = string
  default     = "trialynx.io"
}

variable "ses_verification_token" {
  description = "SES domain verification token from dev account"
  type        = string
}

variable "ses_dkim_tokens" {
  description = "SES DKIM tokens from dev account"
  type        = list(string)
}