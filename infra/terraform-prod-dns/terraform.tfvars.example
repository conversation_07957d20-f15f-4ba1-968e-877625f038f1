# Example terraform.tfvars for prod account DNS setup
# Copy this file to terraform.tfvars and fill in the actual values

aws_region     = "us-east-1"
domain_zone    = "trialynx.io"
app_domain_name = "dev.insights.trialynx.io"

# Get these values from the dev account Terraform outputs
dev_alb_dns_name = "trialynx-insights-dev-alb-*********.us-west-2.elb.amazonaws.com"
dev_alb_zone_id  = "Z1D633PJN98FT9"

# ACM domain validation options from dev account
acm_domain_validation_options = [
  {
    domain_name           = "dev.insights.trialynx.io"
    resource_record_name  = "_abc123.dev.insights.trialynx.io."
    resource_record_type  = "CNAME"
    resource_record_value = "_xyz789.acm-validations.aws."
  }
]

# SES configuration from dev account
ses_domain             = "trialynx.io"
ses_verification_token = "abc123def456ghi789"
ses_dkim_tokens        = ["token1", "token2", "token3"]