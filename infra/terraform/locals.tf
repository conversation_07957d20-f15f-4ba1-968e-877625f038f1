locals {
  project_name = "trialynx-insights"
  name_prefix  = "${local.project_name}-${var.environment}"
  
  common_tags = {
    Project     = "TriaLynx-Insights"
    Environment = var.environment
    ManagedBy   = "Terraform"
    Owner       = var.project_owner
  }
  
  # Legacy Lambda configuration (keeping for backward compatibility)
  lambda_function_name = "${local.project_name}-${var.environment}-query-kb"
  api_gateway_name     = "${local.project_name}-${var.environment}-api"
  
  lambda_environment = {
    BEDROCK_KNOWLEDGE_BASE_ID = var.bedrock_knowledge_base_id
    BEDROCK_MODEL_ID          = var.bedrock_model_id
    CORS_ORIGIN               = join(",", var.cors_allowed_origins)
    JWT_SECRET_ARN            = data.aws_secretsmanager_secret.jwt_secret.arn
  }
  
  # ECS and ALB configuration
  cluster_name = "${local.name_prefix}-cluster"
  service_name = "${local.name_prefix}-service"
  
  # Container environment variables
  container_environment = [
    {
      name  = "NODE_ENV"
      value = "production"
    },
    {
      name  = "AUTH_URL"
      value = "http://${aws_lb.main.dns_name}"
    },
    {
      name  = "NEXTAUTH_URL"
      value = "http://${aws_lb.main.dns_name}"
    },
    {
      name  = "AUTH_TABLE_NAME"
      value = aws_dynamodb_table.next_auth.name
    },
    {
      name  = "ALLOWLIST_TABLE_NAME"
      value = aws_dynamodb_table.allowlist.name
    },
    {
      name  = "WAITLIST_TABLE_NAME"
      value = aws_dynamodb_table.waitlist.name
    },
    {
      name  = "EMAIL_FROM"
      value = var.email_from_address
    },
    {
      name  = "BEDROCK_KNOWLEDGE_BASE_ID"
      value = var.bedrock_knowledge_base_id
    },
    {
      name  = "LAMBDA_ENDPOINT_URL"
      value = "https://${aws_api_gateway_rest_api.main.id}.execute-api.${var.aws_region}.amazonaws.com/${aws_api_gateway_stage.main.stage_name}"
    }
  ]
  
  # Secrets to be retrieved from AWS Secrets Manager
  container_secrets = [
    {
      name      = "AUTH_SECRET"
      valueFrom = aws_secretsmanager_secret.auth_secret.arn
    },
    {
      name      = "AUTH_GOOGLE_ID"
      valueFrom = aws_secretsmanager_secret.oauth_secrets.arn
      valueFromType = "AUTH_GOOGLE_ID"
    },
    {
      name      = "AUTH_GOOGLE_SECRET"
      valueFrom = aws_secretsmanager_secret.oauth_secrets.arn
      valueFromType = "AUTH_GOOGLE_SECRET"
    },
    {
      name      = "AUTH_AZURE_AD_ID"
      valueFrom = aws_secretsmanager_secret.oauth_secrets.arn
      valueFromType = "AUTH_AZURE_AD_ID"
    },
    {
      name      = "AUTH_AZURE_AD_SECRET"
      valueFrom = aws_secretsmanager_secret.oauth_secrets.arn
      valueFromType = "AUTH_AZURE_AD_SECRET"
    },
    {
      name      = "AUTH_AZURE_AD_TENANT_ID"
      valueFrom = aws_secretsmanager_secret.oauth_secrets.arn
      valueFromType = "AUTH_AZURE_AD_TENANT_ID"
    },
    {
      name      = "EMAIL_SERVER"
      valueFrom = aws_secretsmanager_secret.email_secret.arn
    }
  ]
}