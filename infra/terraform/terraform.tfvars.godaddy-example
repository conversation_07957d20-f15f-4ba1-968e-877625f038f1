# Terraform variables for GoDaddy DNS + AWS deployment
# Copy these values to terraform.tfvars and update with your actual values

# Basic Configuration
aws_region     = "us-west-2"
environment    = "dev"
project_owner  = "<EMAIL>"

# Domain Configuration - using subdomain delegation
domain_name = "dev.insights.trialynx.io"
# NOTE: Remove or comment out route53_zone_id since we're creating our own zone

# Bedrock Configuration (get from your existing setup)
bedrock_knowledge_base_id = "M0KKCP0UI9"  # Replace with your actual KB ID
bedrock_model_id         = "us.anthropic.claude-sonnet-4-20250514-v1:0"

# Container Configuration (will be updated after ECR creation)
container_image = "nginx:latest"
container_port  = 3000

# Scaling Configuration
desired_count = 1
min_capacity  = 1
max_capacity  = 3

# Email Configuration (keep your existing SES setup)
ses_domain          = "trialynx.io"
email_from_address  = "<EMAIL>"

# CORS Configuration
cors_allowed_origins = [
  "https://dev.insights.trialynx.io",
  "http://localhost:3000"
]

# Legacy JWT Configuration
jwt_secret_name = "trialynx-jwt-secret"