# ACM Certificate for the application domain
# This will be created in the dev account but validated via DNS in the prod account

resource "aws_acm_certificate" "app_cert" {
  domain_name       = var.domain_name
  validation_method = "DNS"

  subject_alternative_names = [
    "*.${var.domain_name}"
  ]

  lifecycle {
    create_before_destroy = true
  }

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-cert"
    Domain = var.domain_name
  })
}

# Certificate validation
# Note: If using subdomain delegation (route53-subdomain.tf), 
# validation will be automatic. Otherwise, DNS records need to be 
# created manually in GoDaddy or external DNS provider.