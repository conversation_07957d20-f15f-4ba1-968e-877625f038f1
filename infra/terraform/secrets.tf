# Secrets Manager for authentication and OAuth secrets

# Auth.js secret for signing JWTs
resource "aws_secretsmanager_secret" "auth_secret" {
  name        = "${local.name_prefix}-auth-secret"
  description = "NextAuth.js secret for signing JWTs and sessions"

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-auth-secret"
    Purpose = "Authentication"
  })
}

# OAuth provider credentials
resource "aws_secretsmanager_secret" "oauth_secrets" {
  name        = "${local.name_prefix}-oauth-secrets"
  description = "OAuth provider client IDs and secrets"

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-oauth-secrets"
    Purpose = "OAuth Authentication"
  })
}

# Email server configuration
resource "aws_secretsmanager_secret" "email_secret" {
  name        = "${local.name_prefix}-email-secret"
  description = "SMTP server configuration for sending emails"

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-email-secret"
    Purpose = "Email Delivery"
  })
}

# Placeholder secret versions (to be updated manually or via CI/CD)
resource "aws_secretsmanager_secret_version" "auth_secret_placeholder" {
  secret_id = aws_secretsmanager_secret.auth_secret.id
  secret_string = jsonencode({
    AUTH_SECRET = "PLACEHOLDER_VALUE_REPLACE_ME"
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}

resource "aws_secretsmanager_secret_version" "oauth_secrets_placeholder" {
  secret_id = aws_secretsmanager_secret.oauth_secrets.id
  secret_string = jsonencode({
    AUTH_GOOGLE_ID = "PLACEHOLDER_VALUE_REPLACE_ME"
    AUTH_GOOGLE_SECRET = "PLACEHOLDER_VALUE_REPLACE_ME"
    AUTH_AZURE_AD_ID = "PLACEHOLDER_VALUE_REPLACE_ME"
    AUTH_AZURE_AD_SECRET = "PLACEHOLDER_VALUE_REPLACE_ME"
    AUTH_AZURE_AD_TENANT_ID = "PLACEHOLDER_VALUE_REPLACE_ME"
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}

resource "aws_secretsmanager_secret_version" "email_secret_placeholder" {
  secret_id = aws_secretsmanager_secret.email_secret.id
  secret_string = jsonencode({
    EMAIL_SERVER = "PLACEHOLDER_VALUE_REPLACE_ME"
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}

# IAM policy for accessing secrets
resource "aws_iam_policy" "secrets_access" {
  name        = "${local.name_prefix}-secrets-access"
  description = "IAM policy for accessing Secrets Manager secrets"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue",
          "secretsmanager:DescribeSecret"
        ]
        Resource = [
          aws_secretsmanager_secret.auth_secret.arn,
          aws_secretsmanager_secret.oauth_secrets.arn,
          aws_secretsmanager_secret.email_secret.arn
        ]
      }
    ]
  })

  tags = local.common_tags
}