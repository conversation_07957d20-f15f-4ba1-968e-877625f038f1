# API Gateway outputs
output "api_gateway_url" {
  description = "URL of the API Gateway"
  value       = aws_api_gateway_stage.main.invoke_url
}

output "api_gateway_id" {
  description = "ID of the API Gateway"
  value       = aws_api_gateway_rest_api.main.id
}

output "lambda_function_name" {
  description = "Name of the Lambda function"
  value       = aws_lambda_function.query_knowledge_base.function_name
}

output "lambda_function_arn" {
  description = "ARN of the Lambda function"
  value       = aws_lambda_function.query_knowledge_base.arn
}

# output "jwt_authorizer_function_name" {
#   description = "Name of the JWT Authorizer Lambda function"
#   value       = try(aws_lambda_function.jwt_authorizer.function_name, "Not deployed")
# }

# ECS and ALB outputs
output "alb_dns_name" {
  description = "DNS name of the Application Load Balancer"
  value       = aws_lb.main.dns_name
}

output "alb_zone_id" {
  description = "Zone ID of the Application Load Balancer"
  value       = aws_lb.main.zone_id
}

output "alb_arn" {
  description = "ARN of the Application Load Balancer"
  value       = aws_lb.main.arn
}

output "application_url" {
  description = "URL of the deployed application"
  value       = "https://${var.domain_name}"
}

# ACM Certificate outputs for cross-account DNS validation
output "acm_certificate_arn" {
  description = "ARN of the ACM certificate"
  value       = aws_acm_certificate.app_cert.arn
}

output "acm_domain_validation_options" {
  description = "Domain validation options for ACM certificate (use these to create DNS records in prod account)"
  value       = aws_acm_certificate.app_cert.domain_validation_options
  sensitive   = false
}

# SES domain verification outputs
output "ses_domain_identity_arn" {
  description = "ARN of the SES domain identity"
  value       = aws_ses_domain_identity.main.arn
}

output "ses_domain_verification_token" {
  description = "SES domain verification token (create TXT record in prod account)"
  value       = aws_ses_domain_identity.main.verification_token
}

output "ses_dkim_tokens" {
  description = "SES DKIM tokens (create CNAME records in prod account)"
  value       = aws_ses_domain_dkim.main.dkim_tokens
}

# ECR Repository
output "ecr_repository_url" {
  description = "URL of the ECR repository"
  value       = aws_ecr_repository.app.repository_url
}

# ECS Cluster
output "ecs_cluster_name" {
  description = "Name of the ECS cluster"
  value       = aws_ecs_cluster.main.name
}

output "ecs_service_name" {
  description = "Name of the ECS service"
  value       = aws_ecs_service.app.name
}

# DynamoDB Tables
output "dynamodb_table_names" {
  description = "Names of the DynamoDB tables"
  value = {
    auth                     = aws_dynamodb_table.next_auth.name
    allowlist               = aws_dynamodb_table.allowlist.name
    waitlist                = aws_dynamodb_table.waitlist.name
    study_design_sessions   = aws_dynamodb_table.study_design_sessions.name
  }
}

# Security
output "waf_web_acl_arn" {
  description = "ARN of the WAF Web ACL"
  value       = aws_wafv2_web_acl.main.arn
}

# GitHub Actions CI/CD outputs
output "github_actions_role_arn" {
  description = "ARN of the IAM role for GitHub Actions"
  value       = aws_iam_role.github_actions.arn
}

output "github_actions_role_name" {
  description = "Name of the IAM role for GitHub Actions"
  value       = aws_iam_role.github_actions.name
}

# Monitoring
output "cloudwatch_dashboard_url" {
  description = "URL of the CloudWatch dashboard"
  value       = "https://${var.aws_region}.console.aws.amazon.com/cloudwatch/home?region=${var.aws_region}#dashboards:name=${aws_cloudwatch_dashboard.main.dashboard_name}"
}

output "sns_alerts_topic_arn" {
  description = "ARN of the SNS alerts topic"
  value       = aws_sns_topic.alerts.arn
}

# Secrets Manager
output "secrets_manager_arns" {
  description = "ARNs of Secrets Manager secrets"
  value = {
    auth_secret   = aws_secretsmanager_secret.auth_secret.arn
    oauth_secrets = aws_secretsmanager_secret.oauth_secrets.arn
    email_secret  = aws_secretsmanager_secret.email_secret.arn
  }
}

# Route 53 Subdomain Delegation (if using subdomain delegation)
output "route53_name_servers" {
  description = "Name servers for the insights.trialynx.io hosted zone (add these as NS records in GoDaddy)"
  value       = try(aws_route53_zone.insights_subdomain.name_servers, [])
}

output "route53_zone_id_subdomain" {
  description = "Route 53 zone ID for insights.trialynx.io subdomain"
  value       = try(aws_route53_zone.insights_subdomain.zone_id, "")
}