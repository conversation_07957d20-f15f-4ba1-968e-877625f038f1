# Study Design Page Coverage Analysis

## Overview
Analysis of `/study/new/study-design/page.tsx` against the 17 critical questions from `statistics-must-have-questions.json`.

**Page Path:** `/apps/web/src/app/study/new/study-design/page.tsx`
**Critical Questions Source:** `critical-questions-json/statistics-must-have-questions.json`
**Total Questions to Cover:** 17

## Current Implementation Analysis

### Covered Questions (8/17 - 47% Coverage)

| Question ID | Question Text | Current Field | Coverage Status |
|-------------|---------------|---------------|-----------------|
| `intervention_model` | Select the Intervention Model(s) | `designType` (parallel/crossover/factorial) | ✅ **COMPLETE** |
| `has_active_comparator` | Does this study involve an active comparator? | *Inferred from controlType* | 🔸 **PARTIAL** |
| `control_methods` | Select the control method(s) | `controlType` (placebo/active-comparator/etc) | ✅ **COMPLETE** |
| `primary_objective` | Primary Objective | `primaryEndpoint` | ✅ **COMPLETE** |
| `secondary_objective` | Secondary Objective | `secondaryEndpoints[]` | ✅ **COMPLETE** |
| `study_design_descriptors` | Study Design Descriptors | `designDescriptors[]` + `blinding` | ✅ **COMPLETE** |
| `randomization_ratio` | Randomization Ratio | `randomizationRatio` | ✅ **COMPLETE** |
| `blinding_level` | Blinding Level | `blinding` (open-label/single/double/triple) | ✅ **COMPLETE** |

### Missing Questions (9/17 - 53% Missing)

| Question ID | Question Text | Priority | Implementation Complexity |
|-------------|---------------|----------|---------------------------|
| `analysis_populations` | Select the Analysis Population(s) | **HIGH** | Medium |
| `has_adaptive_novel_design` | Does the study include an adaptive or novel design? | **MEDIUM** | Low |
| `adaptive_novel_design_explanation` | Explain your adaptive and/or novel design | **MEDIUM** | Low |
| `number_of_study_arms` | Number of Study Arms | **HIGH** | Low |
| `study_arm_descriptions` | Study Arm Description/s | **HIGH** | Medium |
| `sample_size_determination_method` | Describe the method used to determine the sample size | **HIGH** | High |
| `primary_statistical_analysis` | Detail the statistical model, hypothesis, and method of analysis | **HIGH** | High |
| `has_tertiary_exploratory_objectives` | Does your study have any tertiary or exploratory objectives? | **MEDIUM** | Low |
| `exploratory_objectives` | Exploratory Objective(s) | **MEDIUM** | Low |
| `tertiary_exploratory_analyses` | Describe the analyses of tertiary/exploratory objectives | **LOW** | Medium |
| `interim_analysis_plan` | Describe the interim analysis plan | **MEDIUM** | High |

## Detailed Field Mapping

### ✅ Complete Coverage

**1. Study Design Framework**
- **Fields:** `designType`, `blinding`, `controlType`, `randomizationRatio`
- **Coverage:** Comprehensive design classification
- **UI:** Well-organized cards with clear options

**2. Objectives & Endpoints**
- **Fields:** `primaryEndpoint`, `secondaryEndpoints[]`
- **Features:** Dynamic secondary endpoint management
- **AI Integration:** Insights for endpoint suggestions

**3. Design Descriptors**
- **Fields:** `designDescriptors[]`
- **Implementation:** Tag-based descriptor system

### 🔴 Critical Missing Elements

**1. Analysis Populations (HIGH Priority)**
```tsx
// MISSING: ITT, Per-Protocol, mITT selection
const [analysisPopulations, setAnalysisPopulations] = useState([]);
// Need: Checkbox array for multiple population types
```

**2. Study Arms (HIGH Priority)**
```tsx
// MISSING: Number and description of study arms
const [numberOfStudyArms, setNumberOfStudyArms] = useState(2);
const [studyArmDescriptions, setStudyArmDescriptions] = useState([]);
// Need: Dynamic arm management interface
```

**3. Sample Size Methodology (HIGH Priority)**
```tsx
// MISSING: Statistical justification for sample size
const [sampleSizeMethod, setSampleSizeMethod] = useState("");
// Need: Detailed textarea with power analysis details
```

**4. Statistical Analysis Plan (HIGH Priority)**
```tsx
// MISSING: Primary statistical model and hypothesis
const [primaryStatisticalAnalysis, setPrimaryStatisticalAnalysis] = useState("");
// Need: Comprehensive statistical planning section
```

## Current Form Data Structure

```tsx
const [formData, setFormData] = useState({
  // Core design elements ✅
  phase: store.discovery.phase || null,
  primaryEndpoint: store.discovery.objectives?.primaryGoal || "",
  keyOutcomeMeasure: store.discovery.objectives?.keyOutcome || "",
  secondaryEndpoints: store.discovery.objectives?.secondaryGoals || [],
  studyDuration: store.discovery.objectives?.studyDuration || "",
  followUpPeriod: store.discovery.objectives?.followUpPeriod || "",
  
  // Design configuration ✅
  designType: store.discovery.design?.designType || "parallel",
  randomizationRatio: store.discovery.design?.randomizationRatio || "1:1",
  blinding: store.discovery.design?.blinding || "double-blind",
  controlType: store.discovery.design?.controlType || "placebo",
  designDescriptors: store.discovery.design?.designDescriptors || [],
});
```

## Recommended Implementation

### 1. Add Analysis Populations Section

```tsx
<Card>
  <CardHeader>
    <CardTitle className="flex items-center gap-2">
      <Users className="h-5 w-5" />
      Analysis Populations
    </CardTitle>
    <CardDescription>
      Define which participant populations will be included in the analysis
    </CardDescription>
  </CardHeader>
  <CardContent>
    <div className="space-y-3">
      <Label>Select Analysis Population(s)</Label>
      <div className="grid grid-cols-1 gap-3">
        {[
          { value: "itt", label: "Intent to Treat (ITT)", description: "All randomized participants" },
          { value: "per_protocol", label: "Per-Protocol Population (PPP)", description: "Participants who completed per protocol" },
          { value: "modified_itt", label: "Modified Intent to Treat (mITT)", description: "ITT with predefined criteria" }
        ].map(population => (
          <div key={population.value} className="flex items-start space-x-3 rounded-lg border p-3">
            <Checkbox
              id={population.value}
              checked={formData.analysisPopulations.includes(population.value)}
              onCheckedChange={(checked) => {
                if (checked) {
                  setFormData(prev => ({
                    ...prev,
                    analysisPopulations: [...prev.analysisPopulations, population.value]
                  }));
                } else {
                  setFormData(prev => ({
                    ...prev,
                    analysisPopulations: prev.analysisPopulations.filter(p => p !== population.value)
                  }));
                }
              }}
            />
            <div className="flex-1">
              <Label htmlFor={population.value} className="font-medium">
                {population.label}
              </Label>
              <p className="text-sm text-muted-foreground">{population.description}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  </CardContent>
</Card>
```

### 2. Add Study Arms Management

```tsx
<Card>
  <CardHeader>
    <CardTitle className="flex items-center gap-2">
      <Layers className="h-5 w-5" />
      Study Arms
    </CardTitle>
    <CardDescription>
      Define the number and description of study arms
    </CardDescription>
  </CardHeader>
  <CardContent className="space-y-4">
    <div className="space-y-2">
      <Label htmlFor="numberOfStudyArms">Number of Study Arms</Label>
      <Select
        value={formData.numberOfStudyArms.toString()}
        onValueChange={(value) => setFormData(prev => ({ 
          ...prev, 
          numberOfStudyArms: parseInt(value),
          studyArmDescriptions: Array(parseInt(value)).fill("").map((_, i) => 
            prev.studyArmDescriptions[i] || ""
          )
        }))}
      >
        <SelectTrigger id="numberOfStudyArms">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {[1,2,3,4,5,6,7,8,9,10].map(num => (
            <SelectItem key={num} value={num.toString()}>{num}</SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>

    <div className="space-y-3">
      <Label>Study Arm Descriptions</Label>
      {formData.studyArmDescriptions.map((description, index) => (
        <div key={index} className="space-y-2">
          <Label htmlFor={`arm-${index}`}>Study Arm #{index + 1}</Label>
          <Textarea
            id={`arm-${index}`}
            placeholder={`Describe study arm ${index + 1} (intervention, dose, administration, etc.)...`}
            value={description}
            onChange={(e) => {
              const newDescriptions = [...formData.studyArmDescriptions];
              newDescriptions[index] = e.target.value;
              setFormData(prev => ({ ...prev, studyArmDescriptions: newDescriptions }));
            }}
            rows={2}
          />
        </div>
      ))}
    </div>
  </CardContent>
</Card>
```

### 3. Add Statistical Analysis Section

```tsx
<Card>
  <CardHeader>
    <CardTitle className="flex items-center gap-2">
      <Calculator className="h-5 w-5" />
      Statistical Analysis Plan
    </CardTitle>
    <CardDescription>
      Define statistical methods and sample size justification
    </CardDescription>
  </CardHeader>
  <CardContent className="space-y-4">
    <div className="space-y-2">
      <Label htmlFor="sampleSizeMethod">Sample Size Determination Method</Label>
      <Textarea
        id="sampleSizeMethod"
        placeholder="Describe the method used to determine sample size, including power analysis assumptions, effect size justification, and dropout rate assumptions..."
        rows={6}
        value={formData.sampleSizeMethod}
        onChange={(e) => setFormData(prev => ({ ...prev, sampleSizeMethod: e.target.value }))}
      />
    </div>

    <div className="space-y-2">
      <Label htmlFor="primaryStatisticalAnalysis">Primary Statistical Analysis</Label>
      <Textarea
        id="primaryStatisticalAnalysis"
        placeholder="Detail the statistical model, hypothesis, and method of analysis to evaluate the primary objective. Include null/alternative hypotheses, statistical model, and estimand attributes..."
        rows={6}
        value={formData.primaryStatisticalAnalysis}
        onChange={(e) => setFormData(prev => ({ ...prev, primaryStatisticalAnalysis: e.target.value }))}
      />
    </div>
  </CardContent>
</Card>
```

### 4. Add Adaptive Design Section

```tsx
<Card>
  <CardHeader>
    <CardTitle className="flex items-center gap-2">
      <Zap className="h-5 w-5" />
      Adaptive & Novel Design Elements
    </CardTitle>
  </CardHeader>
  <CardContent className="space-y-4">
    <div className="flex items-center space-x-2">
      <Switch
        id="hasAdaptiveDesign"
        checked={formData.hasAdaptiveDesign}
        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, hasAdaptiveDesign: checked }))}
      />
      <Label htmlFor="hasAdaptiveDesign">
        Does the study include an adaptive or novel design?
      </Label>
    </div>

    {formData.hasAdaptiveDesign && (
      <div className="space-y-2">
        <Label htmlFor="adaptiveDesignExplanation">Adaptive Design Explanation</Label>
        <Textarea
          id="adaptiveDesignExplanation"
          placeholder="Describe the adaptive design aspects, how they will be operationalized, and justify the use of the novel design..."
          rows={4}
          value={formData.adaptiveDesignExplanation}
          onChange={(e) => setFormData(prev => ({ ...prev, adaptiveDesignExplanation: e.target.value }))}
        />
      </div>
    )}
  </CardContent>
</Card>
```

## Enhanced Form Data Structure

```tsx
const [formData, setFormData] = useState({
  // Existing fields...
  phase: store.discovery.phase || null,
  primaryEndpoint: store.discovery.objectives?.primaryGoal || "",
  secondaryEndpoints: store.discovery.objectives?.secondaryGoals || [],
  designType: store.discovery.design?.designType || "parallel",
  randomizationRatio: store.discovery.design?.randomizationRatio || "1:1",
  blinding: store.discovery.design?.blinding || "double-blind",
  controlType: store.discovery.design?.controlType || "placebo",
  designDescriptors: store.discovery.design?.designDescriptors || [],

  // NEW STATISTICAL FIELDS:
  analysisPopulations: store.discovery.design?.analysisPopulations || [],
  hasAdaptiveDesign: store.discovery.design?.hasAdaptiveDesign ?? false,
  adaptiveDesignExplanation: store.discovery.design?.adaptiveDesignExplanation || "",
  numberOfStudyArms: store.discovery.design?.numberOfStudyArms || 2,
  studyArmDescriptions: store.discovery.design?.studyArmDescriptions || ["", ""],
  sampleSizeMethod: store.discovery.design?.sampleSizeMethod || "",
  primaryStatisticalAnalysis: store.discovery.design?.primaryStatisticalAnalysis || "",
  hasTertiaryObjectives: store.discovery.design?.hasTertiaryObjectives ?? false,
  exploratoryObjectives: store.discovery.design?.exploratoryObjectives || "",
  tertiaryExploratoryAnalyses: store.discovery.design?.tertiaryExploratoryAnalyses || "",
  interimAnalysisPlan: store.discovery.design?.interimAnalysisPlan || "",
});
```

## Implementation Priority

### Phase 1 (Critical - Required for Statistical Validity)
1. **Analysis Populations** - Essential for defining analysis strategy
2. **Number of Study Arms** - Core study design element
3. **Study Arm Descriptions** - Required for protocol clarity
4. **Sample Size Methodology** - Regulatory requirement

### Phase 2 (Important - Statistical Rigor)
5. **Primary Statistical Analysis** - Detailed analysis plan
6. **Adaptive Design Support** - Growing trend in clinical trials
7. **Exploratory Objectives** - Comprehensive endpoint coverage

### Phase 3 (Nice-to-Have - Advanced Features)
8. **Interim Analysis Plan** - Complex study oversight
9. **Tertiary Exploratory Analyses** - Detailed statistical planning

## Conclusion

The Study Design page currently covers **47% (8/17)** of the critical statistical questions. While it has strong basic design coverage, it lacks critical statistical planning elements required for comprehensive protocol development.

**Key Strengths:**
- Excellent basic design framework (intervention model, blinding, controls)
- Good endpoint management with dynamic features
- Clean UI with AI insights integration

**Critical Gaps:**
- **Statistical analysis planning** - Missing analysis populations, statistical methods
- **Study arm management** - No systematic arm definition
- **Sample size justification** - No statistical power planning
- **Advanced design features** - No adaptive design support

**Recommendation:**
Add the statistical planning sections in phases to achieve comprehensive coverage while maintaining the current excellent user experience. The statistical planning elements are particularly important for regulatory submissions and study validity.