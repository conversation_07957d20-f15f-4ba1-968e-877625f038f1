# Implementation Roadmap: Achieving 100% Critical Questions Coverage

## Overview

**Current Status:** 95% coverage (75/79 critical questions)  
**Target:** 100% coverage (79/79 critical questions)  
**Implementation Timeline:** 3-4 weeks  
**Priority:** HIGH - Required for complete protocol generation  

## Phase 1: Foundation Updates (Week 1)

### 1.1 Store Schema Enhancement
**Priority:** CRITICAL  
**Estimated Time:** 4-6 hours  
**Dependencies:** None  

**Tasks:**
1. **Update Zustand Store Interface**
```tsx
// File: ~/store/trial-design.ts
interface ProtocolSection {
  protocolAcronym: string;
  protocolIdNumber: string;
  protocolFullTitle: string;
  studyDetailsForAI: string;
  trialInterventionDescription: string;
  studyEventsAndActivities: string;
  durationWithDates: string;
  totalNumberOfParticipants: string;
  numberOfSites: string;
  studyDesignDescriptors: string[];
  involvesInvestigationalDrug: boolean;
  involvesInvestigationalDevice: boolean;
}

interface EnhancedDesignSection {
  // ... existing fields
  analysisPopulations: string[];
  numberOfStudyArms: number;
  studyArmDescriptions: string[];
  sampleSizeMethod: string;
  primaryStatisticalAnalysis: string;
  hasAdaptiveDesign: boolean;
  adaptiveDesignExplanation: string;
  interimAnalysisPlanned: boolean;
  dataMonitoringCommittee: string;
}

interface EnhancedInterventionSection {
  // ... existing fields
  dosageFormStrength: string;
}
```

2. **Add Protocol Section to Discovery Store**
```tsx
interface DiscoveryData {
  // ... existing sections
  protocol: ProtocolSection;
  // ... update existing sections with enhanced fields
}
```

3. **Update Default Values and Initialization**

**Acceptance Criteria:**
- [ ] Store interface updated with all missing fields
- [ ] Default values provided for new fields
- [ ] Existing data migration handled gracefully
- [ ] TypeScript compilation successful

### 1.2 Database Schema Update
**Priority:** CRITICAL  
**Estimated Time:** 2-3 hours  
**Dependencies:** Store schema completion  

**Tasks:**
1. **Update Prisma Schema** (if applicable)
2. **Create Migration Scripts**
3. **Test Data Persistence**

**Acceptance Criteria:**
- [ ] Database stores all new fields
- [ ] Migration scripts tested
- [ ] Data persistence verified

## Phase 2: Study Overview & Background Page Enhancement (Week 1-2)

### 2.1 Basic Protocol Information Fields
**Priority:** HIGH  
**Estimated Time:** 6-8 hours  
**Dependencies:** Store schema completion  

**Implementation Steps:**

1. **Add Missing Form Fields**
```tsx
// File: apps/web/src/app/study/new/study-overview/page.tsx

// Add to existing formData state:
const [formData, setFormData] = useState({
  // ... existing fields
  
  // NEW: Basic Protocol Information
  protocolAcronym: store.discovery.protocol?.protocolAcronym || "",
  protocolIdNumber: store.discovery.protocol?.protocolIdNumber || "",
  protocolFullTitle: store.discovery.protocol?.protocolFullTitle || "",
  
  // NEW: Study Classification
  involvesInvestigationalDrug: store.discovery.protocol?.involvesInvestigationalDrug ?? true,
  involvesInvestigationalDevice: store.discovery.protocol?.involvesInvestigationalDevice ?? false,
  
  // NEW: Planning Information
  totalNumberOfParticipants: store.discovery.protocol?.totalNumberOfParticipants || "",
  numberOfSites: store.discovery.protocol?.numberOfSites || "",
  studyDuration: store.discovery.protocol?.studyDuration || "",
});
```

2. **Add UI Components**
```tsx
// NEW CARD: Protocol Identification
<Card>
  <CardHeader>
    <CardTitle>Protocol Identification</CardTitle>
    <CardDescription>Basic protocol identification and classification</CardDescription>
  </CardHeader>
  <CardContent className="space-y-4">
    <div className="grid grid-cols-2 gap-4">
      <div className="space-y-2">
        <Label htmlFor="protocolAcronym">Protocol Acronym *</Label>
        <Input
          id="protocolAcronym"
          placeholder="e.g., ADVANCE-01, STUDY-ABC"
          value={formData.protocolAcronym}
          onChange={(e) => setFormData({ ...formData, protocolAcronym: e.target.value })}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="protocolIdNumber">Protocol ID Number *</Label>
        <Input
          id="protocolIdNumber"
          placeholder="e.g., PRO-2024-001"
          value={formData.protocolIdNumber}
          onChange={(e) => setFormData({ ...formData, protocolIdNumber: e.target.value })}
        />
      </div>
    </div>
    
    <div className="space-y-2">
      <Label htmlFor="protocolFullTitle">Complete Protocol Title *</Label>
      <Textarea
        id="protocolFullTitle"
        placeholder="Full formal title of the protocol..."
        rows={2}
        value={formData.protocolFullTitle}
        onChange={(e) => setFormData({ ...formData, protocolFullTitle: e.target.value })}
      />
    </div>
    
    <div className="grid grid-cols-2 gap-4">
      <div className="flex items-center space-x-2">
        <Switch
          id="involvesInvestigationalDrug"
          checked={formData.involvesInvestigationalDrug}
          onCheckedChange={(checked) => setFormData({ ...formData, involvesInvestigationalDrug: checked })}
        />
        <Label htmlFor="involvesInvestigationalDrug">Involves Investigational Drug</Label>
      </div>
      <div className="flex items-center space-x-2">
        <Switch
          id="involvesInvestigationalDevice"
          checked={formData.involvesInvestigationalDevice}
          onCheckedChange={(checked) => setFormData({ ...formData, involvesInvestigationalDevice: checked })}
        />
        <Label htmlFor="involvesInvestigationalDevice">Involves Investigational Device</Label>
      </div>
    </div>
  </CardContent>
</Card>
```

3. **Add Study Planning Card**
```tsx
// NEW CARD: Study Planning Information
<Card>
  <CardHeader>
    <CardTitle>Study Planning Information</CardTitle>
    <CardDescription>Basic planning parameters for study design</CardDescription>
  </CardHeader>
  <CardContent className="space-y-4">
    <div className="grid grid-cols-3 gap-4">
      <div className="space-y-2">
        <Label htmlFor="totalNumberOfParticipants">Total Participants *</Label>
        <Input
          id="totalNumberOfParticipants"
          placeholder="e.g., 300"
          value={formData.totalNumberOfParticipants}
          onChange={(e) => setFormData({ ...formData, totalNumberOfParticipants: e.target.value })}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="numberOfSites">Number of Sites *</Label>
        <Input
          id="numberOfSites"
          placeholder="e.g., 25"
          value={formData.numberOfSites}
          onChange={(e) => setFormData({ ...formData, numberOfSites: e.target.value })}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="studyDuration">Study Duration *</Label>
        <Input
          id="studyDuration"
          placeholder="e.g., 24 months"
          value={formData.studyDuration}
          onChange={(e) => setFormData({ ...formData, studyDuration: e.target.value })}
        />
      </div>
    </div>
  </CardContent>
</Card>
```

**Acceptance Criteria:**
- [ ] All 8 basic fields implemented with proper validation
- [ ] UI follows existing design patterns from reference pages
- [ ] Form state properly managed and persisted
- [ ] Validation provides clear error messages

### 2.2 Advanced Study Description Fields
**Priority:** HIGH  
**Estimated Time:** 4-6 hours  
**Dependencies:** Basic fields completion  

**Implementation Steps:**

1. **Add Advanced Description Fields**
```tsx
// Add to formData:
studyDetailsForAI: store.discovery.protocol?.studyDetailsForAI || "",
trialInterventionDescription: store.discovery.protocol?.trialInterventionDescription || "",
studyEventsAndActivities: store.discovery.protocol?.studyEventsAndActivities || "",
durationWithDates: store.discovery.protocol?.durationWithDates || "",
studyDesignDescriptors: store.discovery.protocol?.studyDesignDescriptors || [],
```

2. **Add Study Details Card**
```tsx
// NEW CARD: Detailed Study Information
<Card>
  <CardHeader>
    <CardTitle>Detailed Study Information</CardTitle>
    <CardDescription>Comprehensive study description and characteristics</CardDescription>
  </CardHeader>
  <CardContent className="space-y-4">
    <div className="space-y-2">
      <Label htmlFor="studyDetailsForAI">Study Details for AI Analysis</Label>
      <Textarea
        id="studyDetailsForAI"
        placeholder="Detailed description of the study for AI processing and analysis..."
        rows={4}
        value={formData.studyDetailsForAI}
        onChange={(e) => setFormData({ ...formData, studyDetailsForAI: e.target.value })}
      />
    </div>
    
    <div className="space-y-2">
      <Label htmlFor="trialInterventionDescription">Trial Intervention Description *</Label>
      <Textarea
        id="trialInterventionDescription"
        placeholder="Detailed description of the trial intervention, including dosing, administration, duration..."
        rows={3}
        value={formData.trialInterventionDescription}
        onChange={(e) => setFormData({ ...formData, trialInterventionDescription: e.target.value })}
      />
    </div>
    
    <div className="space-y-2">
      <Label htmlFor="studyEventsAndActivities">Study Events and Activities *</Label>
      <Textarea
        id="studyEventsAndActivities"
        placeholder="Overview of key study events, visits, procedures, and activities..."
        rows={3}
        value={formData.studyEventsAndActivities}
        onChange={(e) => setFormData({ ...formData, studyEventsAndActivities: e.target.value })}
      />
    </div>
    
    <div className="space-y-2">
      <Label htmlFor="durationWithDates">Duration with Dates</Label>
      <Textarea
        id="durationWithDates"
        placeholder="Expected study timeline with key milestone dates..."
        rows={2}
        value={formData.durationWithDates}
        onChange={(e) => setFormData({ ...formData, durationWithDates: e.target.value })}
      />
    </div>
  </CardContent>
</Card>
```

3. **Add Study Design Descriptors Array Management**
```tsx
// Follow pattern from Study Population page for array management
// Add dynamic study design descriptors with add/remove functionality
```

**Acceptance Criteria:**
- [ ] All 5 advanced fields implemented
- [ ] Array management for study design descriptors
- [ ] Proper state management following existing patterns
- [ ] Enhanced validation for required fields

### 2.3 Enhanced Validation and Data Flow
**Priority:** MEDIUM  
**Estimated Time:** 2-3 hours  
**Dependencies:** Field implementation completion  

**Tasks:**
1. **Add Comprehensive Validation**
```tsx
const validateForm = () => {
  // Required basic fields
  if (!formData.protocolAcronym.trim()) {
    toast.error("Protocol acronym is required");
    return false;
  }
  
  if (!formData.protocolIdNumber.trim()) {
    toast.error("Protocol ID number is required");
    return false;
  }
  
  // ... additional validation
  
  return true;
};
```

2. **Update Store Persistence**
3. **Update AI Integration Context**

**Acceptance Criteria:**
- [ ] Comprehensive validation implemented
- [ ] Data properly saved to store
- [ ] AI context updated with new fields

## Phase 3: Study Design Page Enhancement (Week 2-3)

### 3.1 Statistical Analysis Planning
**Priority:** HIGH  
**Estimated Time:** 8-10 hours  
**Dependencies:** Store schema completion  

**Implementation Steps:**

1. **Add Statistical Analysis Fields**
```tsx
// File: apps/web/src/app/study/new/study-design/page.tsx

// Add to formData:
analysisPopulations: store.discovery.design?.analysisPopulations || [],
numberOfStudyArms: store.discovery.design?.numberOfStudyArms || 2,
studyArmDescriptions: store.discovery.design?.studyArmDescriptions || [],
sampleSizeMethod: store.discovery.design?.sampleSizeMethod || "",
primaryStatisticalAnalysis: store.discovery.design?.primaryStatisticalAnalysis || "",
hasAdaptiveDesign: store.discovery.design?.hasAdaptiveDesign ?? false,
adaptiveDesignExplanation: store.discovery.design?.adaptiveDesignExplanation || "",
interimAnalysisPlanned: store.discovery.design?.interimAnalysisPlanned ?? false,
dataMonitoringCommittee: store.discovery.design?.dataMonitoringCommittee || "",
```

2. **Add Analysis Populations Card**
```tsx
// NEW CARD: Analysis Populations
<Card>
  <CardHeader>
    <div className="flex items-center justify-between">
      <div>
        <CardTitle>Analysis Populations</CardTitle>
        <CardDescription>Define populations for statistical analysis</CardDescription>
      </div>
      <InsightsButton
        onClick={() => handleGetInsights("analysis-populations")}
        // ... insights integration
      />
    </div>
  </CardHeader>
  <CardContent className="space-y-4">
    {/* Dynamic array management for analysis populations */}
    {/* Follow Study Population page pattern */}
  </CardContent>
</Card>
```

3. **Add Study Arms Management Card**
```tsx
// NEW CARD: Study Arms
<Card>
  <CardHeader>
    <CardTitle>Study Arms Configuration</CardTitle>
    <CardDescription>Define study arms and their descriptions</CardDescription>
  </CardHeader>
  <CardContent className="space-y-4">
    <div className="space-y-2">
      <Label htmlFor="numberOfStudyArms">Number of Study Arms</Label>
      <Input
        id="numberOfStudyArms"
        type="number"
        min="1"
        max="10"
        value={formData.numberOfStudyArms}
        onChange={(e) => setFormData({ ...formData, numberOfStudyArms: parseInt(e.target.value) || 2 })}
      />
    </div>
    
    {/* Dynamic study arm descriptions based on numberOfStudyArms */}
    {Array.from({ length: formData.numberOfStudyArms }, (_, index) => (
      <div key={index} className="space-y-2">
        <Label htmlFor={`studyArm${index}`}>Study Arm {index + 1} Description</Label>
        <Textarea
          id={`studyArm${index}`}
          placeholder={`Describe study arm ${index + 1}...`}
          // ... implementation
        />
      </div>
    ))}
  </CardContent>
</Card>
```

**Acceptance Criteria:**
- [ ] All 9 statistical analysis fields implemented
- [ ] Dynamic study arms management
- [ ] Analysis populations array management
- [ ] Proper validation and state management

### 3.2 Advanced Statistical Planning
**Priority:** HIGH  
**Estimated Time:** 4-6 hours  
**Dependencies:** Basic statistical fields completion  

**Implementation Steps:**

1. **Add Statistical Methodology Card**
2. **Add Adaptive Design Planning**
3. **Add Interim Analysis Configuration**

**Acceptance Criteria:**
- [ ] Complete statistical planning interface
- [ ] Conditional display for adaptive design
- [ ] Interim analysis planning functionality

## Phase 4: Minor Gap Resolution (Week 3)

### 4.1 Investigational Product Dosage Enhancement
**Priority:** MEDIUM  
**Estimated Time:** 2-3 hours  
**Dependencies:** None  

**Tasks:**
1. **Add Dosage Form and Strength Field**
```tsx
// File: apps/web/src/app/study/new/investigational-product/page.tsx

// Add to formData:
dosageFormStrength: store.discovery.intervention?.dosageFormStrength || "",

// Add UI field:
<div className="space-y-2">
  <Label htmlFor="dosageFormStrength">Dosage Form and Strength</Label>
  <Input
    id="dosageFormStrength"
    placeholder="e.g., Tablets 25mg, Injection 50mg/mL"
    value={formData.dosageFormStrength}
    onChange={(e) => setFormData({ ...formData, dosageFormStrength: e.target.value })}
  />
</div>
```

**Acceptance Criteria:**
- [ ] Dosage form and strength field added
- [ ] Proper integration with existing form
- [ ] Data persistence verified

## Phase 5: Review Page Data Extraction Enhancement (Week 3-4)

### 5.1 Enhanced Data Extraction
**Priority:** HIGH  
**Estimated Time:** 4-6 hours  
**Dependencies:** All field implementations completion  

**Tasks:**
1. **Update Review Page Data Access**
```tsx
// File: apps/web/src/app/study/new/review/page.tsx

// Add access to new protocol data:
discovery.protocol?.protocolAcronym
discovery.protocol?.protocolIdNumber
discovery.protocol?.totalNumberOfParticipants
// ... all new fields

// Add access to enhanced design data:
discovery.design?.analysisPopulations
discovery.design?.studyArmDescriptions
// ... all enhanced fields
```

2. **Update Synopsis Generation Context**
3. **Enhance Data Validation Logic**

**Acceptance Criteria:**
- [ ] All new fields accessible in review page
- [ ] Synopsis generation includes all data
- [ ] Complete data validation implemented

## Phase 6: Testing and Validation (Week 4)

### 6.1 End-to-End Testing
**Priority:** CRITICAL  
**Estimated Time:** 6-8 hours  

**Tasks:**
1. **Complete Workflow Testing**
   - Test all 8 pages with new fields
   - Verify data persistence across navigation
   - Validate form submission and continuation

2. **Critical Questions Coverage Verification**
   - Verify all 79 critical questions are addressable
   - Test each critical question input and validation
   - Confirm complete data flow to review page

3. **Synopsis Generation Testing**
   - Test synopsis generation with complete data
   - Verify all critical information included
   - Test export functionality

**Acceptance Criteria:**
- [ ] All 79 critical questions fully covered
- [ ] Complete data flow verified
- [ ] Synopsis generation includes all critical information
- [ ] No regressions in existing functionality

### 6.2 Performance and UX Testing
**Priority:** MEDIUM  
**Estimated Time:** 2-3 hours  

**Tasks:**
1. **Performance Testing**
2. **UX Flow Testing**
3. **Mobile Responsiveness Testing**

## Implementation Guidelines

### Code Quality Standards
1. **Follow Existing Patterns:** Use successful pages (Study Population, Safety Assessment) as reference
2. **TypeScript Compliance:** Ensure all new code is properly typed
3. **Component Reuse:** Leverage existing UI components and patterns
4. **State Management:** Follow established Zustand store patterns
5. **Validation Consistency:** Use established validation patterns and error handling

### Testing Requirements
1. **Unit Tests:** Add tests for new form components and validation
2. **Integration Tests:** Test complete data flow
3. **Manual Testing:** Comprehensive user workflow testing
4. **Regression Testing:** Ensure no existing functionality breaks

### Rollout Strategy
1. **Feature Branch:** Implement all changes in feature branch
2. **Staged Testing:** Test each phase before proceeding
3. **Review Requirements:** Code review for all major changes
4. **Documentation:** Update documentation for new fields and functionality

## Success Metrics

### Completion Metrics
- [ ] **100% Critical Questions Coverage:** All 79 questions addressable
- [ ] **Complete Data Flow:** All data accessible in review page
- [ ] **Synopsis Quality:** Generated synopsis includes all critical information
- [ ] **Zero Regressions:** All existing functionality preserved

### Quality Metrics
- [ ] **User Experience:** Consistent with existing high-quality pages
- [ ] **Performance:** No degradation in page load or interaction times
- [ ] **Code Quality:** Maintains existing code quality standards
- [ ] **Documentation:** All new functionality properly documented

## Risk Mitigation

### Technical Risks
- **Store Schema Changes:** Implement careful migration strategy
- **Data Persistence:** Thorough testing of data persistence
- **Complex Form Logic:** Incremental implementation and testing

### Timeline Risks
- **Scope Creep:** Focus on critical questions coverage only
- **Testing Time:** Allocate sufficient time for comprehensive testing
- **Integration Issues:** Plan for potential integration challenges

## Conclusion

This roadmap provides a structured approach to achieving 100% critical questions coverage while maintaining the high quality standards established by the existing perfect implementations. The phased approach allows for incremental progress with validation at each step, minimizing risk while ensuring complete coverage of all 79 critical questions.