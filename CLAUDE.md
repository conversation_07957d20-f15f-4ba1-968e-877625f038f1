# Claude Memory File - Trialynx Insights Project

## Critical AWS Bedrock Knowledge Base Requirements

### Prompt Template Variables (REQUIRED)
When creating prompt templates for AWS Bedrock Knowledge Base, **BOTH** of these placeholder variables MUST be included:

1. `$search_results$` - Where Bedrock inserts the retrieved documents
2. `$output_format_instructions$` - Where <PERSON>rock inserts formatting instructions

**Example:**
```typescript
const promptTemplate = `You are an expert...

Study Context:
- Field: ${context.field}

Based on the retrieved documents below:

$search_results$

## YOUR INSTRUCTIONS HERE
Provide specific recommendations...

$output_format_instructions$`;
```

### Common Error Pattern
If you see <PERSON><PERSON> returning "Sorry, I am unable to assist you with this request" in citations despite generating a full response, check that BOTH placeholders are present in the prompt template. Missing `$output_format_instructions$` is a common cause of this error.

## Project Structure

### Key Files
- `/apps/lambda/src/utils/bedrock-kb-client.ts` - Contains all Bedrock prompt templates and response parsing
- `/apps/lambda/src/handlers/query-knowledge-base.ts` - Lambda handler for knowledge base queries
- `/apps/web/src/components/insights/InsightsPanel.tsx` - UI component for displaying insights
- `/apps/web/src/components/insights/DocumentViewer.tsx` - Component for viewing source documents

### Testing Commands
- `sam local start-api` - Run Lambda locally for testing
- `npm run dev` - Run the Next.js frontend

## JSON Prompt Creation Best Practices

### 1. Prompt Structure
When creating JSON-formatted prompts for Bedrock insights:

```typescript
'field-name': (context: any) => `You are a [specific expert role] analyzing [specific aspect].

Study Context:
- [Include ALL relevant context fields]
- Use ${context.field || 'default value'} pattern for safety

Based on the retrieved similar trials, provide recommendations in this EXACT JSON format:

{
  "primaryRecommendation": {
    "value": "The main recommendation",
    "rationale": "Why this is recommended",
    "range": { "min": "lower bound", "max": "upper bound" }
  },
  "alternatives": [
    {
      "value": "Alternative option",
      "rationale": "Why consider this",
      "measurementMethod": "How to implement"
    }
  ],
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier (format: NCTxxxxxxxx) - extract from retrieved documents",
      "details": "Study specifics"
    }
  ]
}

IMPORTANT:
1. Extract actual NCT numbers from retrieved documents
2. Be specific and quantitative
3. Base recommendations on retrieved studies
4. [Field-specific requirements]

$search_results$

$output_format_instructions$`
```

### 2. Parsing Function Pattern
Create a dedicated parsing function for each JSON field:

```typescript
private parse[FieldName]Response(text: string, citations: any[]): any[] {
  const sections = [];
  
  console.log('\n=== PARSING [FIELD] RESPONSE ===');
  console.log('Text length:', text.length);
  
  try {
    // Extract JSON from response
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in response');
    }
    
    const jsonData = JSON.parse(jsonMatch[0]);
    console.log('Successfully parsed JSON for [field]');
    
    // Create sections with actionableData
    sections.push({
      title: 'Section Title',
      content: `**Bold key info** with ${data.value}`,
      type: 'recommendation', // or 'information', 'details', etc.
      actionable: true,
      actionableData: {
        field: 'formFieldName',
        value: jsonData.value,
        // Include all data needed for applying
      },
      citations: [],
      confidence: 0.85,
    });
    
  } catch (error) {
    console.log('Failed to parse JSON, falling back to text parsing:', error);
    return this.parseResponseIntoSections(text, citations, field);
  }
  
  return sections;
}
```

### 3. NCT Number Handling
Always validate and clean NCT numbers:

```typescript
// Extract NCT numbers from citations for validation
const citationNCTNumbers = citations.flatMap(c => 
  c.references?.map(r => r.location?.s3Location?.uri?.match(/NCT\d+/i)?.[0])
).filter(Boolean);

// Validate NCT numbers from JSON response
const validNCT = /^NCT\d+$/i.test(study.nctNumber);
if (!validNCT && citationNCTNumbers[index]) {
  // Use citation NCT as fallback
  nctNumber = citationNCTNumbers[index];
}

// Create S3 URLs only for valid NCTs
if (validNCT) {
  url = `s3://trialynx-clinical-trials-gov/text-documents/${nctNumber.substring(0,6)}/${nctNumber}.txt`;
}
```

### 4. Frontend Integration Pattern

#### Add to parseFieldSpecificResponse:
```typescript
if (field === 'your-field') {
  return this.parseYourFieldResponse(text, citations);
}
```

#### Handle in population/study-design page:
```typescript
// Add to queries mapping
const queries: Record<string, string> = {
  'your-field': `Question for ${context.condition} in ${context.phase} trials?`,
};

// Add to handleApplySuggestion
if (field === "your-field") {
  if (actionableData && actionableData.field === 'targetField') {
    setFormData(prev => ({ ...prev, targetField: actionableData.value }));
    toast.success("Field updated");
  }
  return;
}
```

### 5. Context Passing Best Practices
Always pass comprehensive context:

```typescript
context: {
  studyType: store.discovery.studyType || "drug",
  condition: store.discovery.condition || "",
  phase: store.discovery.phase || "",
  primaryEndpoint: store.discovery.objectives?.primaryGoal || "",
  designType: store.discovery.design?.designType || "parallel",
  // Include all relevant fields for the specific insight
}
```

### 6. Actionable Data Structure
Design actionableData for easy application:

```typescript
actionableData: {
  field: 'formFieldName',  // Which form field to update
  value: processedValue,    // The actual value to apply
  // Additional fields as needed
  measurementMethod: data.method,  // For endpoints
  min: data.range.min,      // For ranges
  max: data.range.max,
}
```

## Common Patterns

### Citation Handling
- When NCT numbers are mentioned in text but no documents are retrieved, create synthetic citations
- Filter out error citations containing "sorry", "unable to assist", or "cannot help"
- Always check for both real and synthetic citations to ensure the UI displays referenced studies
- Validate NCT format before creating S3 URLs

### UI Best Practices
- Use glassmorphic backgrounds for overlays: `bg-white/95 dark:bg-gray-900/95`
- Collapsible sections should start collapsed for better overview
- Show counts/badges for items in collapsed sections
- Always provide visual feedback for clickable elements
- Display both bold recommendations and non-bold rationale for alternatives

### Response Section Types
Use consistent types for sections:
- `'recommendation'` - Primary actionable recommendations
- `'information'` - Supporting information
- `'details'` - Specific technical details
- `'calculation'` - Statistical/numerical calculations
- `'references'` - Benchmark studies and citations
- `'considerations'` - Important factors to consider
- `'alternatives'` - Alternative options
- `'rationale'` - Explanations and reasoning