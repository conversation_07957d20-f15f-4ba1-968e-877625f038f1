# TriaLynx Insights - User Flow & API Design

## User Journey: From Zero to Study Synopsis

### Phase 1: Initial Discovery Questions
**Goal:** Gather minimum viable information to query the knowledge base effectively

#### Screen 1: Study Type Selection
```
What are you studying?
○ Drug (pharmaceutical compound)
○ Device (medical device)
○ Behavioral intervention
○ Diagnostic procedure
○ Other intervention
```

#### Screen 2: Core Details (Dynamic based on type)
For **Drug Studies:**
- Drug name or compound (if known)
- Drug class/mechanism of action
- Is this a new compound or repurposing?

For **Device Studies:**
- Device name/type
- Device category (implantable, wearable, diagnostic, therapeutic)
- FDA device class (if known)

#### Screen 3: Study Phase & Target
```
Study Phase (if known):
○ Phase 1 (Safety/Dosing)
○ Phase 2 (Efficacy/Side Effects)  
○ Phase 3 (Comparative Effectiveness)
○ Phase 4 (Post-Market Surveillance)
○ Not sure yet

What condition/disease are you targeting?
[Text input with autocomplete from medical conditions]

Who is your target population?
- Age range: [Min] to [Max]
- Gender: ○ All ○ Male ○ Female
- Any specific population? (e.g., "elderly with diabetes", "pediatric cancer patients")
```

#### Screen 4: Study Objectives
```
What is your primary goal? (select one)
□ Establish safety and tolerability
□ Demonstrate efficacy vs placebo
□ Compare to existing treatment
□ Optimize dosing/administration
□ Long-term safety monitoring
□ Other: [text input]

What key outcome do you want to measure?
[Text input - e.g., "reduction in blood pressure", "tumor response rate"]
```

### Phase 2: Knowledge Base Query & Results
**User Action:** Click "Find Similar Studies"

#### Loading State
- Progress indicator: "Searching clinical trials database..."
- Sub-status updates:
  - "Analyzing your study parameters..."
  - "Finding similar trials..."
  - "Extracting insights..."

#### Results Presentation
```
Found 23 relevant studies from 2014-2024

Filters:
[✓] Completed studies (12)
[✓] Active/Recruiting (8)
[✓] Terminated/Withdrawn (3)
[✓] Same phase only
[✓] Similar population

Studies ranked by relevance:
┌──────────────────────────────────────┐
│ NCT04521234 (95% match)              │
│ "Efficacy of Drug X in Elderly HF"   │
│ Status: Completed | N=450            │
│ Key insights: Successful primary...   │
│ [View Details] [Include] [Exclude]   │
└──────────────────────────────────────┘
[Additional study cards...]
```

### Phase 3: Insights Generation
**System Action:** Analyze selected studies for patterns

#### Insights Dashboard
```
Based on 15 similar studies:

✅ Success Patterns:
• 73% of successful studies used crossover design
• Median enrollment: 200 participants (range: 50-450)
• Average study duration: 18 months
• Common inclusion criteria: [list]

⚠️ Risk Factors:
• Studies with <100 participants had 60% failure rate
• Lack of run-in period associated with higher dropout
• Single-site studies showed recruitment challenges

📊 Recommendations:
• Consider multi-site design (improves recruitment by 40%)
• Include 2-week washout period (reduces confounding)
• Target sample size: 180-220 based on similar studies
```

### Phase 4: Study Design Builder
**Interactive sections with AI assistance**

#### Section Navigation
- Study Overview ✓
- Design & Methodology [in progress]
- Study Population
- Interventions
- Endpoints & Outcomes
- Procedures & Assessments
- Operational Considerations
- Generate Synopsis

Each section provides:
- Pre-filled recommendations based on insights
- Editable fields
- Rationale tooltips ("Why this recommendation?")
- Alternative options dropdown

### Phase 5: Synopsis Generation
**Final output document with all sections**

---

## API Design

### 1. Initial Discovery Endpoints

```typescript
// tRPC Router: studyDesign.ts

// Start new study design session
createStudySession: {
  input: z.object({
    userId: z.string().optional(),
  }),
  output: z.object({
    sessionId: z.string(),
    expiresAt: z.date(),
  })
}

// Save discovery answers
saveDiscoveryData: {
  input: z.object({
    sessionId: z.string(),
    studyType: z.enum(['drug', 'device', 'behavioral', 'diagnostic', 'other']),
    phase: z.enum(['phase1', 'phase2', 'phase3', 'phase4', 'unknown']).optional(),
    intervention: z.object({
      name: z.string().optional(),
      category: z.string().optional(),
      mechanism: z.string().optional(),
    }),
    condition: z.string(),
    population: z.object({
      ageMin: z.number().optional(),
      ageMax: z.number().optional(),
      gender: z.enum(['all', 'male', 'female']),
      specific: z.string().optional(),
    }),
    objectives: z.object({
      primary: z.string(),
      keyOutcome: z.string(),
    }),
  }),
  output: z.object({
    success: z.boolean(),
    sessionId: z.string(),
  })
}
```

### 2. Knowledge Base Query

```typescript
// Lambda: query-knowledge-base-advanced.ts
interface KnowledgeBaseQuery {
  sessionId: string;
  query: {
    studyType: string;
    condition: string;
    intervention?: string;
    phase?: string;
    population?: PopulationCriteria;
  };
  filters?: {
    dateRange?: { start: Date; end: Date };
    status?: string[];
    minEnrollment?: number;
  };
  limit?: number;
}

// tRPC endpoint
findSimilarStudies: {
  input: KnowledgeBaseQuery,
  output: z.object({
    studies: z.array(StudyResult),
    totalFound: z.number(),
    queryMetadata: z.object({
      searchTerms: z.array(z.string()),
      filters: z.record(z.any()),
      executionTime: z.number(),
    }),
  })
}
```

### 3. Insights Generation

```typescript
// Lambda: generate-insights.ts
interface InsightsRequest {
  sessionId: string;
  selectedStudyIds: string[];
  focusAreas?: ('design' | 'population' | 'endpoints' | 'operational')[];
}

// tRPC endpoint
generateInsights: {
  input: InsightsRequest,
  output: z.object({
    successPatterns: z.array(z.object({
      category: z.string(),
      pattern: z.string(),
      frequency: z.number(),
      confidence: z.number(),
      examples: z.array(z.string()),
    })),
    riskFactors: z.array(z.object({
      factor: z.string(),
      impact: z.enum(['high', 'medium', 'low']),
      mitigation: z.string(),
      frequency: z.number(),
    })),
    recommendations: z.array(z.object({
      area: z.string(),
      recommendation: z.string(),
      rationale: z.string(),
      evidence: z.array(z.string()),
      alternatives: z.array(z.string()),
    })),
    statistics: z.object({
      medianEnrollment: z.number(),
      medianDuration: z.number(),
      successRate: z.number(),
      commonDesigns: z.array(z.string()),
    }),
  })
}
```

### 4. Study Design Builder

```typescript
// Session state management
interface StudyDesignState {
  sessionId: string;
  currentSection: string;
  completedSections: string[];
  designData: {
    overview: StudyOverview;
    methodology: StudyMethodology;
    population: StudyPopulation;
    interventions: StudyInterventions;
    endpoints: StudyEndpoints;
    procedures: StudyProcedures;
    operational: OperationalConsiderations;
  };
  insights: GeneratedInsights;
  lastSaved: Date;
}

// Auto-save endpoint
saveDesignSection: {
  input: z.object({
    sessionId: z.string(),
    section: z.string(),
    data: z.record(z.any()),
  }),
  output: z.object({
    saved: z.boolean(),
    timestamp: z.date(),
  })
}
```

### 5. Synopsis Generation

```typescript
// Lambda: generate-synopsis.ts
generateSynopsis: {
  input: z.object({
    sessionId: z.string(),
    format: z.enum(['draft', 'final']),
    sections: z.array(z.string()).optional(), // specific sections to include
  }),
  output: z.object({
    synopsis: z.object({
      title: z.string(),
      sections: z.array(z.object({
        name: z.string(),
        content: z.string(),
        confidence: z.number(),
        suggestions: z.array(z.string()).optional(),
      })),
      metadata: z.object({
        generatedAt: z.date(),
        version: z.string(),
        basedOnStudies: z.number(),
      }),
    }),
    exportUrl: z.string().optional(), // S3 presigned URL for download
  })
}
```

## State Management (Zustand)

```typescript
interface TrialDesignStore {
  // Session
  sessionId: string | null;
  
  // Discovery Phase
  discovery: {
    studyType: StudyType | null;
    phase: StudyPhase | null;
    intervention: InterventionDetails;
    condition: string;
    population: PopulationCriteria;
    objectives: StudyObjectives;
  };
  
  // Knowledge Base Results
  similarStudies: StudyResult[];
  selectedStudies: string[];
  
  // Insights
  insights: GeneratedInsights | null;
  
  // Design Builder
  design: StudyDesignState;
  
  // Synopsis
  synopsis: GeneratedSynopsis | null;
  
  // Actions
  startNewSession: () => Promise<void>;
  saveDiscovery: (data: Partial<Discovery>) => void;
  findSimilarStudies: () => Promise<void>;
  toggleStudySelection: (studyId: string) => void;
  generateInsights: () => Promise<void>;
  updateDesignSection: (section: string, data: any) => void;
  generateSynopsis: () => Promise<void>;
  exportSynopsis: (format: 'pdf' | 'word') => Promise<void>;
}
```

## UI Component Structure

```
app/
├── (public)/
│   └── page.tsx                 # Landing page with "Create New Study" CTA
├── (app)/
│   ├── study/
│   │   ├── new/
│   │   │   └── page.tsx        # Discovery wizard
│   │   ├── [sessionId]/
│   │   │   ├── page.tsx        # Redirect to current section
│   │   │   ├── discovery/
│   │   │   │   └── page.tsx    # Discovery questions
│   │   │   ├── similar/
│   │   │   │   └── page.tsx    # Similar studies results
│   │   │   ├── insights/
│   │   │   │   └── page.tsx    # Generated insights
│   │   │   ├── design/
│   │   │   │   └── page.tsx    # Design builder
│   │   │   └── synopsis/
│   │   │       └── page.tsx    # Final synopsis
│   │   └── layout.tsx           # Shared layout with progress tracker
│   └── layout.tsx               # App layout with navigation
└── components/
    ├── discovery/
    │   ├── StudyTypeSelector.tsx
    │   ├── InterventionDetails.tsx
    │   ├── PopulationCriteria.tsx
    │   └── ObjectivesForm.tsx
    ├── knowledge-base/
    │   ├── StudyCard.tsx
    │   ├── StudyFilters.tsx
    │   └── StudyDetails.tsx
    ├── insights/
    │   ├── SuccessPatterns.tsx
    │   ├── RiskFactors.tsx
    │   ├── Recommendations.tsx
    │   └── Statistics.tsx
    ├── design/
    │   ├── SectionNav.tsx
    │   ├── DesignForm.tsx
    │   └── RecommendationTooltip.tsx
    └── synopsis/
        ├── SynopsisViewer.tsx
        ├── SynopsisEditor.tsx
        └── ExportOptions.tsx
```

## Real-time Features

### Streaming Responses
- Use Server-Sent Events (SSE) for knowledge base search progress
- Stream insights as they're generated
- Progressive synopsis generation with section-by-section updates

### Auto-save
- Debounced auto-save every 30 seconds
- Save on section navigation
- Draft recovery on session restore

### Collaborative Features (Future)
- WebSocket connections for real-time collaboration
- Presence indicators
- Commenting system
- Version control for synopsis