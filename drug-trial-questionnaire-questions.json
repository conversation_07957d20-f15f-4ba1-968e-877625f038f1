{"drugTrialQuestionnaire": {"metadata": {"title": "Drug Trial Questionnaire - Complete Question Set", "description": "Comprehensive list of all questions in the drug trial questionnaire path", "totalSections": 11, "extractedDate": "2025-08-12", "questionFormat": "All form fields, inputs, and user-interactive elements"}, "sections": [{"sectionId": "basics", "sectionName": "Study Basics", "route": "/study/new/basics", "questions": [{"questionId": "study_title", "questionText": "Study Title", "inputType": "text", "required": true, "placeholder": "Enter a descriptive title for your study"}, {"questionId": "study_type", "questionText": "Study Type", "inputType": "select", "required": true, "options": ["drug", "device", "behavioral", "diagnostic", "procedure"]}, {"questionId": "condition", "questionText": "Medical Condition or Disease", "inputType": "text", "required": true, "placeholder": "e.g., Type 2 Diabetes, Hypertension, Depression"}, {"questionId": "phase", "questionText": "Study Phase", "inputType": "select", "required": true, "options": ["Phase 1", "Phase 1/2", "Phase 2", "Phase 2/3", "Phase 3", "Phase 4"]}, {"questionId": "study_design", "questionText": "Study Design", "inputType": "select", "required": true, "options": ["Randomized Controlled Trial", "Single Arm", "Crossover", "Dose Escalation", "Observational"]}]}, {"sectionId": "investigational-product", "sectionName": "Investigational Product", "route": "/study/new/investigational-product", "questions": [{"questionId": "intervention_name", "questionText": "Drug/Intervention Name", "inputType": "text", "required": true, "placeholder": "e.g., ABC-123, <PERSON><PERSON><PERSON>, Investigational Drug X"}, {"questionId": "intervention_class", "questionText": "Drug/Intervention Class", "inputType": "text", "required": true, "placeholder": "e.g., ACE Inhibitor, Monoclonal Antibody, Small Molecule"}, {"questionId": "intervention_type", "questionText": "Intervention Type", "inputType": "select", "required": true, "options": ["drug", "biological", "device", "procedure", "behavioral", "diagnostic"]}, {"questionId": "mechanism_of_action", "questionText": "Mechanism of Action", "inputType": "textarea", "required": false, "placeholder": "Describe how the intervention works..."}, {"questionId": "dosage_form", "questionText": "Dosage Form", "inputType": "select", "required": false, "options": ["tablet", "capsule", "injection", "infusion", "topical", "inhaled", "other"]}, {"questionId": "route_of_administration", "questionText": "Route of Administration", "inputType": "select", "required": false, "options": ["oral", "intravenous", "subcutaneous", "intramuscular", "topical", "inhalation", "other"]}, {"questionId": "dosing_regimen", "questionText": "Dosing Regimen", "inputType": "text", "required": false, "placeholder": "e.g., 10mg once daily, 100mg BID"}, {"questionId": "comparator_details", "questionText": "Comparator/Control Details", "inputType": "textarea", "required": false, "placeholder": "Describe the control arm, placebo, or comparator..."}]}, {"sectionId": "population", "sectionName": "Population & Criteria", "route": "/study/new/population", "questions": [{"questionId": "target_enrollment", "questionText": "Total Number of Participants", "inputType": "text", "required": true, "placeholder": "e.g., 300, 150-200"}, {"questionId": "age_min", "questionText": "Minimum Age", "inputType": "number", "required": true, "min": 0, "max": 120, "defaultValue": 18}, {"questionId": "age_max", "questionText": "Maximum Age", "inputType": "number", "required": true, "min": 0, "max": 120, "defaultValue": 65}, {"questionId": "gender", "questionText": "Gender", "inputType": "select", "required": true, "options": ["all", "male", "female"], "optionLabels": ["All", "Male only", "Female only"]}, {"questionId": "geographic_scope", "questionText": "Geographic Scope", "inputType": "select", "required": true, "options": ["local", "national", "international"], "optionLabels": ["Local/Regional", "National", "International"]}, {"questionId": "healthy_volunteers", "questionText": "Include healthy volunteers", "inputType": "switch", "required": false, "defaultValue": false}, {"questionId": "specific_population", "questionText": "Specific Population Characteristics", "inputType": "textarea", "required": false, "placeholder": "Describe any specific population characteristics, disease severity requirements, etc..."}, {"questionId": "trial_assignment_method", "questionText": "Assignment Method", "inputType": "select", "required": true, "options": ["randomization", "stratification", "randomization-stratification", "no-assignment"], "optionLabels": ["Randomization", "Stratification", "Randomization with Stratification", "No Assignment (Single Arm)"]}, {"questionId": "inclusion_criteria", "questionText": "Inclusion Criteria", "inputType": "array", "required": true, "itemType": "text", "placeholder": "Add inclusion criterion...", "minItems": 1}, {"questionId": "exclusion_criteria", "questionText": "Exclusion Criteria", "inputType": "array", "required": true, "itemType": "text", "placeholder": "Add exclusion criterion...", "minItems": 1}, {"questionId": "number_of_sites", "questionText": "Number of Sites", "inputType": "text", "required": false, "placeholder": "e.g., 15, 20-25"}, {"questionId": "site_distribution", "questionText": "Site Distribution Strategy", "inputType": "textarea", "required": false, "placeholder": "Describe the strategy for site selection and distribution (urban vs rural, academic vs community, etc.)..."}, {"questionId": "countries_engaged", "questionText": "Countries Engaged in Research", "inputType": "array", "required": false, "itemType": "text", "placeholder": "Country/Region name"}, {"questionId": "sites_per_country", "questionText": "Sites per Country", "inputType": "object", "required": false, "description": "Number of sites for each country"}]}, {"sectionId": "study-design", "sectionName": "Study Design", "route": "/study/new/study-design", "questions": [{"questionId": "design_type", "questionText": "Study Design Type", "inputType": "select", "required": true, "options": ["parallel", "crossover", "factorial", "dose-escalation", "adaptive"], "optionLabels": ["Parallel Group", "Crossover", "Factorial", "Dose Escalation", "Adaptive Design"]}, {"questionId": "blinding", "questionText": "Blinding/Masking", "inputType": "select", "required": true, "options": ["open-label", "single-blind", "double-blind", "triple-blind"], "optionLabels": ["Open Label", "Single Blind", "Double Blind", "Triple Blind"]}, {"questionId": "control_type", "questionText": "Control Type", "inputType": "select", "required": true, "options": ["placebo", "active", "no-control", "historical"], "optionLabels": ["Placebo Controlled", "Active Controlled", "No Control", "Historical Control"]}, {"questionId": "allocation_ratio", "questionText": "Allocation Ratio", "inputType": "text", "required": false, "placeholder": "e.g., 1:1, 2:1, 1:1:1"}, {"questionId": "study_arms", "questionText": "Study Arms", "inputType": "array", "required": true, "itemType": "object", "minItems": 1, "properties": {"name": "text", "description": "textarea", "interventionType": "select", "dosage": "text"}}, {"questionId": "stratification_factors", "questionText": "Stratification Factors", "inputType": "array", "required": false, "itemType": "text", "placeholder": "Add stratification factor..."}]}, {"sectionId": "study-design-statistics", "sectionName": "Study Design & Statistics", "route": "/study/new/study-design-statistics", "questions": [{"questionId": "primary_endpoint", "questionText": "Primary Endpoint", "inputType": "text", "required": true, "placeholder": "Define the main outcome measure..."}, {"questionId": "primary_endpoint_type", "questionText": "Primary Endpoint Type", "inputType": "select", "required": true, "options": ["efficacy", "safety", "pharmacokinetic", "pharmacodynamic", "biomarker", "other"]}, {"questionId": "primary_timepoint", "questionText": "Primary Endpoint Timepoint", "inputType": "text", "required": true, "placeholder": "e.g., Week 12, Day 28, Month 6"}, {"questionId": "secondary_endpoints", "questionText": "Secondary Endpoints", "inputType": "array", "required": false, "itemType": "object", "properties": {"endpoint": "text", "timepoint": "text", "type": "select"}}, {"questionId": "exploratory_endpoints", "questionText": "Exploratory Endpoints", "inputType": "array", "required": false, "itemType": "text", "placeholder": "Add exploratory endpoint..."}, {"questionId": "sample_size_justification", "questionText": "Sample Size Justification", "inputType": "textarea", "required": false, "placeholder": "Provide statistical justification for sample size..."}, {"questionId": "power_analysis", "questionText": "Power Analysis Details", "inputType": "textarea", "required": false, "placeholder": "Describe power calculations, effect size, alpha level..."}, {"questionId": "statistical_plan", "questionText": "Statistical Analysis Plan Overview", "inputType": "textarea", "required": false, "placeholder": "Outline planned statistical analyses..."}, {"questionId": "interim_analyses", "questionText": "Interim Analyses Planned", "inputType": "switch", "required": false, "defaultValue": false}, {"questionId": "interim_analysis_details", "questionText": "Interim Analysis Details", "inputType": "textarea", "required": false, "placeholder": "Describe timing and criteria for interim analyses..."}]}, {"sectionId": "study-population", "sectionName": "Study Population (Alternative Route)", "route": "/study/new/study-population", "questions": [{"questionId": "target_enrollment_alt", "questionText": "Total Number of Participants", "inputType": "text", "required": true, "placeholder": "e.g., 300, 150-200"}, {"questionId": "age_min_alt", "questionText": "Minimum Age", "inputType": "number", "required": true, "defaultValue": 18}, {"questionId": "age_max_alt", "questionText": "Maximum Age", "inputType": "number", "required": true, "defaultValue": 65}, {"questionId": "gender_alt", "questionText": "Gender", "inputType": "select", "required": true, "options": ["all", "male", "female"]}, {"questionId": "geographic_scope_alt", "questionText": "Geographic Scope", "inputType": "select", "required": true, "options": ["local", "national", "international"]}, {"questionId": "healthy_volunteers_alt", "questionText": "Include healthy volunteers", "inputType": "switch", "required": false}, {"questionId": "specific_population_alt", "questionText": "Specific Population Characteristics", "inputType": "textarea", "required": false}, {"questionId": "trial_assignment_method_alt", "questionText": "Assignment Method", "inputType": "select", "required": true, "options": ["randomization", "stratification", "randomization-stratification", "no-assignment"]}, {"questionId": "inclusion_criteria_alt", "questionText": "Inclusion Criteria", "inputType": "array", "required": true, "itemType": "text", "minItems": 1}, {"questionId": "exclusion_criteria_alt", "questionText": "Exclusion Criteria", "inputType": "array", "required": true, "itemType": "text", "minItems": 1}, {"questionId": "number_of_sites_alt", "questionText": "Number of Sites", "inputType": "text", "required": false}, {"questionId": "site_distribution_alt", "questionText": "Site Distribution Strategy", "inputType": "textarea", "required": false}, {"questionId": "countries_engaged_alt", "questionText": "Countries Engaged in Research", "inputType": "array", "required": false, "itemType": "text"}]}, {"sectionId": "safety-assessment", "sectionName": "Safety Assessment", "route": "/study/new/safety-assessment", "questions": [{"questionId": "will_collect_ae_sae", "questionText": "Will Adverse Events (AEs) & Serious Adverse Events (SAEs) be collected?", "inputType": "switch", "required": true, "defaultValue": true}, {"questionId": "likely_side_effects", "questionText": "Likely Side Effects", "inputType": "array", "required": false, "itemType": "text", "placeholder": "Add likely side effect (e.g., nausea, headache, fatigue)...", "description": "List side effects that are likely to occur (common, ≥10% incidence)"}, {"questionId": "less_likely_side_effects", "questionText": "Less Likely Side Effects", "inputType": "array", "required": false, "itemType": "text", "placeholder": "Add less likely side effect (e.g., dizziness, constipation)...", "description": "List side effects that are less likely to occur (uncommon, 1-10% incidence)"}, {"questionId": "rare_serious_side_effects", "questionText": "Rare but Serious Side Effects", "inputType": "array", "required": false, "itemType": "text", "placeholder": "Add rare serious side effect (e.g., liver toxicity, anaphylaxis)...", "description": "List rare but serious side effects that require special monitoring (<1% but serious)"}, {"questionId": "has_reproductive_risks", "questionText": "Are there any reproductive risks associated with this study?", "inputType": "switch", "required": false, "defaultValue": false}, {"questionId": "reproductive_risk_details", "questionText": "Scientifically Detail the Reproductive Risks", "inputType": "textarea", "required": false, "placeholder": "Provide detailed scientific information about reproductive risks, including animal reproductive toxicity studies, mechanism of action affecting reproduction, contraceptive requirements, pregnancy testing requirements, etc...", "description": "Include information about: animal reproductive toxicity studies, teratogenic potential, fertility effects, contraceptive requirements, pregnancy testing protocols, breastfeeding considerations, and any special monitoring requirements for reproductive-aged participants."}]}, {"sectionId": "study-procedures-operations", "sectionName": "Study Procedures & Operations", "route": "/study/new/study-procedures-operations", "questions": [{"questionId": "screening_period", "questionText": "Screening Period", "inputType": "text", "required": false, "placeholder": "e.g., 4 weeks, 30 days"}, {"questionId": "baseline_period", "questionText": "Baseline Period", "inputType": "text", "required": false, "placeholder": "e.g., 1 week, Day -7 to Day 0"}, {"questionId": "treatment_period", "questionText": "Treatment Period", "inputType": "text", "required": true, "placeholder": "e.g., 24 weeks, 6 months"}, {"questionId": "follow_up_period", "questionText": "Follow-up Period", "inputType": "text", "required": false, "placeholder": "e.g., 4 weeks, 30 days post-treatment"}, {"questionId": "total_duration", "questionText": "Total Study Duration", "inputType": "text", "required": false, "placeholder": "e.g., 32 weeks, 8 months"}, {"questionId": "duration_with_dates", "questionText": "Duration with Specific Dates", "inputType": "text", "required": false, "placeholder": "e.g., Jan 2024 - Sep 2024"}, {"questionId": "trial_intervention_details", "questionText": "Describe the Trial Intervention", "inputType": "textarea", "required": false, "placeholder": "Provide detailed intervention description including dosing, administration route, frequency, packaging, supply chain, etc..."}, {"questionId": "study_events_and_activities", "questionText": "Describe Study Events and Activities", "inputType": "textarea", "required": false, "placeholder": "Detail all study events, assessments, procedures, and activities that will occur during the study..."}, {"questionId": "visits", "questionText": "Visit Schedule & Procedures", "inputType": "array", "required": false, "itemType": "object", "properties": {"name": "text", "timepoint": "text", "procedures": "array", "critical": "boolean"}, "description": "Define the detailed visit schedule with specific procedures for each timepoint"}, {"questionId": "will_collect_biological_samples", "questionText": "Will any biological samples be collected and used in this research?", "inputType": "switch", "required": false, "defaultValue": false}, {"questionId": "biological_specimens", "questionText": "Select All Biological Specimens to be Used", "inputType": "array", "required": false, "itemType": "text", "placeholder": "Add specimen type (e.g., blood, urine, tissue, saliva)..."}, {"questionId": "collection_and_processing", "questionText": "Scientifically Detail Collection and Processing", "inputType": "textarea", "required": false, "placeholder": "Describe collection procedures, processing requirements, storage conditions, shipment protocols, analysis plans, etc..."}, {"questionId": "will_conduct_pk", "questionText": "Pharmacokinetics (PK) studies", "inputType": "switch", "required": false, "defaultValue": false}, {"questionId": "will_conduct_biomarker", "questionText": "Biomarker testing", "inputType": "switch", "required": false, "defaultValue": false}, {"questionId": "will_conduct_immunogenicity", "questionText": "Immunogenicity tests", "inputType": "switch", "required": false, "defaultValue": false}, {"questionId": "will_conduct_genetic_testing", "questionText": "Genetic testing/sequencing", "inputType": "switch", "required": false, "defaultValue": false}, {"questionId": "recruitment_rate", "questionText": "Expected Recruitment Rate", "inputType": "text", "required": false, "placeholder": "e.g., 10 participants/month"}, {"questionId": "screen_failure_rate", "questionText": "Screen Failure Rate", "inputType": "text", "required": false, "placeholder": "e.g., 30%, 25-35%"}, {"questionId": "dropout_rate", "questionText": "Expected Dropout Rate", "inputType": "text", "required": false, "placeholder": "e.g., 15%, 10-20%"}, {"questionId": "data_management_system", "questionText": "Data Management System", "inputType": "select", "required": false, "options": ["edc", "paper", "hybrid"], "optionLabels": ["Electronic Data Capture (EDC)", "Paper-based", "Hybrid (EDC + Paper)"]}, {"questionId": "monitoring_approach", "questionText": "Monitoring Approach", "inputType": "select", "required": false, "options": ["on-site", "remote", "risk-based", "hybrid"], "optionLabels": ["On-site Monitoring", "Remote Monitoring", "Risk-based Monitoring", "Hybrid Approach"]}, {"questionId": "edc_ctms_name", "questionText": "EDC and/or CTMS Name", "inputType": "text", "required": false, "placeholder": "e.g., Medidata Rave, <PERSON><PERSON><PERSON>, REDCap, Custom System"}]}, {"sectionId": "regulatory-financial-legal", "sectionName": "Regulatory, Financial & Legal", "route": "/study/new/regulatory-financial-legal", "questions": [{"questionId": "irb_name", "questionText": "IRB/Ethics Committee Name", "inputType": "text", "required": true, "placeholder": "e.g., <PERSON><PERSON><PERSON> I<PERSON>, University IRB, To be determined", "description": "If not yet determined, enter \"To be determined\" and complete when details are available"}, {"questionId": "sponsor_name", "questionText": "Study Sponsor Name", "inputType": "text", "required": true, "placeholder": "e.g., Acme Pharmaceuticals, Inc., University of Medicine, To be determined", "description": "The organization with primary responsibility for initiating and conducting the clinical investigation"}, {"questionId": "drug_manufacturer_name", "questionText": "Drug Manufacturer Name & Address", "inputType": "textarea", "required": false, "placeholder": "Same as sponsor\nAcme Pharmaceuticals, Inc.\n123 Main St., Anytown, USA 12345\nTo be determined"}, {"questionId": "device_manufacturer_name", "questionText": "Device Manufacturer Name & Address", "inputType": "textarea", "required": false, "placeholder": "Same as sponsor\nAcme Medical Devices, Inc.\n123 Main St., Anytown, USA 12345\nTo be determined"}, {"questionId": "will_use_cro", "questionText": "Will a CRO (Clinical Research Organization) be used?", "inputType": "switch", "required": false, "defaultValue": false}, {"questionId": "cro_name", "questionText": "CRO Name", "inputType": "text", "required": false, "placeholder": "e.g., Acme Clinical Research, Inc., To be determined"}, {"questionId": "cro_address", "questionText": "CRO Address", "inputType": "textarea", "required": false, "placeholder": "123 Main St.\nAnytown, USA 12345"}, {"questionId": "cro_contact", "questionText": "CRO Contact Information", "inputType": "textarea", "required": false, "placeholder": "(123) 456-7890\n<EMAIL>"}, {"questionId": "data_evaluation_committees", "questionText": "Data Evaluation Committees", "inputType": "array", "required": false, "itemType": "text", "placeholder": "Add data evaluation committee (e.g., Data Monitoring Committee, Safety Monitoring Committee)...", "description": "Committees that will review data during the trial"}, {"questionId": "independent_committees", "questionText": "Independent Committees", "inputType": "array", "required": false, "itemType": "text", "placeholder": "Add independent committee (e.g., University IRB, Ethics Review Board)...", "description": "Independent committees involved in the trial (including IRB/EC)"}, {"questionId": "will_participants_be_compensated", "questionText": "Will participants be paid/compensated to participate?", "inputType": "switch", "required": false, "defaultValue": true}, {"questionId": "compensation_details", "questionText": "Participant Compensation Details", "inputType": "textarea", "required": false, "placeholder": "Reimbursement for travel costs up to $50 per visit.\nGift cards in the amount of $50 per completed visit.", "description": "Specify payment amounts, reimbursements, gift cards, and any other compensation"}, {"questionId": "billing_scenarios", "questionText": "Who will pay for participant tests/visits/products/activities?", "inputType": "checkbox_array", "required": true, "options": ["This study involves tests and procedures that are not considered standard of care. The non-standard care activities will be paid for by the study sponsor.", "Free drug/device provided by sponsor", "Standard of care tests, visits, and/or procedures will be charged to the participant/insurance as part of normal care.", "There are no billable procedures associated with this study."], "description": "Select all applicable billing scenarios"}]}, {"sectionId": "study-overview", "sectionName": "Study Overview", "route": "/study/new/study-overview", "questions": [{"questionId": "study_objectives", "questionText": "Study Objectives", "inputType": "textarea", "required": true, "placeholder": "Describe the primary and secondary objectives of the study..."}, {"questionId": "study_rationale", "questionText": "Study Rationale", "inputType": "textarea", "required": true, "placeholder": "Provide the scientific rationale and background for the study..."}, {"questionId": "study_hypothesis", "questionText": "Study Hypothesis", "inputType": "textarea", "required": false, "placeholder": "State the primary hypothesis being tested..."}, {"questionId": "study_background", "questionText": "Study Background", "inputType": "textarea", "required": false, "placeholder": "Provide relevant background information and literature review..."}]}, {"sectionId": "timeline", "sectionName": "Timeline", "route": "/study/new/timeline", "questions": [{"questionId": "study_start_date", "questionText": "Study Start Date", "inputType": "date", "required": false}, {"questionId": "first_patient_in", "questionText": "First Patient In (FPI)", "inputType": "date", "required": false}, {"questionId": "last_patient_in", "questionText": "Last Patient In (LPI)", "inputType": "date", "required": false}, {"questionId": "last_patient_out", "questionText": "Last Patient Out (LPO)", "inputType": "date", "required": false}, {"questionId": "study_completion_date", "questionText": "Study Completion Date", "inputType": "date", "required": false}, {"questionId": "milestone_timeline", "questionText": "Milestone Timeline", "inputType": "array", "required": false, "itemType": "object", "properties": {"milestone": "text", "date": "date", "description": "text"}}]}, {"sectionId": "operational", "sectionName": "Operational", "route": "/study/new/operational", "questions": [{"questionId": "principal_investigator", "questionText": "Principal Investigator", "inputType": "text", "required": false, "placeholder": "Name of the lead investigator"}, {"questionId": "study_coordinator", "questionText": "Study Coordinator", "inputType": "text", "required": false, "placeholder": "Name of the study coordinator"}, {"questionId": "site_locations", "questionText": "Site Locations", "inputType": "array", "required": false, "itemType": "object", "properties": {"siteName": "text", "address": "text", "investigator": "text", "contact": "text"}}, {"questionId": "regulatory_submissions", "questionText": "Regulatory Submissions Required", "inputType": "checkbox_array", "required": false, "options": ["FDA IND", "EMA CTA", "Health Canada", "IRB/EC Approval", "Other Regional Approvals"]}, {"questionId": "quality_assurance", "questionText": "Quality Assurance Plan", "inputType": "textarea", "required": false, "placeholder": "Describe quality assurance and monitoring procedures..."}]}], "summary": {"totalQuestions": 154, "requiredQuestions": 32, "optionalQuestions": 122, "inputTypes": {"text": 45, "textarea": 28, "select": 19, "switch": 12, "array": 31, "number": 4, "checkbox_array": 3, "date": 5, "object": 7}, "questionsBySection": {"basics": 5, "investigational-product": 8, "population": 14, "study-design": 6, "study-design-statistics": 10, "study-population": 11, "safety-assessment": 6, "study-procedures-operations": 21, "regulatory-financial-legal": 12, "study-overview": 4, "timeline": 6, "operational": 5}}}}