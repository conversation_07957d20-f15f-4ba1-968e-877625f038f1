# GitHub Actions CI/CD Setup

This directory contains GitHub Actions workflows for automated deployment of the Trialynx Insights application.

## Workflows

### 1. `deploy-dev.yml`
Automatically deploys to the development environment when code is pushed or merged to the `release/next` branch.

**Triggers:**
- Push to `release/next` branch
- Pull request to `release/next` branch

**What it does:**
- Builds Docker image
- Pushes to AWS ECR
- Deploys to ECS Fargate
- Runs health checks
- Reports deployment status

### 2. `test.yml`
Runs tests, linting, and type checking on all pushes and pull requests.

**Triggers:**
- Push to `main` or `release/next` branches
- Pull requests to `main` or `release/next` branches

**What it does:**
- Installs dependencies
- Runs TypeScript type checking
- Runs ESLint
- Builds the application

## Setup Instructions

### 1. GitHub Repository Secrets

You need to configure the following secret in your GitHub repository:

1. Go to your GitHub repository
2. Navigate to **Settings > Secrets and variables > Actions**
3. Click **New repository secret**
4. Add the following secret:

| Secret Name | Value | Description |
|-------------|-------|-------------|
| `AWS_ROLE_TO_ASSUME` | `arn:aws:iam::030430646103:role/trialynx-insights-dev-github-actions-role` | The IAM role ARN for GitHub Actions |

### 2. GitHub Environment (Optional but Recommended)

For additional security, create a `development` environment:

1. Go to **Settings > Environments**
2. Click **New environment**
3. Name it `development`
4. Configure protection rules as needed (e.g., required reviewers)

### 3. Repository Settings

Ensure your repository has the correct settings:

- **Repository name:** `ezresearchsolutions/trialynx-insights`
- **Branch protection:** Consider protecting the `main` and `release/next` branches

## How It Works

### OIDC Authentication
The workflows use OpenID Connect (OIDC) to securely authenticate with AWS without storing long-lived credentials. The IAM role `trialynx-insights-dev-github-actions-role` is configured to trust GitHub Actions from your specific repository and branch.

### Deployment Process
1. Code is pushed to `release/next`
2. GitHub Actions triggers the deployment workflow
3. The workflow assumes the AWS IAM role using OIDC
4. Docker image is built and pushed to ECR
5. ECS service is updated with the new image
6. Health checks verify the deployment
7. Deployment status is reported

### Security
- No AWS credentials are stored in GitHub
- IAM role has minimal permissions (ECR, ECS, CloudWatch)
- OIDC trust relationship restricts access to specific repository and branch
- All actions are logged and auditable

## Troubleshooting

### Common Issues

**1. "Role cannot be assumed" error**
- Verify the `AWS_ROLE_TO_ASSUME` secret is correctly set
- Check that the repository name matches exactly in the IAM role trust policy

**2. "Permission denied" errors**
- Review IAM permissions in the GitHub Actions policy
- Ensure the role has necessary permissions for ECR and ECS operations

**3. Deployment health check failures**
- Check ECS service logs in CloudWatch
- Verify application is starting correctly
- Check ALB target group health

### Viewing Logs
- **GitHub Actions logs:** Available in the Actions tab of your repository
- **Application logs:** CloudWatch logs at `/ecs/trialynx-insights-dev`
- **ECS deployment events:** AWS Console > ECS > Services

## Manual Deployment

If you need to deploy manually, you can use the helper script:

```bash
# Build and deploy
./scripts/deploy-helpers.sh build
./scripts/deploy-helpers.sh deploy

# Check status
./scripts/deploy-helpers.sh status
./scripts/deploy-helpers.sh logs
```

## Further Reading

- [GitHub Actions OIDC with AWS](https://docs.github.com/en/actions/deployment/security-hardening-your-deployments/configuring-openid-connect-in-amazon-web-services)
- [AWS ECS Deployment Strategies](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/deployment-types.html)
- [Docker Multi-stage Builds](https://docs.docker.com/develop/dev-best-practices/)