name: Deploy to Development

on:
  push:
    branches: [ release/next, hy/add-action ]

env:
  AWS_REGION: us-west-2
  ECR_REPOSITORY: trialynx-insights-dev-app
  ECS_SERVICE: trialynx-insights-dev-service
  ECS_CLUSTER: trialynx-insights-dev-cluster
  CONTAINER_NAME: app

permissions:
  contents: read
  id-token: write # This is required for requesting the JWT

jobs:
  deploy:
    name: Deploy to AWS
    runs-on: ubuntu-latest
    environment: development

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: ${{ secrets.AWS_ROLE_TO_ASSUME }}
        aws-region: ${{ env.AWS_REGION }}
        role-session-name: GitHub-Actions-Deploy

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Build, tag, and push image to Amazon ECR
      id: build-image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: ${{ github.sha }}
      run: |
        # Build a docker container and push it to ECR so that it can
        # be deployed to ECS.
        cd apps/web
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:latest .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest
        echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

    - name: Force new deployment
      run: |
        aws ecs update-service --cluster $ECS_CLUSTER --service $ECS_SERVICE --force-new-deployment

    - name: Wait for deployment to complete
      run: |
        aws ecs wait services-stable --cluster $ECS_CLUSTER --services $ECS_SERVICE

    - name: Test deployment
      run: |
        # Get ALB URL from ECS service
        ALB_URL=$(aws elbv2 describe-load-balancers --names trialynx-insights-dev-alb --query 'LoadBalancers[0].DNSName' --output text)
        
        # Test health endpoint
        echo "Testing health endpoint at http://$ALB_URL/api/health"
        
        # Wait a moment for the service to be ready
        sleep 30
        
        # Test the health endpoint
        curl -f "http://$ALB_URL/api/health" || exit 1
        
        echo "✅ Deployment successful and health check passed!"

    - name: Notify deployment status
      if: always()
      run: |
        if [ "${{ job.status }}" == "success" ]; then
          echo "🎉 Deployment to development environment was successful!"
        else
          echo "❌ Deployment to development environment failed!"
        fi