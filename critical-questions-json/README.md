# Critical Questions JSON Conversion Summary

## Overview
This directory contains 10 JSON files converted from the original text documents in the `critical-questions/` folder. Each JSON file maintains the complete structure and content from the source documents while providing a standardized, machine-readable format for integration into the application.

## Converted Files

### 1. AEs-SAEs-must-have-questions.json
- **Category**: Safety Assessment
- **Questions**: 1
- **Focus**: Adverse Events (AEs) and Serious Adverse Events (SAEs) collection

### 2. biological-samples-must-have-questions.json
- **Category**: Study Procedures & Operations
- **Questions**: 7
- **Focus**: Biological sample collection, processing, PK studies, genetic testing, biomarker testing, immunogenicity

### 3. data-management-must-have-questions.json
- **Category**: Study Procedures & Operations
- **Questions**: 1
- **Focus**: Electronic Data Capture (EDC) and Clinical Trial Management Systems (CTMS)

### 4. financial-and-legal-must-have-questions.json
- **Category**: Regulatory, Financial & Legal
- **Questions**: 3
- **Focus**: Participant compensation, billing scenarios, and payment responsibilities

### 5. investigational-product-must-have-questions.json
- **Category**: Investigational Product
- **Questions**: 12
- **Focus**: Comprehensive drug/device details including regulatory status, composition, preclinical/clinical studies, safety data

### 6. participants-must-have-questions.json
- **Category**: Study Population
- **Questions**: 3
- **Focus**: Inclusion/exclusion criteria and trial assignment methods

### 7. risk-benefits-must-have-questions.json
- **Category**: Safety Assessment
- **Questions**: 5
- **Focus**: Side effects categorization (likely, less likely, rare/serious) and reproductive risks

### 8. statistics-must-have-questions.json
- **Category**: Study Design & Statistics
- **Questions**: 17
- **Focus**: Statistical design, analysis methods, endpoints, objectives, sample size determination

### 9. study-essentials-must-have-questions.json
- **Category**: Study Overview & Essential Information
- **Questions**: 22
- **Focus**: Core study information including protocols, titles, design descriptors, regulatory details

### 10. team-collaborators-must-have-questions.json
- **Category**: Regulatory, Financial & Legal
- **Questions**: 8
- **Focus**: Study team, organizations, committees, and collaborative entities

## Total Summary
- **Total Questions**: 79
- **Total Categories**: 5 unique categories
- **Conversion Date**: 2025-08-15

## JSON Structure
Each JSON file follows a consistent structure:
```json
{
  "documentName": "original-filename",
  "category": "Primary Category",
  "description": "Brief description of content",
  "extractedDate": "2025-08-15",
  "questions": [
    {
      "id": "unique_identifier",
      "questionText": "The actual question",
      "directions": "Instructions for answering",
      "example": "Sample answer (when provided)",
      "aiInstructions": "AI-specific guidance",
      "inputType": "UI component type",
      "required": boolean,
      "options": [], // for select/checkbox inputs
      "dependsOn": "field_id", // for conditional questions
      "dependsOnValue": "trigger_value"
    }
  ]
}
```

## Input Types Used
- `text`: Single-line text input
- `textarea`: Multi-line text input
- `select`: Dropdown selection
- `checkbox_array`: Multiple checkbox options
- `array`: Dynamic list of items
- `number`: Numeric input

## Next Steps
1. **Mapping Analysis**: Compare these questions with the existing `drug-trial-questionnaire-questions.json` to identify:
   - Questions already covered in the current UI
   - New questions requiring UI implementation
   - Potential consolidation opportunities

2. **Category Integration**: Align the 5 categories with the current 8-step workflow:
   - Study Overview & Background
   - Investigational Product
   - Study Design
   - Study Population
   - Safety Assessment
   - Study Procedures & Operations
   - Regulatory, Financial & Legal
   - Review & Synopsis

3. **UI Implementation**: Use the structured JSON to generate form components and integrate with the existing workflow.

## Benefits of JSON Format
- **Standardized Structure**: Consistent format across all question sets
- **Machine Readable**: Easy integration with React components and form libraries
- **Preserves Original Content**: All directions, examples, and AI instructions maintained
- **Extensible**: Easy to add new fields or modify existing ones
- **Validation Ready**: Structure supports form validation and conditional logic