import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { BedrockKnowledgeBaseClient } from '../utils/bedrock-kb-client';
import { AuthValidator } from '../utils/auth';
import type { BedrockQueryRequest } from '../types/index';

const bedrockClient = new BedrockKnowledgeBaseClient();
const authValidator = new AuthValidator();

export const handler = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
      body: '',
    };
  }

  try {
    // Skip auth validation for local development
    const isLocal = process.env.AWS_SAM_LOCAL === 'true';
    let authContext = { userId: 'local-user', email: '<EMAIL>', roles: ['researcher'] };
    
    if (!isLocal) {
      // Validate authentication in production only
      authContext = await authValidator.validateRequest(event);
    }
    
    // Parse request body
    if (!event.body) {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*'
        },
        body: JSON.stringify({
          error: 'Request body is required'
        })
      };
    }
    
    const request: BedrockQueryRequest = JSON.parse(event.body);
    
    // Validate request
    if (!request.query || typeof request.query !== 'string') {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*'
        },
        body: JSON.stringify({
          error: 'Query parameter is required'
        })
      };
    }
    
    // Query the knowledge base with field context
    const response = await bedrockClient.query({
      ...request,
      field: request.field || 'default',
    });
    
    // Log the query for analytics (in production, this would go to CloudWatch/analytics service)
    console.log({
      userId: authContext.userId,
      query: request.query,
      field: request.field,
      hasResults: !!response.results || !!response.sections,
      metadata: response.metadata
    });
    
    // Log sections being returned for debugging
    if (response.sections) {
      console.log('\n=== RETURNING SECTIONS TO FRONTEND ===');
      console.log('Total sections:', response.sections.length);
      response.sections.forEach((section, idx) => {
        console.log(`Section ${idx + 1}:`, {
          title: section.title,
          type: section.type,
          actionable: section.actionable,
          hasEndpoints: !!section.endpoints,
          endpointsCount: section.endpoints?.length || 0
        });
      });
      console.log('=======================================\n');
    }
    
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*',
        'Access-Control-Allow-Credentials': 'true'
      },
      body: JSON.stringify(response)
    };
    
  } catch (error) {
    console.error('Error handling request:', error);
    
    // Determine appropriate error response
    if (error instanceof Error) {
      if (error.message.includes('authorization')) {
        return {
          statusCode: 401,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*'
          },
          body: JSON.stringify({
            error: 'Unauthorized'
          })
        };
      }
    }
    
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || '*'
      },
      body: JSON.stringify({
        error: 'Internal server error'
      })
    };
  }
};