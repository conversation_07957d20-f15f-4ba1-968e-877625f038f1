#!/bin/bash

# Test script for local Lambda Knowledge Base integration

echo "Testing Knowledge Base Query..."

curl -X POST http://localhost:3001/query \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What phase should I use for a novel SGLT-2 inhibitor for type 2 diabetes?",
    "context": {
      "studyType": "drug",
      "condition": "Type 2 Diabetes Mellitus",
      "drugClass": "SGLT-2 inhibitor",
      "isNewCompound": true,
      "mechanism": "Inhibits sodium-glucose cotransporter 2 in the proximal renal tubules"
    },
    "field": "phase",
    "retrieveOnly": false
  }' | jq '.'

echo ""
echo "Test complete. Check the response above."