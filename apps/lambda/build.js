import { build } from 'esbuild';
import { readdirSync } from 'fs';
import { join } from 'path';

const handlersDir = './src/handlers';
const outDir = './dist';

// Get all handler files
const handlers = readdirSync(handlersDir)
  .filter(file => file.endsWith('.ts'))
  .map(file => file.replace('.ts', ''));

// Build each handler
for (const handler of handlers) {
  await build({
    entryPoints: [`${handlersDir}/${handler}.ts`],
    bundle: true,
    outfile: `${outDir}/${handler}.js`,
    platform: 'node',
    target: 'node18',  // Lambda uses Node 18
    format: 'cjs',      // Changed to CommonJS for Lambda
    sourcemap: true,
    minify: true,
    // Don't mark AWS SDK v3 as external since it's not included in Lambda runtime
    external: [],
  });
  
  console.log(`Built: ${handler}.js`);
}