# TriaLynx Lambda Functions

AWS Lambda functions for the TriaLynx Insights platform, providing serverless API endpoints for Bedrock Knowledge Base integration.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- AWS SAM CLI
- AWS CLI configured with appropriate credentials
- Access to AWS Bedrock Knowledge Base

### Setup

1. **Install dependencies:**
```bash
npm install
```

2. **Create environment configuration:**

Create an `env.json` file with your AWS configuration:

```json
{
  "QueryKnowledgeBaseFunction": {
    "BEDROCK_KNOWLEDGE_BASE_ID": "your-knowledge-base-id",
    "BEDROCK_MODEL_ID": "us.anthropic.claude-sonnet-4-20250514-v1:0",
    "S3_BUCKET_NAME": "trialynx-clinical-trials-gov",
    "AWS_REGION": "us-west-2"
  },
  "GetDocumentFunction": {
    "S3_BUCKET_NAME": "trialynx-clinical-trials-gov",
    "AWS_REGION": "us-west-2"
  }
}
```

**For local testing without AWS Bedrock:** Set `BEDROCK_KNOWLEDGE_BASE_ID` to `"SKIP_FOR_LOCAL_TESTING"` to use mock data.

3. **Build the functions:**
```bash
npm run build
sam build
```

4. **Start local API:**
```bash
sam local start-api --port 3001 --profile your-aws-profile --env-vars env.json
```

## 📁 Project Structure

```
lambda/
├── src/
│   ├── handlers/
│   │   ├── query-knowledge-base.ts   # Main insights query endpoint
│   │   ├── get-document.ts           # Document retrieval endpoint
│   │   └── jwt-authorizer.ts         # JWT authorization handler
│   └── utils/
│       ├── bedrock-kb-client.ts      # Bedrock Knowledge Base client
│       ├── auth.ts                   # Authentication utilities
│       └── response-formatter.ts     # Response formatting utilities
├── dist/                              # Compiled JavaScript output
├── env.json                          # Environment variables (create this)
├── template.yaml                     # SAM template
└── package.json
```

## 🔧 Available Endpoints

### POST /query
Query the Bedrock Knowledge Base for insights.

**Request Body:**
```json
{
  "query": "What are typical inclusion criteria for Phase 2 cancer trials?",
  "field": "inclusion-criteria",
  "context": {
    "condition": "cancer",
    "phase": "Phase 2",
    "studyType": "drug"
  }
}
```

**Response:**
```json
{
  "text": "Generated insights text",
  "sections": [
    {
      "title": "Primary Recommendation",
      "content": "Recommendation content",
      "type": "recommendation",
      "actionable": true,
      "actionableData": {},
      "citations": [],
      "confidence": 0.85
    }
  ],
  "sources": [],
  "citations": [],
  "queryTime": 1234
}
```

### GET /document/{nctId}
Retrieve a specific clinical trial document from S3.

**Response:** Returns the document content or a signed URL for access.

## 🛠️ Development

### Building

```bash
# Build TypeScript to JavaScript
npm run build

# Build SAM application
sam build
```

### Testing

```bash
# Run unit tests
npm test

# Test specific function locally
sam local invoke QueryKnowledgeBaseFunction --event events/test-query.json
```

### Adding New Prompts

Edit `src/utils/bedrock-kb-client.ts` and add your prompt template to the `PROMPT_TEMPLATES` object:

```typescript
'your-field': (context: any) => `You are an expert...

Study Context:
- Condition: ${context.condition}

Based on the retrieved trials, provide recommendations...

$search_results$

$output_format_instructions$`
```

**Important:** Always include both `$search_results$` and `$output_format_instructions$` placeholders.

## 🚢 Deployment

### Deploy to AWS

```bash
# First-time deployment
sam deploy --guided

# Subsequent deployments
sam deploy

# Deploy with specific profile
sam deploy --profile your-aws-profile
```

### Update Production Environment

After deployment, update your web application's `.env` with the API Gateway URL from the SAM outputs.

## 🔑 Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `BEDROCK_KNOWLEDGE_BASE_ID` | AWS Bedrock Knowledge Base ID | ✅ |
| `BEDROCK_MODEL_ID` | Bedrock model to use (e.g., Claude Sonnet) | ✅ |
| `S3_BUCKET_NAME` | S3 bucket containing trial documents | ✅ |
| `AWS_REGION` | AWS region for services | ✅ |
| `CORS_ORIGIN` | Allowed CORS origin (default: *) | ❌ |

## 🐛 Troubleshooting

### "Knowledge Base with id XXX does not exist"

This means the Knowledge Base ID is invalid or your AWS credentials don't have access.

**Solutions:**
1. Verify the Knowledge Base ID in AWS Bedrock console
2. Check your AWS profile has the necessary permissions
3. For local testing, set `BEDROCK_KNOWLEDGE_BASE_ID` to `"SKIP_FOR_LOCAL_TESTING"`

### Build Errors

If you encounter build errors:
```bash
# Clean and rebuild
rm -rf .aws-sam dist
npm run build
sam build --use-container
```

### Permission Errors

Ensure your AWS profile has these permissions:
- `bedrock:RetrieveAndGenerate`
- `bedrock:Retrieve`
- `s3:GetObject` (for the trials bucket)

## 📚 Resources

- [AWS SAM Documentation](https://docs.aws.amazon.com/serverless-application-model/)
- [AWS Bedrock API Reference](https://docs.aws.amazon.com/bedrock/latest/APIReference/)
- [Lambda TypeScript Guide](https://docs.aws.amazon.com/lambda/latest/dg/typescript-handler.html)