{"name": "web", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "dev": "next dev --turbo", "lint": "next lint", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/dynamodb-adapter": "^2.10.0", "@aws-sdk/client-dynamodb": "^3.868.0", "@aws-sdk/lib-dynamodb": "^3.868.0", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@t3-oss/env-nextjs": "^0.12.0", "@tailwindcss/postcss": "^4.0.15", "@tanstack/react-query": "^5.69.0", "@tanstack/react-table": "^8.21.3", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.0.0", "@trpc/server": "^11.0.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "lucide-react": "^0.537.0", "motion": "^12.23.12", "next": "^15.2.3", "next-auth": "^5.0.0-beta.29", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.62.0", "server-only": "^0.0.1", "sonner": "^2.0.7", "superjson": "^2.2.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.0.15", "zod": "^3.24.2", "zustand": "^5.0.7"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^20.14.10", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "eslint": "^8.57.1", "eslint-config-next": "^15.4.6", "typescript": "^5.8.2"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "npm@10.9.2"}