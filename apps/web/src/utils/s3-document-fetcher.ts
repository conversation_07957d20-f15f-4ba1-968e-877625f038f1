// Utility to fetch and parse S3 documents

export async function fetchS3Document(url: string): Promise<{
  nctId: string;
  title: string;
  content: string;
  parsed: any;
}> {
  try {
    // Convert S3 console URL to direct S3 URL if needed
    let fetchUrl = url;
    if (url.includes('console.aws.amazon.com')) {
      // Extract bucket and key from console URL
      const match = url.match(/object\/([^?]+)\?.*prefix=(.+)/);
      if (match && match[1] && match[2]) {
        const bucket = match[1];
        const key = decodeURIComponent(match[2]);
        fetchUrl = `https://${bucket}.s3.${process.env.NEXT_PUBLIC_AWS_REGION || 'us-west-2'}.amazonaws.com/${key}`;
      }
    }
    
    const response = await fetch(fetchUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch document: ${response.statusText}`);
    }
    
    const content = await response.text();
    const parsed = parseTrialDocument(content);
    
    return {
      nctId: parsed.nctId || extractNCTId(url),
      title: parsed.title || 'Clinical Trial Document',
      content,
      parsed,
    };
  } catch (error) {
    console.error('Error fetching S3 document:', error);
    throw error;
  }
}

function extractNCTId(url: string): string {
  const match = url.match(/NCT\d+/);
  return match ? match[0] : 'Unknown';
}

function parseTrialDocument(content: string): any {
  const lines = content.split('\n');
  const parsed: any = {
    sections: [],
  };
  
  // Extract key fields
  for (const line of lines) {
    if (line.includes('Clinical Trial:')) {
      parsed.nctId = line.split(':')[1]?.trim();
    } else if (line.includes('Title:') && !parsed.title) {
      parsed.title = line.split(':').slice(1).join(':').trim();
    } else if (line.includes('Official Title:')) {
      parsed.officialTitle = line.split(':').slice(1).join(':').trim();
    } else if (line.includes('Status:')) {
      parsed.status = line.split(':')[1]?.trim();
    } else if (line.includes('Phase:')) {
      parsed.phase = line.split(':')[1]?.trim();
    } else if (line.includes('Enrollment:')) {
      const enrollmentMatch = line.match(/(\d+)/);
      if (enrollmentMatch && enrollmentMatch[1]) {
        parsed.enrollment = parseInt(enrollmentMatch[1]);
      }
    }
  }
  
  // Parse sections
  let currentSection: any = null;
  const sectionHeaders = [
    'OVERVIEW',
    'STUDY DETAILS',
    'DESCRIPTION',
    'CONDITIONS AND KEYWORDS',
    'INTERVENTIONS',
    'ELIGIBILITY',
    'OUTCOMES',
    'STUDY DESIGN',
    'ARMS AND GROUPS',
    'SPONSOR AND COLLABORATORS',
  ];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Check if this is a section header
    const isHeader = sectionHeaders.some(header => 
      line?.toUpperCase().includes(header)
    );
    
    if (isHeader && line) {
      if (currentSection) {
        parsed.sections.push(currentSection);
      }
      currentSection = {
        title: line.replace(/[-=]/g, '').trim(),
        content: [],
      };
    } else if (currentSection && line?.trim()) {
      currentSection.content.push(line);
    }
  }
  
  if (currentSection) {
    parsed.sections.push(currentSection);
  }
  
  // Process sections content
  parsed.sections = parsed.sections.map((section: any) => ({
    ...section,
    content: section.content.join('\n').trim(),
  }));
  
  return parsed;
}