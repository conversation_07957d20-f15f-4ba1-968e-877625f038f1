// Import all types from Lambda
import type {
  StudyType,
  StudyPhase,
  StudyStatus,
  Gender,
  InterventionDetails,
  PopulationCriteria,
  StudyObjectives,
  DiscoveryData as BaseDiscoveryData,
  StudyResult as BaseStudyResult,
  OutcomeMeasure,
  StudyDesign,
  StudyResults,
  ParticipantFlow,
  AdverseEvents,
  BedrockQueryRequest,
  BedrockQueryResponse,
  StudyProtocol,
  StudyDesignDetails,
  StatisticalAnalysis,
  SafetyAssessment,
  LaboratoryStudies,
  StudyTimeline,
  OperationalDetails,
  RegulatoryFinancialLegal,
} from "@/../../lambda/src/types";

// Re-export for convenience
export type {
  StudyType,
  StudyPhase,
  StudyStatus,
  Gender,
  InterventionDetails,
  PopulationCriteria,
  StudyObjectives,
  OutcomeMeasure,
  StudyDesign,
  StudyResults,
  ParticipantFlow,
  AdverseEvents,
  BedrockQueryRequest,
  BedrockQueryResponse,
  StudyProtocol,
  StudyDesignDetails,
  StatisticalAnalysis,
  SafetyAssessment,
  LaboratoryStudies,
  StudyTimeline,
  OperationalDetails,
  RegulatoryFinancialLegal,
};

// Extend with proper types
export type DiscoveryData = BaseDiscoveryData;
export type StudyResult = BaseStudyResult;

// Additional frontend-specific types
export interface Citation {
  id: string;
  title: string;
  url: string;
  relevance: number;
}

export interface SourceDocument {
  nctId: string;
  status: string;
  studyType: string;
  s3Uri: string;
  excerpt?: string;
  score?: number;
}

export interface InsightSection {
  title: string;
  content: string;
  type?: 'recommendation' | 'information' | 'details' | 'rationale' | 'alternatives' | 'calculation' | 'references' | 'considerations' | 'overview' | 'efficacy-endpoints' | 'safety-endpoints' | 'qol-endpoints' | 'exploratory-endpoints';
  actionable?: boolean;
  citations: Citation[];
  confidence?: number;
  endpoints?: Array<{
    title: string;
    content: string;
    actionable: boolean;
  }>;
  actionableData?: {
    field: string;
    value: any;
    [key: string]: any;
  };
  alternatives?: Array<{
    endpoint: string;
    rationale?: string;
    measurementMethod?: string;
    actionableData?: {
      field: string;
      value: any;
      [key: string]: any;
    };
  }>;
}

export interface Pattern {
  id: string;
  category: "design" | "population" | "intervention" | "endpoints" | "operational";
  description: string;
  frequency: number;
  confidence: number;
  examples: string[];
  impact?: "positive" | "negative" | "neutral";
}

export interface RiskFactor {
  id: string;
  factor: string;
  category: string;
  impact: "high" | "medium" | "low";
  frequency: number;
  mitigation: string;
  examples: string[];
}

export interface Recommendation {
  id: string;
  area: string;
  recommendation: string;
  rationale: string;
  evidence: string[];
  confidence: number;
  alternatives: string[];
  priority: "critical" | "important" | "optional";
}

export interface StudyStatistics {
  medianEnrollment: number;
  enrollmentRange: { min: number; max: number; q1: number; q3: number };
  medianDurationMonths: number;
  durationRange: { min: number; max: number };
  successRate: number;
  completionRate: number;
  commonDesigns: Array<{ design: string; frequency: number }>;
  commonEndpoints: Array<{ endpoint: string; frequency: number }>;
  dropoutRate: { median: number; range: { min: number; max: number } };
}

export interface GeneratedInsights {
  sessionId: string;
  basedOnStudies: string[];
  generatedAt: Date;
  successPatterns: Pattern[];
  riskFactors: RiskFactor[];
  recommendations: Recommendation[];
  statistics: StudyStatistics;
  keyTakeaways: string[];
}

export interface StudyDesignSession {
  id: string;
  userId?: string;
  status: "discovery" | "analyzing" | "designing" | "reviewing" | "complete";
  currentStep: string;
  completedSteps: string[];
  createdAt: Date;
  updatedAt: Date;
  expiresAt: Date;
  
  discovery: DiscoveryData;
  knowledgeBaseResults?: {
    studies: StudyResult[];
    selectedStudies: string[];
    queryMetadata: any;
  };
  insights?: GeneratedInsights;
  design?: {
    overview?: any;
    methodology?: any;
    population?: any;
    interventions?: any;
    endpoints?: any;
    procedures?: any;
    operational?: any;
  };
  synopsis?: any;
}