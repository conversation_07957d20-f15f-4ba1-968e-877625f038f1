import type { StudyResult, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, RiskFactor, Recommendation } from "~/types/trial-design";

// Mock data generator for local testing
export const generateMockStudies = (count: number = 15): StudyResult[] => {
  const conditions = ["Hypertension", "Type 2 Diabetes", "Heart Failure", "Atrial Fibrillation", "COPD"];
  const interventions = ["Drug A", "Drug B", "Device X", "Behavioral Therapy", "Combination Treatment"];
  const sponsors = ["Pharma Corp", "University Medical Center", "Research Institute", "BioTech Inc"];
  
  return Array.from({ length: count }, (_, i) => ({
    id: `study-${i + 1}`,
    nctId: `NCT${String(4000000 + i).padStart(8, "0")}`,
    title: `A ${["Randomized", "Open-Label", "Double-Blind"][i % 3]} Study of ${interventions[i % 5]} in Patients with ${conditions[i % 5]}`,
    description: `This is a ${["phase 2", "phase 3", "phase 4"][i % 3]} clinical trial evaluating the ${["efficacy", "safety", "effectiveness"][i % 3]} of ${interventions[i % 5]} in patients with ${conditions[i % 5]}.`,
    studyType: ["drug", "device", "behavioral"][i % 3] as any,
    phase: ["phase2", "phase3", "phase4"][i % 3] as any,
    status: ["completed", "active", "recruiting"][i % 3] as any,
    startDate: new Date(2020 + (i % 4), i % 12, 1).toISOString(),
    completionDate: i % 3 === 0 ? new Date(2023 + (i % 2), i % 12, 1).toISOString() : undefined,
    enrollment: 100 + (i * 50),
    actualEnrollment: i % 3 === 0 ? 90 + (i * 45) : undefined,
    
    relevanceScore: 0.95 - (i * 0.03),
    matchedTerms: [conditions[i % 5]!, interventions[i % 5]!],
    highlights: [
      `Relevant ${["drug", "device", "intervention"][i % 3]} study`,
      `Similar patient population`,
      `Comparable endpoints`
    ],
    
    conditions: [conditions[i % 5]!],
    interventions: [interventions[i % 5]!],
    sponsors: [sponsors[i % 4]!],
    locations: ["United States", "Canada", "United Kingdom", "Germany"].slice(0, (i % 3) + 1),
    
    primaryOutcomeMeasures: [{
      measure: `Change in ${["blood pressure", "HbA1c", "ejection fraction", "FEV1", "quality of life score"][i % 5]}`,
      timeFrame: `${12 + (i % 12)} weeks`,
      description: `Primary efficacy endpoint measuring change from baseline`
    }],
    
    secondaryOutcomeMeasures: [{
      measure: `${["Safety events", "Hospitalization rate", "Mortality", "Adverse events"][i % 4]}`,
      timeFrame: `${24 + (i % 12)} weeks`,
      description: `Secondary safety endpoint`
    }],
    
    studyDesign: {
      allocation: ["Randomized", "Non-Randomized"][i % 2],
      interventionModel: ["Parallel Assignment", "Crossover Assignment", "Sequential Assignment"][i % 3],
      primaryPurpose: ["Treatment", "Prevention", "Diagnostic"][i % 3],
      masking: ["Double", "Single", "None"][i % 3],
      whoMasked: i % 3 !== 2 ? ["Participant", "Investigator", "Outcomes Assessor"] : [],
    },
    
    results: i % 3 === 0 ? {
      participantFlow: {
        enrolled: 100 + (i * 50),
        started: 95 + (i * 48),
        completed: 90 + (i * 45),
        withdrawnReasons: {
          "Lost to follow-up": 3,
          "Adverse events": 2,
          "Withdrew consent": 5
        }
      },
      adverseEvents: {
        serious: 5 + (i % 10),
        other: 20 + (i % 20),
        deaths: i % 5 === 0 ? 1 : 0
      }
    } : undefined
  }));
};

export const generateMockInsights = (studyIds: string[]): GeneratedInsights => {
  const patterns: Pattern[] = [
    {
      id: "pattern-1",
      category: "design",
      description: "73% of successful studies used a parallel assignment design",
      frequency: 73,
      confidence: 0.85,
      examples: studyIds.slice(0, 3),
      impact: "positive"
    },
    {
      id: "pattern-2",
      category: "population",
      description: "Studies with broader inclusion criteria had 40% better enrollment rates",
      frequency: 65,
      confidence: 0.78,
      examples: studyIds.slice(2, 5),
      impact: "positive"
    },
    {
      id: "pattern-3",
      category: "endpoints",
      description: "Composite endpoints improved statistical power in 60% of studies",
      frequency: 60,
      confidence: 0.72,
      examples: studyIds.slice(1, 4),
      impact: "positive"
    }
  ];
  
  const riskFactors: RiskFactor[] = [
    {
      id: "risk-1",
      factor: "Studies with <100 participants had 60% failure rate",
      category: "sample size",
      impact: "high",
      frequency: 60,
      mitigation: "Consider increasing sample size to at least 150-200 participants",
      examples: studyIds.slice(3, 5)
    },
    {
      id: "risk-2",
      factor: "Lack of run-in period associated with 25% higher dropout",
      category: "design",
      impact: "medium",
      frequency: 45,
      mitigation: "Include a 2-4 week run-in period to identify non-compliant participants",
      examples: studyIds.slice(0, 2)
    }
  ];
  
  const recommendations: Recommendation[] = [
    {
      id: "rec-1",
      area: "Sample Size",
      recommendation: "Target 180-220 participants based on similar studies",
      rationale: "Studies in this range showed optimal balance of power and feasibility",
      evidence: studyIds.slice(0, 5),
      confidence: 0.82,
      alternatives: ["Consider adaptive design to adjust sample size", "Use stratified randomization"],
      priority: "critical"
    },
    {
      id: "rec-2",
      area: "Study Duration",
      recommendation: "Plan for 18-24 month total duration with 12-week treatment period",
      rationale: "Aligns with successful trials in similar populations",
      evidence: studyIds.slice(2, 6),
      confidence: 0.75,
      alternatives: ["Consider interim analysis at 6 weeks", "Extended follow-up for safety"],
      priority: "important"
    },
    {
      id: "rec-3",
      area: "Site Selection",
      recommendation: "Multi-site design with 5-8 centers",
      rationale: "Improves recruitment and generalizability",
      evidence: studyIds.slice(1, 4),
      confidence: 0.88,
      alternatives: ["Start with 3 sites and expand if needed", "Consider virtual trial elements"],
      priority: "important"
    }
  ];
  
  return {
    sessionId: "mock-session-" + Date.now(),
    basedOnStudies: studyIds,
    generatedAt: new Date(),
    successPatterns: patterns,
    riskFactors,
    recommendations,
    statistics: {
      medianEnrollment: 200,
      enrollmentRange: { min: 50, max: 450, q1: 125, q3: 325 },
      medianDurationMonths: 18,
      durationRange: { min: 6, max: 36 },
      successRate: 0.73,
      completionRate: 0.85,
      commonDesigns: [
        { design: "Parallel Assignment", frequency: 0.65 },
        { design: "Crossover", frequency: 0.25 },
        { design: "Sequential", frequency: 0.10 }
      ],
      commonEndpoints: [
        { endpoint: "Clinical Improvement", frequency: 0.75 },
        { endpoint: "Safety Events", frequency: 0.60 },
        { endpoint: "Quality of Life", frequency: 0.45 }
      ],
      dropoutRate: { median: 0.15, range: { min: 0.05, max: 0.35 } }
    },
    keyTakeaways: [
      "Multi-site design significantly improves recruitment timeline",
      "Consider adaptive design elements for efficiency",
      "Patient-reported outcomes increasingly important for regulatory approval",
      "Digital health technologies can reduce site burden and improve retention"
    ]
  };
};