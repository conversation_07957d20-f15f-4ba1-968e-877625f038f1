"use client";

import { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { ConfirmationDialog } from "./confirmation-dialog";
import type { ComponentProps } from "react";

type ConfirmationDialogProps = ComponentProps<typeof ConfirmationDialog>;

export function ConfirmationPortal(props: ConfirmationDialogProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  if (!mounted) {
    return null;
  }

  // Create a portal that renders the dialog at the document body level
  // This ensures it's not affected by any transforms in parent elements
  // and always appears in the viewport center
  return createPortal(
    <ConfirmationDialog {...props} />,
    document.body
  );
}