import * as React from "react"

import { cn } from "~/lib/utils"

function Textarea({ className, value, onChange, ...props }: React.ComponentProps<"textarea">) {
 const textareaRef = React.useRef<HTMLTextAreaElement>(null)

 const adjustHeight = React.useCallback(() => {
 const textarea = textareaRef.current
 if (textarea) {
 // Reset height to recalculate
 textarea.style.height = 'auto'
 // Get the scroll height and account for padding (py-2 = 0.5rem top + 0.5rem bottom = 16px total)
 const scrollHeight = textarea.scrollHeight
 const maxHeight = 250
 
 // Only set height to max and enable scrolling if content actually exceeds the limit
 if (scrollHeight > maxHeight) {
 textarea.style.height = `${maxHeight}px`
 textarea.style.overflowY = 'auto'
 } else {
 textarea.style.height = `${scrollHeight}px`
 textarea.style.overflowY = 'hidden'
 }
 }
 }, [])

 React.useEffect(() => {
 adjustHeight()
 }, [value, adjustHeight])

 const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
 if (onChange) {
 onChange(e)
 }
 // Adjust height after state update
 setTimeout(adjustHeight, 0)
 }

 return (
 <textarea
 ref={textareaRef}
 data-slot="textarea"
 className={cn(
 "border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 aria-invalid:border-destructive flex min-h-16 max-h-[250px] w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm resize-none",
 className
 )}
 value={value}
 onChange={handleChange}
 {...props}
 />
 )
}

export { Textarea }
