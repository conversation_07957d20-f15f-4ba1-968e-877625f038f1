"use client";

import { useEffect } from "react";
import { But<PERSON> } from "./button";
import { AlertTriangle } from "lucide-react";

interface TBDConfirmationDialogProps {
 isOpen: boolean;
 onClose: () => void;
 onConfirm: () => void;
 blankFields: string[];
 isLoading?: boolean;
}

export function TBDConfirmationDialog({ 
 isOpen, 
 onClose, 
 onConfirm, 
 blankFields, 
 isLoading = false 
}: TBDConfirmationDialogProps) {
 // Handle escape key
 useEffect(() => {
 const handleEscape = (e: KeyboardEvent) => {
 if (e.key === 'Escape' && isOpen) {
 onClose();
 }
 };

 document.addEventListener('keydown', handleEscape);
 return () => document.removeEventListener('keydown', handleEscape);
 }, [isOpen, onClose]);

 // Lock body scroll when dialog is open
 useEffect(() => {
 if (isOpen) {
 document.body.style.overflow = 'hidden';
 } else {
 document.body.style.overflow = 'unset';
 }
 
 return () => {
 document.body.style.overflow = 'unset';
 };
 }, [isOpen]);

 if (!isOpen) return null;

 const handleBackdropClick = (e: React.MouseEvent) => {
 if (e.target === e.currentTarget) {
 onClose();
 }
 };

 return (
 <div 
 className="fixed inset-0 z-60 flex items-center justify-center bg-black/50 p-4 backdrop-blur-sm"
 onClick={handleBackdropClick}
 style={{ 
 position: 'fixed',
 top: 0,
 left: 0,
 right: 0,
 bottom: 0,
 }}
 >
 <div 
 className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto relative animate-in fade-in-0 zoom-in-95 duration-200"
 onClick={(e) => e.stopPropagation()}
 >
 <div className="p-6">
 <div className="flex items-start gap-3 mb-4">
 <AlertTriangle className="h-6 w-6 text-amber-500 mt-1 flex-shrink-0" />
 <div>
 <h3 className="text-lg font-semibold text-gray-900 ">
 Complete Form with Placeholder Values
 </h3>
 <p className="text-sm text-gray-600 mt-1">
 The following fields will be filled with "TBD" and can be updated later:
 </p>
 </div>
 </div>

 <div className="mb-6">
 <div className="bg-gray-50 rounded-md p-4 max-h-60 overflow-y-auto">
 <ul className="space-y-2">
 {blankFields.map((field, index) => (
 <li key={index} className="flex items-center gap-2 text-sm text-gray-700 ">
 <div className="w-2 h-2 bg-amber-400 rounded-full flex-shrink-0" />
 {field}
 </li>
 ))}
 </ul>
 </div>
 </div>

 <div className="flex flex-col sm:flex-row gap-3 justify-end">
 <Button
 variant="outline"
 onClick={onClose}
 disabled={isLoading}
 className="order-2 sm:order-1"
 >
 Go Back to Fill Fields
 </Button>
 <Button
 onClick={onConfirm}
 disabled={isLoading}
 className="order-1 sm:order-2"
 >
 {isLoading ? "Saving..." : "Continue with TBD Values"}
 </Button>
 </div>
 </div>
 </div>
 </div>
 );
}