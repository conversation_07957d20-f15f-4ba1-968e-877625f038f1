"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { BlurFade } from "~/components/ui/blur-fade";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { 
 Sparkles, 
 Search, 
 BarChart3, 
 FileText, 
 ArrowRight,
 Zap,
 Shield,
 Globe,
 Users,
 TrendingUp,
 Brain,
 Target,
 Clock
} from "lucide-react";

interface HomeClientProps {
 hasSession: boolean;
}

export function HomeClient({ hasSession }: HomeClientProps) {
 const [statsCounter, setStatsCounter] = useState({ trials: 0, insights: 0, success: 0 });

 // Animated counters
 useEffect(() => {
 const duration = 2000;
 const steps = 50;
 const targets = { trials: 450000, insights: 1200, success: 94 };
 
 const timer = setInterval(() => {
 setStatsCounter(prev => ({
 trials: Math.min(prev.trials + Math.floor(targets.trials / steps), targets.trials),
 insights: Math.min(prev.insights + Math.floor(targets.insights / steps), targets.insights),
 success: Math.min(prev.success + Math.floor(targets.success / steps), targets.success),
 }));
 }, duration / steps);

 return () => clearInterval(timer);
 }, []);

 return (
 <div className="relative min-h-screen overflow-hidden">
 {/* Animated gradient background */}
 <div className="fixed inset-0 bg-gradient-to-br from-purple-50 via-white to-teal-50">
 <div className="absolute inset-0 bg-gradient-to-tr from-[#5A32FA]/5 via-transparent to-[#00C4CC]/5" />
 </div>
 
 {/* Floating orbs */}
 <div className="fixed inset-0 overflow-hidden pointer-events-none">
 <div className="absolute -top-40 -left-40 h-80 w-80 rounded-full bg-gradient-to-br from-[#5A32FA]/20 to-[#7D2AE8]/20 blur-3xl animate-pulse" />
 <div className="absolute top-60 -right-40 h-96 w-96 rounded-full bg-gradient-to-br from-[#00C4CC]/20 to-[#5A32FA]/20 blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
 <div className="absolute bottom-20 left-40 h-72 w-72 rounded-full bg-gradient-to-br from-[#7D2AE8]/20 to-[#00C4CC]/20 blur-3xl animate-pulse" style={{ animationDelay: '4s' }} />
 <div className="absolute top-1/2 right-1/4 h-64 w-64 rounded-full bg-gradient-to-br from-[#5A32FA]/15 to-[#00C4CC]/15 blur-3xl animate-pulse" style={{ animationDelay: '3s' }} />
 </div>

 <div className="relative z-10">
 {/* Glass-morphic header */}
 <BlurFade delay={0} inView>
 <header className="sticky top-0 z-50 border-b border-white/20 bg-white/70 backdrop-blur-xl shadow-sm">
 <nav className="mx-auto flex max-w-7xl items-center justify-between px-6 py-4">
 <Link href="/" className="flex items-center gap-3 group">
 <div className="relative h-10 w-10 rounded-xl bg-gradient-to-br from-[#5A32FA] to-[#7D2AE8] shadow-lg shadow-purple-500/25 group-hover:shadow-xl group-hover:shadow-purple-500/30 transition-all duration-300 group-hover:scale-110">
 <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent" />
 <Sparkles className="absolute inset-2 text-white" />
 </div>
 <span className="text-xl font-bold bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8] bg-clip-text text-transparent">
 TriaLynx Insights
 </span>
 </Link>
 <div className="flex items-center gap-4">
 {hasSession ? (
 <>
 <Link href="/dashboard">
 <Button variant="ghost" className="hover:bg-[#5A32FA]/10">
 Dashboard
 </Button>
 </Link>
 <Link href="/study/new">
 <Button className="bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8] text-white shadow-lg shadow-purple-500/25 hover:shadow-xl hover:shadow-purple-500/30 transition-all duration-300 hover:scale-105">
 New Study Design
 <Zap className="ml-2 h-4 w-4" />
 </Button>
 </Link>
 </>
 ) : (
 <>
 <Link href="/api/auth/signin">
 <Button variant="ghost" className="hover:bg-[#5A32FA]/10">
 Sign In
 </Button>
 </Link>
 <Link href="/api/auth/signin">
 <Button className="bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8] text-white shadow-lg shadow-purple-500/25 hover:shadow-xl hover:shadow-purple-500/30 transition-all duration-300 hover:scale-105">
 Get Started
 <ArrowRight className="ml-2 h-4 w-4" />
 </Button>
 </Link>
 </>
 )}
 </div>
 </nav>
 </header>
 </BlurFade>

 {/* Hero Section */}
 <main className="flex-1">
 <section className="relative py-24">
 <div className="mx-auto max-w-7xl px-6">
 <BlurFade delay={0.1} inView>
 <div className="mx-auto max-w-4xl text-center">
 <div className="mb-8 inline-flex items-center gap-2 rounded-full border border-[#5A32FA]/20 bg-white/80 backdrop-blur-sm px-4 py-2 shadow-lg">
 <Brain className="h-4 w-4 text-[#5A32FA]" />
 <span className="text-sm font-medium text-gray-700">
 AI-Powered Clinical Trial Design Platform
 </span>
 </div>
 <h1 className="text-6xl font-bold tracking-tight text-gray-900 sm:text-7xl">
 Design Clinical Trials with{" "}
 <span className="bg-gradient-to-r from-[#5A32FA] via-[#7D2AE8] to-[#00C4CC] bg-clip-text text-transparent animate-gradient">
 Intelligent Insights
 </span>
 </h1>
 <p className="mt-8 text-xl leading-relaxed text-gray-600 max-w-3xl mx-auto">
 Leverage data from <span className="font-semibold text-[#5A32FA]">450,000+</span> clinical trials 
 to design more successful studies. Our AI analyzes patterns from ClinicalTrials.gov 
 to provide evidence-based recommendations for your trial design.
 </p>
 </div>
 </BlurFade>

 <BlurFade delay={0.2} inView>
 <div className="mt-12 flex flex-col sm:flex-row items-center justify-center gap-4">
 <Link
 href={hasSession ? "/study/new" : "/api/auth/signin"}
 className="group relative inline-flex items-center rounded-xl bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8] px-8 py-4 text-lg font-semibold text-white shadow-xl shadow-purple-500/25 hover:shadow-2xl hover:shadow-purple-500/30 transition-all duration-300 hover:scale-105"
 >
 Start Designing Your Trial
 <Zap className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
 <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-white/0 to-white/20 opacity-0 group-hover:opacity-100 transition-opacity" />
 </Link>
 <Link
 href="#how-it-works"
 className="inline-flex items-center rounded-xl border-2 border-[#5A32FA]/20 bg-white/80 backdrop-blur-sm px-8 py-4 text-lg font-semibold text-gray-900 hover:bg-[#5A32FA]/5 hover:border-[#5A32FA]/40 transition-all duration-300"
 >
 See How It Works
 <ArrowRight className="ml-2 h-5 w-5" />
 </Link>
 </div>
 </BlurFade>

 {/* Stats */}
 <BlurFade delay={0.3} inView>
 <div className="mt-20 grid grid-cols-1 gap-8 sm:grid-cols-3">
 <div className="relative overflow-hidden rounded-2xl border border-[#5A32FA]/10 bg-white/80 backdrop-blur-sm p-6 hover:border-[#5A32FA]/30 transition-all duration-300 group">
 <div className="absolute inset-0 bg-gradient-to-br from-[#5A32FA]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
 <div className="relative">
 <Globe className="h-8 w-8 text-[#5A32FA] mb-3" />
 <div className="text-3xl font-bold bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8] bg-clip-text text-transparent">
 {statsCounter.trials.toLocaleString()}+
 </div>
 <p className="mt-2 text-sm text-gray-600">Clinical Trials Analyzed</p>
 </div>
 </div>
 <div className="relative overflow-hidden rounded-2xl border border-[#00C4CC]/10 bg-white/80 backdrop-blur-sm p-6 hover:border-[#00C4CC]/30 transition-all duration-300 group">
 <div className="absolute inset-0 bg-gradient-to-br from-[#00C4CC]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
 <div className="relative">
 <TrendingUp className="h-8 w-8 text-[#00C4CC] mb-3" />
 <div className="text-3xl font-bold bg-gradient-to-r from-[#00C4CC] to-[#5A32FA] bg-clip-text text-transparent">
 {statsCounter.insights.toLocaleString()}+
 </div>
 <p className="mt-2 text-sm text-gray-600">AI-Generated Insights</p>
 </div>
 </div>
 <div className="relative overflow-hidden rounded-2xl border border-[#7D2AE8]/10 bg-white/80 backdrop-blur-sm p-6 hover:border-[#7D2AE8]/30 transition-all duration-300 group">
 <div className="absolute inset-0 bg-gradient-to-br from-[#7D2AE8]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
 <div className="relative">
 <Target className="h-8 w-8 text-[#7D2AE8] mb-3" />
 <div className="text-3xl font-bold bg-gradient-to-r from-[#7D2AE8] to-[#00C4CC] bg-clip-text text-transparent">
 {statsCounter.success}%
 </div>
 <p className="mt-2 text-sm text-gray-600">Design Success Rate</p>
 </div>
 </div>
 </div>
 </BlurFade>
 </div>
 </section>

 {/* Features Section */}
 <section className="py-24">
 <div className="mx-auto max-w-7xl px-6">
 <BlurFade delay={0.4} inView>
 <div className="mx-auto max-w-3xl text-center">
 <h2 className="text-4xl font-bold text-gray-900 sm:text-5xl">
 Everything you need to{" "}
 <span className="bg-gradient-to-r from-[#5A32FA] to-[#00C4CC] bg-clip-text text-transparent">
 design a successful trial
 </span>
 </h2>
 <p className="mt-6 text-lg text-gray-600">
 From initial concept to complete synopsis, we guide you through every step
 with AI-powered recommendations and real-world evidence
 </p>
 </div>
 </BlurFade>

 <div className="mx-auto mt-16 grid grid-cols-1 gap-8 md:grid-cols-3">
 <BlurFade delay={0.5} inView>
 <Card className="group relative overflow-hidden border-[#5A32FA]/10 bg-white/80 backdrop-blur-sm hover:border-[#5A32FA]/30 hover:shadow-2xl hover:shadow-purple-500/10 transition-all duration-300 hover:scale-[1.02]">
 <div className="absolute inset-0 bg-gradient-to-br from-[#5A32FA]/5 via-transparent to-[#7D2AE8]/5 opacity-0 group-hover:opacity-100 transition-opacity" />
 <CardContent className="relative p-8">
 <div className="mb-4 inline-flex h-14 w-14 items-center justify-center rounded-2xl bg-gradient-to-br from-[#5A32FA] to-[#7D2AE8] shadow-lg shadow-purple-500/25 group-hover:shadow-xl group-hover:shadow-purple-500/30 transition-all duration-300 group-hover:scale-110">
 <Search className="h-7 w-7 text-white" />
 </div>
 <h3 className="text-xl font-bold text-gray-900 mb-3">Smart Discovery</h3>
 <p className="text-gray-600 leading-relaxed">
 Answer a few key questions about your study, and we'll find the most relevant 
 existing trials to learn from using advanced AI pattern matching.
 </p>
 </CardContent>
 </Card>
 </BlurFade>

 <BlurFade delay={0.6} inView>
 <Card className="group relative overflow-hidden border-[#00C4CC]/10 bg-white/80 backdrop-blur-sm hover:border-[#00C4CC]/30 hover:shadow-2xl hover:shadow-teal-500/10 transition-all duration-300 hover:scale-[1.02]">
 <div className="absolute inset-0 bg-gradient-to-br from-[#00C4CC]/5 via-transparent to-[#5A32FA]/5 opacity-0 group-hover:opacity-100 transition-opacity" />
 <CardContent className="relative p-8">
 <div className="mb-4 inline-flex h-14 w-14 items-center justify-center rounded-2xl bg-gradient-to-br from-[#00C4CC] to-[#5A32FA] shadow-lg shadow-teal-500/25 group-hover:shadow-xl group-hover:shadow-teal-500/30 transition-all duration-300 group-hover:scale-110">
 <BarChart3 className="h-7 w-7 text-white" />
 </div>
 <h3 className="text-xl font-bold text-gray-900 mb-3">Pattern Analysis</h3>
 <p className="text-gray-600 leading-relaxed">
 Our AI identifies success patterns and common pitfalls from similar trials, 
 giving you actionable insights backed by real-world evidence.
 </p>
 </CardContent>
 </Card>
 </BlurFade>

 <BlurFade delay={0.7} inView>
 <Card className="group relative overflow-hidden border-[#7D2AE8]/10 bg-white/80 backdrop-blur-sm hover:border-[#7D2AE8]/30 hover:shadow-2xl hover:shadow-purple-500/10 transition-all duration-300 hover:scale-[1.02]">
 <div className="absolute inset-0 bg-gradient-to-br from-[#7D2AE8]/5 via-transparent to-[#00C4CC]/5 opacity-0 group-hover:opacity-100 transition-opacity" />
 <CardContent className="relative p-8">
 <div className="mb-4 inline-flex h-14 w-14 items-center justify-center rounded-2xl bg-gradient-to-br from-[#7D2AE8] to-[#00C4CC] shadow-lg shadow-purple-500/25 group-hover:shadow-xl group-hover:shadow-purple-500/30 transition-all duration-300 group-hover:scale-110">
 <FileText className="h-7 w-7 text-white" />
 </div>
 <h3 className="text-xl font-bold text-gray-900 mb-3">Synopsis Generation</h3>
 <p className="text-gray-600 leading-relaxed">
 Generate a complete trial synopsis with all standard sections, backed by 
 evidence from successful trials and regulatory best practices.
 </p>
 </CardContent>
 </Card>
 </BlurFade>
 </div>

 {/* Additional Features */}
 <div className="mx-auto mt-12 grid grid-cols-2 gap-8 md:grid-cols-4">
 <BlurFade delay={0.8} inView>
 <div className="text-center group">
 <div className="mx-auto mb-4 inline-flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-[#5A32FA]/10 to-[#7D2AE8]/10 group-hover:from-[#5A32FA]/20 group-hover:to-[#7D2AE8]/20 transition-all duration-300">
 <Shield className="h-6 w-6 text-[#5A32FA]" />
 </div>
 <h4 className="font-semibold text-gray-900">Regulatory Compliance</h4>
 <p className="mt-1 text-sm text-gray-600">FDA & EMA aligned</p>
 </div>
 </BlurFade>

 <BlurFade delay={0.85} inView>
 <div className="text-center group">
 <div className="mx-auto mb-4 inline-flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-[#00C4CC]/10 to-[#5A32FA]/10 group-hover:from-[#00C4CC]/20 group-hover:to-[#5A32FA]/20 transition-all duration-300">
 <Users className="h-6 w-6 text-[#00C4CC]" />
 </div>
 <h4 className="font-semibold text-gray-900">Team Collaboration</h4>
 <p className="mt-1 text-sm text-gray-600">Share & iterate together</p>
 </div>
 </BlurFade>

 <BlurFade delay={0.9} inView>
 <div className="text-center group">
 <div className="mx-auto mb-4 inline-flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-[#7D2AE8]/10 to-[#00C4CC]/10 group-hover:from-[#7D2AE8]/20 group-hover:to-[#00C4CC]/20 transition-all duration-300">
 <Clock className="h-6 w-6 text-[#7D2AE8]" />
 </div>
 <h4 className="font-semibold text-gray-900">Fast Generation</h4>
 <p className="mt-1 text-sm text-gray-600">Hours not weeks</p>
 </div>
 </BlurFade>

 <BlurFade delay={0.95} inView>
 <div className="text-center group">
 <div className="mx-auto mb-4 inline-flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-[#5A32FA]/10 to-[#00C4CC]/10 group-hover:from-[#5A32FA]/20 group-hover:to-[#00C4CC]/20 transition-all duration-300">
 <Zap className="h-6 w-6 text-[#5A32FA]" />
 </div>
 <h4 className="font-semibold text-gray-900">AI-Powered</h4>
 <p className="mt-1 text-sm text-gray-600">Latest LLM technology</p>
 </div>
 </BlurFade>
 </div>
 </div>
 </section>

 {/* How It Works Section */}
 <section id="how-it-works" className="py-24 relative">
 <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#5A32FA]/5 to-transparent" />
 <div className="mx-auto max-w-7xl px-6 relative">
 <BlurFade delay={0.2} inView>
 <div className="mx-auto max-w-3xl text-center">
 <h2 className="text-4xl font-bold text-gray-900 sm:text-5xl">
 How It Works
 </h2>
 <p className="mt-6 text-lg text-gray-600">
 Four simple steps to a data-driven trial design
 </p>
 </div>
 </BlurFade>

 <div className="mx-auto mt-16 max-w-5xl">
 <div className="space-y-12">
 <BlurFade delay={0.3} inView>
 <div className="flex gap-6 group">
 <div className="relative flex h-12 w-12 shrink-0 items-center justify-center">
 <div className="absolute inset-0 rounded-full bg-gradient-to-br from-[#5A32FA] to-[#7D2AE8] shadow-lg shadow-purple-500/25 group-hover:shadow-xl group-hover:shadow-purple-500/30 transition-all duration-300 group-hover:scale-110" />
 <span className="relative text-white font-bold text-lg">1</span>
 </div>
 <div className="flex-1">
 <h3 className="text-xl font-bold text-gray-900 mb-2">Tell us about your study</h3>
 <p className="text-gray-600 leading-relaxed">
 Provide basic information about your intervention, target population, and primary objectives.
 Our intuitive wizard guides you through the essential questions.
 </p>
 </div>
 </div>
 </BlurFade>

 <BlurFade delay={0.4} inView>
 <div className="flex gap-6 group">
 <div className="relative flex h-12 w-12 shrink-0 items-center justify-center">
 <div className="absolute inset-0 rounded-full bg-gradient-to-br from-[#00C4CC] to-[#5A32FA] shadow-lg shadow-teal-500/25 group-hover:shadow-xl group-hover:shadow-teal-500/30 transition-all duration-300 group-hover:scale-110" />
 <span className="relative text-white font-bold text-lg">2</span>
 </div>
 <div className="flex-1">
 <h3 className="text-xl font-bold text-gray-900 mb-2">We find similar trials</h3>
 <p className="text-gray-600 leading-relaxed">
 Our AI searches through 450,000+ trials to find the most relevant studies,
 analyzing design patterns, outcomes, and regulatory submissions.
 </p>
 </div>
 </div>
 </BlurFade>

 <BlurFade delay={0.5} inView>
 <div className="flex gap-6 group">
 <div className="relative flex h-12 w-12 shrink-0 items-center justify-center">
 <div className="absolute inset-0 rounded-full bg-gradient-to-br from-[#7D2AE8] to-[#00C4CC] shadow-lg shadow-purple-500/25 group-hover:shadow-xl group-hover:shadow-purple-500/30 transition-all duration-300 group-hover:scale-110" />
 <span className="relative text-white font-bold text-lg">3</span>
 </div>
 <div className="flex-1">
 <h3 className="text-xl font-bold text-gray-900 mb-2">Get actionable insights</h3>
 <p className="text-gray-600 leading-relaxed">
 Review AI-generated recommendations for endpoints, inclusion criteria, sample size,
 and operational considerations based on successful precedents.
 </p>
 </div>
 </div>
 </BlurFade>

 <BlurFade delay={0.6} inView>
 <div className="flex gap-6 group">
 <div className="relative flex h-12 w-12 shrink-0 items-center justify-center">
 <div className="absolute inset-0 rounded-full bg-gradient-to-br from-[#5A32FA] to-[#00C4CC] shadow-lg shadow-purple-500/25 group-hover:shadow-xl group-hover:shadow-purple-500/30 transition-all duration-300 group-hover:scale-110" />
 <span className="relative text-white font-bold text-lg">4</span>
 </div>
 <div className="flex-1">
 <h3 className="text-xl font-bold text-gray-900 mb-2">Build your synopsis</h3>
 <p className="text-gray-600 leading-relaxed">
 Generate a complete, professional trial synopsis with all standard sections,
 ready for regulatory submission and stakeholder review.
 </p>
 </div>
 </div>
 </BlurFade>
 </div>
 </div>
 </div>
 </section>

 {/* CTA Section */}
 <section className="py-24 relative overflow-hidden">
 <div className="absolute inset-0 bg-gradient-to-br from-[#5A32FA] via-[#7D2AE8] to-[#00C4CC]" />
 <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
 
 {/* Floating elements for CTA */}
 <div className="absolute -top-20 -left-20 h-60 w-60 rounded-full bg-white/10 blur-3xl" />
 <div className="absolute -bottom-20 -right-20 h-80 w-80 rounded-full bg-white/10 blur-3xl" />
 
 <div className="relative mx-auto max-w-7xl px-6 text-center">
 <BlurFade delay={0.1} inView>
 <h2 className="text-4xl font-bold tracking-tight text-white sm:text-5xl">
 Ready to revolutionize your trial design process?
 </h2>
 <p className="mx-auto mt-6 max-w-2xl text-xl text-white/90">
 Join leading researchers and biotech companies using AI-powered insights 
 to design more successful clinical trials.
 </p>
 <div className="mt-10 flex flex-col sm:flex-row items-center justify-center gap-4">
 <Link
 href={hasSession ? "/study/new" : "/api/auth/signin"}
 className="group inline-flex items-center rounded-xl bg-white px-8 py-4 text-lg font-semibold text-[#5A32FA] shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105"
 >
 Start Your Free Trial
 <Sparkles className="ml-2 h-5 w-5 group-hover:rotate-12 transition-transform" />
 </Link>
 <Link
 href="/contact"
 className="inline-flex items-center rounded-xl border-2 border-white/30 bg-white/10 backdrop-blur-sm px-8 py-4 text-lg font-semibold text-white hover:bg-white/20 hover:border-white/50 transition-all duration-300"
 >
 Schedule a Demo
 <ArrowRight className="ml-2 h-5 w-5" />
 </Link>
 </div>
 </BlurFade>
 </div>
 </section>
 </main>

 {/* Footer */}
 <footer className="relative border-t border-gray-200/20 bg-gray-900/95 backdrop-blur-xl">
 <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/20" />
 <div className="relative mx-auto max-w-7xl px-6 py-12">
 <div className="flex flex-col items-center justify-between gap-6 sm:flex-row">
 <div className="flex items-center gap-3">
 <div className="relative h-8 w-8 rounded-lg bg-gradient-to-br from-[#5A32FA] to-[#7D2AE8] shadow-lg shadow-purple-500/25">
 <Sparkles className="absolute inset-1.5 text-white" />
 </div>
 <span className="text-lg font-semibold text-white">TriaLynx Insights</span>
 </div>
 <div className="flex gap-6">
 <Link href="/privacy" className="text-sm text-gray-400 hover:text-white transition-colors">
 Privacy Policy
 </Link>
 <Link href="/terms" className="text-sm text-gray-400 hover:text-white transition-colors">
 Terms of Service
 </Link>
 <Link href="/contact" className="text-sm text-gray-400 hover:text-white transition-colors">
 Contact
 </Link>
 </div>
 </div>
 <div className="mt-8 border-t border-gray-800 pt-8 text-center">
 <p className="text-sm text-gray-400">
 © 2024 TriaLynx Insights. All rights reserved.
 </p>
 </div>
 </div>
 </footer>
 </div>

 <style jsx>{`
 @keyframes gradient {
 0%, 100% {
 background-size: 200% 200%;
 background-position: left center;
 }
 50% {
 background-size: 200% 200%;
 background-position: right center;
 }
 }
 .animate-gradient {
 background-size: 200% 200%;
 animation: gradient 4s ease infinite;
 }
 `}</style>
 </div>
 );
}