"use client";

import { useState } from "react";
import { Button } from "~/components/ui/button";
import { <PERSON><PERSON><PERSON>, Loader2, Eye, RefreshCw } from "lucide-react";
import { cn } from "~/lib/utils";

interface InsightsButtonProps {
 onClick: () => void | Promise<void>;
 loading?: boolean;
 disabled?: boolean;
 className?: string;
 size?: "default" | "sm" | "lg" | "icon";
 variant?: "default" | "outline" | "ghost" | "secondary";
 children?: React.ReactNode;
 hasCachedData?: boolean;
 showRefresh?: boolean;
 onRefresh?: () => void | Promise<void>;
}

export function InsightsButton({
 onClick,
 loading = false,
 disabled = false,
 className,
 size = "sm",
 variant = "outline",
 children,
 hasCachedData = false,
 showRefresh = false,
 onRefresh
}: InsightsButtonProps) {
 const [isLoading, setIsLoading] = useState(false);

 const handleClick = async () => {
 if (loading || isLoading || disabled) return;
 
 setIsLoading(true);
 try {
 await onClick();
 } finally {
 setIsLoading(false);
 }
 };

 const handleRefresh = async () => {
 if (loading || isLoading || disabled || !onRefresh) return;
 
 setIsLoading(true);
 try {
 await onRefresh();
 } finally {
 setIsLoading(false);
 }
 };

 const showLoading = loading || isLoading;

 if (hasCachedData && showRefresh && onRefresh) {
 return (
 <div className="flex gap-2">
 <Button
 onClick={handleClick}
 disabled={disabled || showLoading}
 size={size}
 variant={variant}
 className={cn("transition-all", className)}
 >
 <Eye className="mr-2 h-4 w-4" />
 View Insights
 </Button>
 <Button
 onClick={handleRefresh}
 disabled={disabled || showLoading}
 size="icon"
 variant="ghost"
 className="h-8 w-8"
 title="Refresh insights"
 >
 {showLoading ? (
 <Loader2 className="h-4 w-4 animate-spin" />
 ) : (
 <RefreshCw className="h-4 w-4" />
 )}
 </Button>
 </div>
 );
 }

 return (
 <Button
 onClick={handleClick}
 disabled={disabled || showLoading}
 size={size}
 variant={variant}
 className={cn(
 "relative transition-all",
 showLoading && "pl-8",
 className
 )}
 >
 {showLoading ? (
 <>
 <Loader2 className="absolute left-2 h-4 w-4 animate-spin" />
 <span className="ml-2">Getting insights...</span>
 </>
 ) : hasCachedData ? (
 <>
 <Eye className="mr-2 h-4 w-4" />
 {children || "View Insights"}
 </>
 ) : (
 <>
 <Sparkles className="mr-2 h-4 w-4" />
 {children || "Get Insights"}
 </>
 )}
 </Button>
 );
}