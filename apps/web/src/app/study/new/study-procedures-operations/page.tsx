"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { InsightsButton } from "~/components/insights/InsightsButton";
import { InsightsPanelPortal } from "~/components/insights/InsightsPanelPortal";
import { DocumentViewerPortal } from "~/components/insights/DocumentViewerPortal";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { cn } from "~/lib/utils";
import { 
 ChevronLeft, 
 Calendar,
 Clock,
 TestTube,
 Microscope,
 Activity,
 Building2,
 TrendingUp,
 Database,
 Eye,
 Plus,
 X,
 Info,
 AlertTriangle
} from "lucide-react";

export default function StudyProceduresOperationsPage() {
 const router = useRouter();
 const store = useTrialDesignStore();
 
 // Validation state
 const [fieldErrors, setFieldErrors] = useState<Record<string, boolean>>({});
 
 const [formData, setFormData] = useState({
 // Timeline & Study Events (enhanced from existing)
 screeningPeriod: store.discovery.timeline?.screeningPeriod || "",
 baselinePeriod: store.discovery.timeline?.baselinePeriod || "",
 treatmentPeriod: store.discovery.timeline?.treatmentPeriod || "",
 followUpPeriod: store.discovery.timeline?.followUpPeriod || "",
 totalDuration: store.discovery.timeline?.totalDuration || "",
 studyEventsAndActivities: store.discovery.protocol?.studyEventsAndActivities || "",
 durationWithDates: store.discovery.protocol?.durationWithDates || "",
 trialInterventionDetails: store.discovery.protocol?.trialInterventionDetails || "",
 visits: store.discovery.timeline?.visits || [],
 
 // Laboratory & Biomarker Studies (7 new critical questions)
 willCollectBiologicalSamples: store.discovery.laboratory?.willCollectBiologicalSamples === true ? "Yes" : 
                                store.discovery.laboratory?.willCollectBiologicalSamples === false ? "No" : "",
 biologicalSpecimens: store.discovery.laboratory?.biologicalSpecimens || [],
 collectionAndProcessing: store.discovery.laboratory?.collectionAndProcessing || "",
 willConductPK: store.discovery.laboratory?.willConductPK === true ? "Yes" : 
                store.discovery.laboratory?.willConductPK === false ? "No" : "",
 willConductBiomarker: store.discovery.laboratory?.willConductBiomarker === true ? "Yes" : 
                       store.discovery.laboratory?.willConductBiomarker === false ? "No" : "",
 willConductImmunogenicity: store.discovery.laboratory?.willConductImmunogenicity === true ? "Yes" : 
                            store.discovery.laboratory?.willConductImmunogenicity === false ? "No" : "",
 willConductGeneticTesting: store.discovery.laboratory?.willConductGeneticTesting === true ? "Yes" : 
                            store.discovery.laboratory?.willConductGeneticTesting === false ? "No" : "",
 
 // Operational Details (enhanced from existing)
 recruitmentRate: store.discovery.operational?.recruitmentRate || "",
 screenFailureRate: store.discovery.operational?.screenFailureRate || "",
 dropoutRate: store.discovery.operational?.dropoutRate || "",
 dataManagementSystem: store.discovery.operational?.dataManagementSystem || "",
 edcCTMSName: store.discovery.operational?.edcCTMSName || "",
 monitoringApproach: store.discovery.operational?.monitoringApproach || "",
 });

 const [newVisit, setNewVisit] = useState({ name: "", timepoint: "", procedures: [""], critical: "No" });
 const [newSpecimen, setNewSpecimen] = useState("");
 const [activeInsightsPanel, setActiveInsightsPanel] = useState<string | null>(null);
 const [insightsData, setInsightsData] = useState<Record<string, { sections: any[]; sources?: any[]; progressStatus?: string; progressMessages?: string[] }>>({});
 const [documentViewerUrl, setDocumentViewerUrl] = useState<string | null>(null);
 
 const cachedInsights = store.insightsCache || {};

 const queryInsights = api.knowledgeBase.queryInsights.useMutation({
 onSuccess: (data, variables) => {
 const insightsPayload = {
 sections: data.sections || [],
 sources: data.sources || [],
 progressStatus: undefined,
 progressMessages: [],
 };
 
 setInsightsData(prev => ({
 ...prev,
 [variables.field]: insightsPayload
 }));
 
 store.cacheInsights(variables.field, insightsPayload);
 setActiveInsightsPanel(variables.field);
 },
 onError: (error) => {
 toast.error("Failed to get insights: " + error.message);
 },
 });

 const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
 onSuccess: () => {
 store.markStepCompleted("study-procedures-operations");
 router.push("/study/new/regulatory-financial-legal");
 },
 onError: (error) => {
 toast.error("Failed to save: " + error.message);
 },
 });

 const handleGetInsights = async (field: string, forceRefresh = false) => {
 if (!forceRefresh && cachedInsights[field]) {
 setActiveInsightsPanel(field);
 return;
 }
 
 setActiveInsightsPanel(field);
 setInsightsData(prev => ({
 ...prev,
 [field]: {
 sections: [],
 sources: [],
 progressStatus: 'Analyzing study procedures...',
 progressMessages: [],
 }
 }));
 
 const progressUpdates = field === 'study-timeline' ? [
 { delay: 0, message: 'Initializing search...' },
 { delay: 1500, message: 'Searching study timelines...' },
 { delay: 3500, message: 'Analyzing phase durations...' },
 { delay: 6000, message: 'Extracting timeline patterns...' },
 { delay: 8500, message: 'Generating recommendations...' },
 ] : field === 'visit-schedule' ? [
 { delay: 0, message: 'Initializing search...' },
 { delay: 1500, message: 'Searching visit schedules...' },
 { delay: 3500, message: 'Analyzing procedure frequencies...' },
 { delay: 6000, message: 'Extracting visit patterns...' },
 { delay: 8500, message: 'Generating recommendations...' },
 ] : field === 'intervention-details' ? [
 { delay: 0, message: 'Initializing search...' },
 { delay: 1500, message: 'Searching intervention protocols...' },
 { delay: 3500, message: 'Analyzing dosing strategies...' },
 { delay: 6000, message: 'Extracting study milestones...' },
 { delay: 8500, message: 'Generating recommendations...' },
 ] : field === 'laboratory-studies' ? [
 { delay: 0, message: 'Initializing search...' },
 { delay: 1500, message: 'Searching laboratory procedures...' },
 { delay: 3500, message: 'Analyzing biomarker strategies...' },
 { delay: 6000, message: 'Extracting PK/PD approaches...' },
 { delay: 8500, message: 'Generating recommendations...' },
 ] : [
 { delay: 0, message: 'Initializing search...' },
 { delay: 1500, message: 'Searching operational data...' },
 { delay: 3500, message: 'Analyzing recruitment strategies...' },
 { delay: 6000, message: 'Extracting best practices...' },
 { delay: 8500, message: 'Generating recommendations...' },
 ];
 
 progressUpdates.forEach(({ delay, message }) => {
 setTimeout(() => {
 setInsightsData(prev => {
 const current = prev[field];
 if (current && !current.sections?.length) {
 return {
 ...prev,
 [field]: {
 ...current,
 progressStatus: message,
 progressMessages: [...(current.progressMessages || []), message],
 }
 };
 }
 return prev;
 });
 }, delay);
 });
 
 const context = {
 studyType: store.discovery.studyType || "drug",
 condition: store.discovery.condition || "",
 phase: store.discovery.phase || "",
 drugName: store.discovery.intervention?.name || "",
 targetEnrollment: store.discovery.population?.targetEnrollment || "",
 numberOfSites: store.discovery.design?.numberOfSites || "",
 mechanismOfAction: store.discovery.intervention?.mechanismOfAction || "",
 treatmentPeriod: formData.treatmentPeriod || store.discovery.timeline?.treatmentPeriod || "",
 };

 const queries: Record<string, string> = {
 "study-timeline": `What are typical study durations and timeline phases for ${store.discovery.condition || "clinical"} trials in ${store.discovery.phase || "Phase 2/3"}?`,
 "visit-schedule": `What are recommended visit schedules and procedures for ${store.discovery.condition || "clinical"} trials in ${store.discovery.phase || "Phase 2/3"}?`,
 "intervention-details": `What are recommended intervention details and study events for ${store.discovery.intervention?.name || store.discovery.condition || "clinical"} trials in ${store.discovery.phase || "Phase 2/3"}?`,
 "laboratory-studies": `What laboratory studies (PK, biomarkers, genetic testing) are commonly conducted in ${store.discovery.condition || "clinical"} trials?`,
 "operational-strategy": `What are typical recruitment rates and operational strategies for ${store.discovery.condition || "clinical"} trials with ${store.discovery.population.targetEnrollment || "300"} participants?`,
 };

 await queryInsights.mutateAsync({
 sessionId: store.sessionId!,
 field,
 context,
 query: queries[field] || "",
 });
 };

 // Visit management functions
 const addVisit = () => {
 if (newVisit.name.trim() && newVisit.timepoint.trim()) {
 // Check for duplicate visits
 const exists = formData.visits.some(v => 
 v.name.toLowerCase() === newVisit.name.trim().toLowerCase() || 
 v.timepoint.toLowerCase() === newVisit.timepoint.trim().toLowerCase()
 );
 
 if (exists) {
 toast.error("A visit with this name or timepoint already exists");
 return;
 }
 
 // Check that at least one procedure is defined
 const validProcedures = newVisit.procedures.filter(p => p.trim());
 if (validProcedures.length === 0) {
 toast.error("Please specify at least one procedure for this visit");
 return;
 }
 
 setFormData(prev => ({
 ...prev,
 visits: [...prev.visits, {
 name: newVisit.name.trim(),
 timepoint: newVisit.timepoint.trim(),
 procedures: validProcedures,
 critical: newVisit.critical === "Yes"
 }]
 }));
 setNewVisit({ name: "", timepoint: "", procedures: [""], critical: "No" });
 toast.success(`Added ${newVisit.name.trim()} visit`);
 }
 };

 const removeVisit = (index: number) => {
 setFormData(prev => ({
 ...prev,
 visits: prev.visits.filter((_, i) => i !== index)
 }));
 };

 // Visit template functions
 const visitTemplates = {
 screening: {
 name: "Screening",
 timepoint: "Day -28 to -1",
 procedures: ["Informed consent", "Medical history", "Physical exam", "Vital signs", "Laboratory tests", "ECG", "Eligibility assessment"],
 critical: true
 },
 baseline: {
 name: "Baseline/Day 0",
 timepoint: "Day 0",
 procedures: ["Vital signs", "Physical exam", "Laboratory tests", "ECG", "Drug dispensing", "Randomization"],
 critical: true
 },
 followUp: {
 name: "Follow-up",
 timepoint: "Week 12",
 procedures: ["Vital signs", "Physical exam", "Adverse event assessment", "Drug compliance check"],
 critical: false
 },
 endOfTreatment: {
 name: "End of Treatment",
 timepoint: "Week 24",
 procedures: ["Vital signs", "Physical exam", "Laboratory tests", "ECG", "Primary endpoint assessment", "Drug return"],
 critical: true
 },
 safetyFollowUp: {
 name: "Safety Follow-up",
 timepoint: "Week 28",
 procedures: ["Vital signs", "Adverse event assessment", "Laboratory tests"],
 critical: false
 }
 };

 const addVisitTemplate = (templateKey: keyof typeof visitTemplates) => {
 const template = visitTemplates[templateKey];
 // Check if this visit already exists
 const exists = formData.visits.some(v => 
 v.name === template.name || v.timepoint === template.timepoint
 );
 
 if (exists) {
 toast.info(`${template.name} visit already exists`);
 return;
 }
 
 setFormData(prev => ({
 ...prev,
 visits: [...prev.visits, template]
 }));
 toast.success(`Added ${template.name} visit`);
 };

 const updateVisitProcedure = (index: number, value: string) => {
 const updatedProcedures = [...newVisit.procedures];
 updatedProcedures[index] = value;
 setNewVisit(prev => ({ ...prev, procedures: updatedProcedures }));
 };

 const addVisitProcedure = () => {
 setNewVisit(prev => ({ ...prev, procedures: [...prev.procedures, ""] }));
 };

 const removeVisitProcedure = (index: number) => {
 setNewVisit(prev => ({
 ...prev,
 procedures: prev.procedures.filter((_, i) => i !== index)
 }));
 };

 // Specimen management functions
 const addSpecimen = () => {
 if (newSpecimen.trim()) {
 setFormData(prev => ({
 ...prev,
 biologicalSpecimens: [...prev.biologicalSpecimens, newSpecimen.trim()]
 }));
 setNewSpecimen("");
 }
 };

 const removeSpecimen = (index: number) => {
 setFormData(prev => ({
 ...prev,
 biologicalSpecimens: prev.biologicalSpecimens.filter((_, i) => i !== index)
 }));
 };

 const validateForm = (): { errors: string[], fieldErrors: Record<string, boolean>, firstErrorField: string | null } => {
 const errors: string[] = [];
 const fieldErrorMap: Record<string, boolean> = {};
 let firstErrorField: string | null = null;
 
 // Timeline & Study Events - ALL fields are required
 if (!formData.screeningPeriod.trim()) {
 errors.push("Screening Period is required");
 fieldErrorMap.screeningPeriod = true;
 if (!firstErrorField) firstErrorField = "screeningPeriod";
 }
 
 if (!formData.baselinePeriod.trim()) {
 errors.push("Baseline Period is required");
 fieldErrorMap.baselinePeriod = true;
 if (!firstErrorField) firstErrorField = "baselinePeriod";
 }
 
 if (!formData.treatmentPeriod.trim()) {
 errors.push("Treatment Period is required");
 fieldErrorMap.treatmentPeriod = true;
 if (!firstErrorField) firstErrorField = "treatmentPeriod";
 }
 
 if (!formData.followUpPeriod.trim()) {
 errors.push("Follow-up Period is required");
 fieldErrorMap.followUpPeriod = true;
 if (!firstErrorField) firstErrorField = "followUpPeriod";
 }
 
 if (!formData.totalDuration.trim()) {
 errors.push("Total Study Duration is required");
 fieldErrorMap.totalDuration = true;
 if (!firstErrorField) firstErrorField = "totalDuration";
 }
 
 if (!formData.durationWithDates.trim()) {
 errors.push("Duration with Specific Dates is required");
 fieldErrorMap.durationWithDates = true;
 if (!firstErrorField) firstErrorField = "durationWithDates";
 }
 
 // Trial Intervention section
 if (!formData.trialInterventionDetails.trim()) {
 errors.push("Trial Intervention Details are required");
 fieldErrorMap.trialInterventionDetails = true;
 if (!firstErrorField) firstErrorField = "trialInterventionDetails";
 }
 
 if (!formData.studyEventsAndActivities.trim()) {
 errors.push("Study Events and Activities are required");
 fieldErrorMap.studyEventsAndActivities = true;
 if (!firstErrorField) firstErrorField = "studyEventsAndActivities";
 }
 
 // Visit schedule validation
 if (formData.visits.length === 0) {
 errors.push("At least one visit must be defined");
 // Can't highlight a specific field for this, but we note it
 } else {
 // Check for essential visits
 const hasScreening = formData.visits.some(v => 
 v.name.toLowerCase().includes('screening')
 );
 const hasBaseline = formData.visits.some(v => 
 v.name.toLowerCase().includes('baseline') || 
 v.name.toLowerCase().includes('day 0')
 );
 
 if (!hasScreening) {
 errors.push("A Screening visit is typically required");
 }
 if (!hasBaseline) {
 errors.push("A Baseline/Day 0 visit is typically required");
 }
 }
 
 // Laboratory & Biomarker Studies - ALL Yes/No fields are required
 if (!formData.willCollectBiologicalSamples) {
 errors.push("Please select whether biological samples will be collected");
 fieldErrorMap.willCollectBiologicalSamples = true;
 if (!firstErrorField) firstErrorField = "willCollectBiologicalSamples";
 }
 
 // Conditional validations for biological samples
 if (formData.willCollectBiologicalSamples === "Yes") {
 if (formData.biologicalSpecimens.length === 0) {
 errors.push("Please specify what specimens will be collected");
 }
 if (!formData.collectionAndProcessing.trim()) {
 errors.push("Please describe the collection and processing procedures");
 fieldErrorMap.collectionAndProcessing = true;
 if (!firstErrorField) firstErrorField = "collectionAndProcessing";
 }
 }
 
 if (!formData.willConductPK) {
 errors.push("Please select whether PK studies will be conducted");
 fieldErrorMap.willConductPK = true;
 if (!firstErrorField) firstErrorField = "willConductPK";
 }
 
 if (!formData.willConductBiomarker) {
 errors.push("Please select whether biomarker analysis will be conducted");
 fieldErrorMap.willConductBiomarker = true;
 if (!firstErrorField) firstErrorField = "willConductBiomarker";
 }
 
 if (!formData.willConductImmunogenicity) {
 errors.push("Please select whether immunogenicity testing will be conducted");
 fieldErrorMap.willConductImmunogenicity = true;
 if (!firstErrorField) firstErrorField = "willConductImmunogenicity";
 }
 
 if (!formData.willConductGeneticTesting) {
 errors.push("Please select whether genetic testing will be conducted");
 fieldErrorMap.willConductGeneticTesting = true;
 if (!firstErrorField) firstErrorField = "willConductGeneticTesting";
 }
 
 // Operational Strategy - ALL fields are required
 if (!formData.recruitmentRate.trim()) {
 errors.push("Expected Recruitment Rate is required");
 fieldErrorMap.recruitmentRate = true;
 if (!firstErrorField) firstErrorField = "recruitmentRate";
 }
 
 if (!formData.screenFailureRate.trim()) {
 errors.push("Screen Failure Rate is required");
 fieldErrorMap.screenFailureRate = true;
 if (!firstErrorField) firstErrorField = "screenFailureRate";
 }
 
 if (!formData.dropoutRate.trim()) {
 errors.push("Expected Dropout Rate is required");
 fieldErrorMap.dropoutRate = true;
 if (!firstErrorField) firstErrorField = "dropoutRate";
 }
 
 // Data Management System - already has default but let's ensure it's selected
 if (!formData.dataManagementSystem) {
 errors.push("Data Management System selection is required");
 fieldErrorMap.dataManagementSystem = true;
 if (!firstErrorField) firstErrorField = "dataManagementSystem";
 }
 
 // Conditional validation for EDC/CTMS
 if (formData.dataManagementSystem === "edc" || formData.dataManagementSystem === "hybrid") {
 if (!formData.edcCTMSName.trim()) {
 errors.push("Please specify the EDC/CTMS platform name");
 fieldErrorMap.edcCTMSName = true;
 if (!firstErrorField) firstErrorField = "edcCTMSName";
 }
 }
 
 // Monitoring Approach - already has default but let's ensure it's selected
 if (!formData.monitoringApproach) {
 errors.push("Monitoring Approach selection is required");
 fieldErrorMap.monitoringApproach = true;
 if (!firstErrorField) firstErrorField = "monitoringApproach";
 }
 
 return { errors, fieldErrors: fieldErrorMap, firstErrorField };
 };


 const handleContinue = () => {
 if (!store.sessionId) {
 toast.error("Session not found");
 return;
 }

 // Validate form before proceeding
 const validation = validateForm();
 if (validation.errors.length > 0) {
 // Set field errors directly to show all errors simultaneously
 setFieldErrors(validation.fieldErrors);
 toast.error("Please complete all required fields");
 
 // Scroll to first error field
 if (validation.firstErrorField) {
 setTimeout(() => {
 const element = document.getElementById(validation.firstErrorField);
 if (element) {
 element.scrollIntoView({ behavior: 'smooth', block: 'center' });
 setTimeout(() => {
 element.focus();
 }, 500);
 }
 }, 100);
 }
 return;
 }
 
 // Clear all field errors on successful validation
 setFieldErrors({});

 // Update store with comprehensive procedures and operations data
 store.updateDiscovery({ 
 timeline: {
 screeningPeriod: formData.screeningPeriod,
 baselinePeriod: formData.baselinePeriod,
 treatmentPeriod: formData.treatmentPeriod,
 followUpPeriod: formData.followUpPeriod,
 totalDuration: formData.totalDuration,
 visits: formData.visits,
 },
 protocol: {
 ...store.discovery.protocol,
 studyEventsAndActivities: formData.studyEventsAndActivities,
 durationWithDates: formData.durationWithDates,
 trialInterventionDetails: formData.trialInterventionDetails,
 },
 laboratory: {
 willCollectBiologicalSamples: formData.willCollectBiologicalSamples === "Yes",
 biologicalSpecimens: formData.biologicalSpecimens,
 collectionAndProcessing: formData.collectionAndProcessing,
 willConductPK: formData.willConductPK === "Yes",
 willConductBiomarker: formData.willConductBiomarker === "Yes",
 willConductImmunogenicity: formData.willConductImmunogenicity === "Yes",
 willConductGeneticTesting: formData.willConductGeneticTesting === "Yes",
 },
 operational: {
 recruitmentRate: formData.recruitmentRate,
 screenFailureRate: formData.screenFailureRate,
 dropoutRate: formData.dropoutRate,
 dataManagementSystem: formData.dataManagementSystem as any,
 edcCTMSName: formData.edcCTMSName,
 monitoringApproach: formData.monitoringApproach as any,
 }
 });

 saveDiscovery.mutate({
 sessionId: store.sessionId,
 data: {
 timeline: {
 screeningPeriod: formData.screeningPeriod,
 baselinePeriod: formData.baselinePeriod,
 treatmentPeriod: formData.treatmentPeriod,
 followUpPeriod: formData.followUpPeriod,
 totalDuration: formData.totalDuration,
 visits: formData.visits,
 },
 protocol: {
 studyEventsAndActivities: formData.studyEventsAndActivities,
 durationWithDates: formData.durationWithDates,
 trialInterventionDetails: formData.trialInterventionDetails,
 },
 laboratory: {
 willCollectBiologicalSamples: formData.willCollectBiologicalSamples === "Yes",
 biologicalSpecimens: formData.biologicalSpecimens,
 collectionAndProcessing: formData.collectionAndProcessing,
 willConductPK: formData.willConductPK === "Yes",
 willConductBiomarker: formData.willConductBiomarker === "Yes",
 willConductImmunogenicity: formData.willConductImmunogenicity === "Yes",
 willConductGeneticTesting: formData.willConductGeneticTesting === "Yes",
 },
 operational: {
 recruitmentRate: formData.recruitmentRate,
 screenFailureRate: formData.screenFailureRate,
 dropoutRate: formData.dropoutRate,
 dataManagementSystem: formData.dataManagementSystem,
 edcCTMSName: formData.edcCTMSName,
 monitoringApproach: formData.monitoringApproach,
 }
 },
 });
 };

 const handleApplySuggestion = (field: string, suggestion: string, actionableData?: any) => {
 if (actionableData) {
 const updates: any = {};
 
 if (field === "study-timeline") {
 // Handle timeline fields
 if (actionableData.field === 'timeline') {
 if (actionableData.screeningPeriod) updates.screeningPeriod = actionableData.screeningPeriod;
 if (actionableData.baselinePeriod) updates.baselinePeriod = actionableData.baselinePeriod;
 if (actionableData.treatmentPeriod) updates.treatmentPeriod = actionableData.treatmentPeriod;
 if (actionableData.followUpPeriod) updates.followUpPeriod = actionableData.followUpPeriod;
 if (actionableData.totalDuration) updates.totalDuration = actionableData.totalDuration;
 if (actionableData.durationWithDates) updates.durationWithDates = actionableData.durationWithDates;
 toast.success("Study timeline updated");
 store.incrementInsightsCounter();
 }
 }
 
 if (field === "visit-schedule") {
 // Handle entire visit schedule
 if (actionableData.field === 'visits' && actionableData.visits) {
 updates.visits = [...formData.visits, ...actionableData.visits];
 toast.success("Visit schedule updated");
 store.incrementInsightsCounter();
 }
 // Handle single visit addition
 if (actionableData.field === 'singleVisit' && actionableData.value) {
 const newVisit = actionableData.value;
 // Check if visit already exists
 const exists = formData.visits.some(v => 
 v.name === newVisit.name && v.timepoint === newVisit.timepoint
 );
 if (!exists) {
 updates.visits = [...formData.visits, newVisit];
 toast.success(`Added visit: ${newVisit.name}`);
 store.incrementInsightsCounter();
 } else {
 toast.info(`Visit ${newVisit.name} already exists`);
 }
 }
 }
 
 if (field === "intervention-details") {
 // Handle trial intervention details
 if (actionableData.field === 'trialInterventionDetails' && actionableData.value) {
 updates.trialInterventionDetails = actionableData.value;
 toast.success("Trial intervention details updated");
 store.incrementInsightsCounter();
 }
 // Handle study events and activities
 if (actionableData.field === 'studyEventsAndActivities' && actionableData.value) {
 updates.studyEventsAndActivities = actionableData.value;
 toast.success("Study events and activities updated");
 store.incrementInsightsCounter();
 }
 }
 
 if (field === "laboratory-studies") {
 // Handle specimen collection
 if (actionableData.field === 'biologicalSpecimens' && actionableData.specimens) {
 updates.biologicalSpecimens = [...new Set([...formData.biologicalSpecimens, ...actionableData.specimens])];
 updates.willCollectBiologicalSamples = "Yes";
 // Also apply processing requirements if provided
 if (actionableData.collectionAndProcessing) {
 updates.collectionAndProcessing = actionableData.collectionAndProcessing;
 }
 toast.success("Biological specimens and processing requirements updated");
 store.incrementInsightsCounter();
 }
 // Handle single specimen addition
 if (actionableData.field === 'singleSpecimen' && actionableData.value) {
 if (!formData.biologicalSpecimens.includes(actionableData.value)) {
 updates.biologicalSpecimens = [...formData.biologicalSpecimens, actionableData.value];
 updates.willCollectBiologicalSamples = "Yes";
 toast.success(`Added specimen: ${actionableData.value}`);
 store.incrementInsightsCounter();
 } else {
 toast.info(`Specimen ${actionableData.value} already added`);
 }
 }
 // Handle specialized studies (PK, biomarkers, etc)
 if (actionableData.willConductPK !== undefined) {
 updates.willConductPK = actionableData.willConductPK;
 }
 if (actionableData.willConductBiomarker !== undefined) {
 updates.willConductBiomarker = actionableData.willConductBiomarker;
 }
 if (actionableData.willConductImmunogenicity !== undefined) {
 updates.willConductImmunogenicity = actionableData.willConductImmunogenicity;
 }
 if (actionableData.willConductGeneticTesting !== undefined) {
 updates.willConductGeneticTesting = actionableData.willConductGeneticTesting;
 }
 if (Object.keys(updates).some(k => k.startsWith('willConduct'))) {
 toast.success("Laboratory studies updated");
 store.incrementInsightsCounter();
 }
 }
 
 if (field === "operational-strategy") {
 // Handle recruitment metrics
 if (actionableData.recruitmentRate) {
 updates.recruitmentRate = actionableData.recruitmentRate;
 }
 if (actionableData.screenFailureRate) {
 updates.screenFailureRate = actionableData.screenFailureRate;
 }
 if (actionableData.dropoutRate) {
 updates.dropoutRate = actionableData.dropoutRate;
 }
 if (Object.keys(updates).some(k => k.includes('Rate'))) {
 toast.success("Recruitment metrics updated");
 store.incrementInsightsCounter();
 }
 
 // Handle EDC/CTMS
 if (actionableData.field === 'edcCTMSName') {
 updates.edcCTMSName = actionableData.value;
 if (actionableData.dataManagementSystem) {
 updates.dataManagementSystem = actionableData.dataManagementSystem;
 }
 toast.success("Data management system updated");
 store.incrementInsightsCounter();
 }
 
 // Handle monitoring approach
 if (actionableData.field === 'monitoringApproach') {
 updates.monitoringApproach = actionableData.value;
 toast.success("Monitoring approach updated");
 store.incrementInsightsCounter();
 }
 }
 
 if (Object.keys(updates).length > 0) {
 setFormData(prev => ({ ...prev, ...updates }));
 }
 }
 };

 return (
 <div className="space-y-8">
 <BlurFade delay={0.01} inView>
 <div>
 <h1 className="text-3xl font-bold text-gray-900">
 Study Procedures & Operations
 </h1>
 <p className="mt-2 text-lg text-gray-600">
 Define study timeline, procedures, laboratory studies, and operational framework
 </p>
 </div>
 </BlurFade>

 {/* Study Timeline & Duration */}
 <BlurFade delay={0.02} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <Calendar className="h-5 w-5" />
 Study Timeline & Duration
 </CardTitle>
 <CardDescription>
 Define study phases and overall duration with specific dates
 </CardDescription>
 </div>
 <InsightsButton
 onClick={() => handleGetInsights("study-timeline")}
 onRefresh={() => handleGetInsights("study-timeline", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "study-timeline"}
 hasCachedData={!!cachedInsights["study-timeline"]}
 showRefresh={!!cachedInsights["study-timeline"]}
 />
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="grid grid-cols-2 gap-4">
 <div className="space-y-2">
 <Label htmlFor="screeningPeriod">Screening Period *</Label>
 <Input
 id="screeningPeriod"
 placeholder="e.g., 4 weeks, 30 days"
 value={formData.screeningPeriod}
 onChange={(e) => {
 setFormData({ ...formData, screeningPeriod: e.target.value });
 if (fieldErrors.screeningPeriod) {
 setFieldErrors(prev => ({ ...prev, screeningPeriod: false }));
 }
 }}
 className={fieldErrors.screeningPeriod ? "border-red-500 focus:ring-red-500" : ""}
 />
 </div>
 <div className="space-y-2">
 <Label htmlFor="baselinePeriod">Baseline Period *</Label>
 <Input
 id="baselinePeriod"
 placeholder="e.g., 1 week, Day -7 to Day 0"
 value={formData.baselinePeriod}
 onChange={(e) => {
 setFormData({ ...formData, baselinePeriod: e.target.value });
 if (fieldErrors.baselinePeriod) {
 setFieldErrors(prev => ({ ...prev, baselinePeriod: false }));
 }
 }}
 className={fieldErrors.baselinePeriod ? "border-red-500 focus:ring-red-500" : ""}
 />
 </div>
 </div>

 <div className="grid grid-cols-2 gap-4">
 <div className="space-y-2">
 <Label htmlFor="treatmentPeriod">Treatment Period *</Label>
 <Input
 id="treatmentPeriod"
 placeholder="e.g., 24 weeks, 6 months"
 value={formData.treatmentPeriod}
 onChange={(e) => {
 setFormData({ ...formData, treatmentPeriod: e.target.value });
 if (fieldErrors.treatmentPeriod) {
 setFieldErrors(prev => ({ ...prev, treatmentPeriod: false }));
 }
 }}
 className={fieldErrors.treatmentPeriod ? "border-red-500 focus:ring-red-500" : ""}
 />
 </div>
 <div className="space-y-2">
 <Label htmlFor="followUpPeriod">Follow-up Period *</Label>
 <Input
 id="followUpPeriod"
 placeholder="e.g., 4 weeks, 30 days post-treatment"
 value={formData.followUpPeriod}
 onChange={(e) => {
 setFormData({ ...formData, followUpPeriod: e.target.value });
 if (fieldErrors.followUpPeriod) {
 setFieldErrors(prev => ({ ...prev, followUpPeriod: false }));
 }
 }}
 className={fieldErrors.followUpPeriod ? "border-red-500 focus:ring-red-500" : ""}
 />
 </div>
 </div>

 <div className="grid grid-cols-2 gap-4">
 <div className="space-y-2">
 <Label htmlFor="totalDuration">Total Study Duration *</Label>
 <Input
 id="totalDuration"
 placeholder="e.g., 32 weeks, 8 months"
 value={formData.totalDuration}
 onChange={(e) => {
 setFormData({ ...formData, totalDuration: e.target.value });
 if (fieldErrors.totalDuration) {
 setFieldErrors(prev => ({ ...prev, totalDuration: false }));
 }
 }}
 className={fieldErrors.totalDuration ? "border-red-500 focus:ring-red-500" : ""}
 />
 </div>
 <div className="space-y-2">
 <Label htmlFor="durationWithDates">Duration with Specific Dates *</Label>
 <Input
 id="durationWithDates"
 placeholder="e.g., Jan 2024 - Sep 2024"
 value={formData.durationWithDates}
 onChange={(e) => {
 setFormData({ ...formData, durationWithDates: e.target.value });
 if (fieldErrors.durationWithDates) {
 setFieldErrors(prev => ({ ...prev, durationWithDates: false }));
 }
 }}
 className={fieldErrors.durationWithDates ? "border-red-500 focus:ring-red-500" : ""}
 />
 </div>
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Trial Intervention & Study Events */}
 <BlurFade delay={0.03} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <Activity className="h-5 w-5" />
 Trial Intervention & Study Events
 </CardTitle>
 <CardDescription>
 Provide detailed descriptions of interventions and study activities
 </CardDescription>
 </div>
 <InsightsButton
 onClick={() => handleGetInsights("intervention-details")}
 onRefresh={() => handleGetInsights("intervention-details", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "intervention-details"}
 hasCachedData={!!cachedInsights["intervention-details"]}
 showRefresh={!!cachedInsights["intervention-details"]}
 />
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="space-y-2">
 <Label htmlFor="trialInterventionDetails">Describe the Trial Intervention *</Label>
 <Textarea
 id="trialInterventionDetails"
 placeholder="Provide detailed intervention description including dosing, administration route, frequency, packaging, supply chain, etc..."
 rows={4}
 value={formData.trialInterventionDetails}
 onChange={(e) => {
 setFormData({ ...formData, trialInterventionDetails: e.target.value });
 if (fieldErrors.trialInterventionDetails) {
 setFieldErrors(prev => ({ ...prev, trialInterventionDetails: false }));
 }
 }}
 className={fieldErrors.trialInterventionDetails ? "border-red-500 focus:ring-red-500" : ""}
 />
 </div>

 <div className="space-y-2">
 <Label htmlFor="studyEventsAndActivities">Describe Study Events and Activities *</Label>
 <Textarea
 id="studyEventsAndActivities"
 placeholder="Detail all study events, assessments, procedures, and activities that will occur during the study..."
 rows={5}
 value={formData.studyEventsAndActivities}
 onChange={(e) => {
 setFormData({ ...formData, studyEventsAndActivities: e.target.value });
 if (fieldErrors.studyEventsAndActivities) {
 setFieldErrors(prev => ({ ...prev, studyEventsAndActivities: false }));
 }
 }}
 className={fieldErrors.studyEventsAndActivities ? "border-red-500 focus:ring-red-500" : ""}
 />
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Visit Schedule */}
 <BlurFade delay={0.04} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <Clock className="h-5 w-5" />
 Visit Schedule & Procedures
 </CardTitle>
 <CardDescription>
 Define the detailed visit schedule with specific procedures for each timepoint
 </CardDescription>
 </div>
 <InsightsButton
 onClick={() => handleGetInsights("visit-schedule")}
 onRefresh={() => handleGetInsights("visit-schedule", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "visit-schedule"}
 hasCachedData={!!cachedInsights["visit-schedule"]}
 showRefresh={!!cachedInsights["visit-schedule"]}
 />
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 {/* Quick Add Templates */}
 <div className="rounded-lg bg-blue-50 p-4 space-y-3">
 <div className="flex items-center gap-2">
 <Info className="h-4 w-4 text-blue-600" />
 <h4 className="text-sm font-medium text-blue-900">Quick Add Visit Templates</h4>
 </div>
 <div className="flex flex-wrap gap-2">
 <Button
 type="button"
 variant="outline"
 size="sm"
 onClick={() => addVisitTemplate('screening')}
 className="text-xs"
 >
 <Plus className="h-3 w-3 mr-1" />
 Screening Visit
 </Button>
 <Button
 type="button"
 variant="outline"
 size="sm"
 onClick={() => addVisitTemplate('baseline')}
 className="text-xs"
 >
 <Plus className="h-3 w-3 mr-1" />
 Baseline Visit
 </Button>
 <Button
 type="button"
 variant="outline"
 size="sm"
 onClick={() => addVisitTemplate('followUp')}
 className="text-xs"
 >
 <Plus className="h-3 w-3 mr-1" />
 Follow-up Visit
 </Button>
 <Button
 type="button"
 variant="outline"
 size="sm"
 onClick={() => addVisitTemplate('endOfTreatment')}
 className="text-xs"
 >
 <Plus className="h-3 w-3 mr-1" />
 End of Treatment
 </Button>
 <Button
 type="button"
 variant="outline"
 size="sm"
 onClick={() => addVisitTemplate('safetyFollowUp')}
 className="text-xs"
 >
 <Plus className="h-3 w-3 mr-1" />
 Safety Follow-up
 </Button>
 </div>
 </div>

 {/* Existing Visits */}
 {formData.visits.map((visit, index) => (
 <div 
 key={index} 
 className={cn(
 "rounded-lg border p-4 transition-all",
 visit.critical && "border-amber-300 bg-amber-50/50"
 )}
 >
 <div className="flex items-center justify-between mb-2">
 <div className="flex items-center gap-2">
 <h4 className="font-medium">{visit.name}</h4>
 <Badge 
 variant={visit.timepoint.includes("±") ? "secondary" : "outline"}
 className="text-xs"
 >
 {visit.timepoint}
 </Badge>
 {visit.critical && (
 <Badge className="bg-amber-500 hover:bg-amber-600 text-xs">
 <AlertTriangle className="h-3 w-3 mr-1" />
 Critical
 </Badge>
 )}
 </div>
 <Button
 size="icon"
 variant="ghost"
 onClick={() => removeVisit(index)}
 >
 <X className="h-4 w-4" />
 </Button>
 </div>
 <div className="text-sm text-muted-foreground">
 <div className="flex flex-wrap gap-1 mt-2">
 {visit.procedures.map((proc, pIdx) => (
 <Badge key={pIdx} variant="outline" className="text-xs">
 {proc}
 </Badge>
 ))}
 </div>
 </div>
 </div>
 ))}

 {/* Add New Visit */}
 <div className="rounded-lg border-2 border-dashed p-4 space-y-3">
 <h4 className="font-medium">Add Visit</h4>
 <div className="grid grid-cols-2 gap-2">
 <Input
 placeholder="Visit name (e.g., Baseline, Week 4)"
 value={newVisit.name}
 onChange={(e) => setNewVisit(prev => ({ ...prev, name: e.target.value }))}
 />
 <Input
 placeholder="Timepoint (e.g., Day 0, Week 12)"
 value={newVisit.timepoint}
 onChange={(e) => setNewVisit(prev => ({ ...prev, timepoint: e.target.value }))}
 />
 </div>
 
 <div className="space-y-2">
 <Label>Procedures</Label>
 
 {/* Common Procedures Quick Add */}
 <div className="p-3 bg-gray-50 rounded-lg space-y-2">
 <p className="text-xs text-muted-foreground mb-2">Quick add common procedures:</p>
 <div className="flex flex-wrap gap-1">
 {[
 "Vital signs",
 "Physical exam",
 "Laboratory tests",
 "ECG",
 "Drug dispensing",
 "Adverse event assessment",
 "Efficacy assessment",
 "Drug compliance check",
 "PK sampling",
 "Quality of life questionnaire"
 ].map((proc) => (
 <Button
 key={proc}
 type="button"
 variant="outline"
 size="sm"
 className="h-7 text-xs"
 onClick={() => {
 // Add procedure if not already present
 if (!newVisit.procedures.includes(proc)) {
 const emptyIndex = newVisit.procedures.findIndex(p => !p.trim());
 if (emptyIndex >= 0) {
 // Replace empty slot
 updateVisitProcedure(emptyIndex, proc);
 } else {
 // Add new slot
 setNewVisit(prev => ({ ...prev, procedures: [...prev.procedures, proc] }));
 }
 }
 }}
 disabled={newVisit.procedures.includes(proc)}
 >
 {newVisit.procedures.includes(proc) ? (
 <>✓ {proc}</>
 ) : (
 <>+ {proc}</>
 )}
 </Button>
 ))}
 </div>
 </div>
 
 {/* Manual Procedure Input */}
 {newVisit.procedures.map((procedure, pIndex) => (
 <div key={pIndex} className="flex gap-2">
 <Input
 placeholder="Custom procedure..."
 value={procedure}
 onChange={(e) => updateVisitProcedure(pIndex, e.target.value)}
 />
 {pIndex > 0 && (
 <Button
 size="icon"
 variant="ghost"
 onClick={() => removeVisitProcedure(pIndex)}
 >
 <X className="h-4 w-4" />
 </Button>
 )}
 </div>
 ))}
 <Button 
 type="button" 
 variant="outline" 
 size="sm"
 onClick={addVisitProcedure}
 >
 <Plus className="h-4 w-4 mr-2" />
 Add Custom Procedure
 </Button>
 </div>
 
 <div className="flex items-center gap-3">
 <Label htmlFor="critical" className="text-sm font-medium">
 Critical Visit?
 </Label>
 <Select
 value={newVisit.critical}
 onValueChange={(value) => setNewVisit(prev => ({ ...prev, critical: value }))}
 >
 <SelectTrigger id="critical" className="w-24">
 <SelectValue />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="Yes">Yes</SelectItem>
 <SelectItem value="No">No</SelectItem>
 </SelectContent>
 </Select>
 <p className="text-xs text-muted-foreground">
 Critical visits are primary endpoints or safety assessments
 </p>
 </div>
 
 <Button onClick={addVisit} className="w-full">
 Add Visit
 </Button>
 </div>
 
 <p className="text-xs text-muted-foreground">
 {formData.visits.length} visits defined, {formData.visits.filter(v => v.critical).length} critical
 </p>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Laboratory & Biomarker Studies */}
 <BlurFade delay={0.05} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <TestTube className="h-5 w-5" />
 Laboratory & Biomarker Studies
 </CardTitle>
 <CardDescription>
 Define biological sample collection and specialized laboratory studies
 </CardDescription>
 </div>
 <InsightsButton
 onClick={() => handleGetInsights("laboratory-studies")}
 onRefresh={() => handleGetInsights("laboratory-studies", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "laboratory-studies"}
 hasCachedData={!!cachedInsights["laboratory-studies"]}
 showRefresh={!!cachedInsights["laboratory-studies"]}
 />
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="space-y-2">
 <Label htmlFor="willCollectBiologicalSamples" className="text-base">
 Will any biological samples be collected and used in this research? *
 </Label>
 <Select
 value={formData.willCollectBiologicalSamples}
 onValueChange={(value) => {
 setFormData({ ...formData, willCollectBiologicalSamples: value });
 if (fieldErrors.willCollectBiologicalSamples) {
 setFieldErrors(prev => ({ ...prev, willCollectBiologicalSamples: false }));
 }
 }}
 >
 <SelectTrigger id="willCollectBiologicalSamples" className={cn("w-32", fieldErrors.willCollectBiologicalSamples && "border-red-500 focus:ring-red-500")}>
 <SelectValue placeholder="Select Yes or No" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="Yes">Yes</SelectItem>
 <SelectItem value="No">No</SelectItem>
 </SelectContent>
 </Select>
 </div>

 {formData.willCollectBiologicalSamples === "Yes" && (
 <div className="space-y-4">
 <div className="rounded-lg bg-blue-50 p-4">
 <div className="flex items-center gap-2 mb-2">
 <Info className="h-4 w-4 text-blue-600" />
 <h4 className="font-medium text-blue-900 ">Biological Sample Collection Active</h4>
 </div>
 <p className="text-sm text-blue-800 ">
 Please specify the specimens that will be collected and their processing requirements.
 </p>
 </div>

 {/* Biological Specimens */}
 <div className="space-y-3">
 <Label>Select All Biological Specimens to be Used</Label>
 
 {/* Quick Add Common Specimens */}
 <div className="p-3 bg-gray-50 rounded-lg space-y-2">
 <p className="text-xs text-muted-foreground mb-2">Quick add common specimens:</p>
 <div className="flex flex-wrap gap-1">
 {[
 "Blood",
 "Serum",
 "Plasma", 
 "Urine",
 "Saliva",
 "Stool",
 "Tissue biopsy",
 "CSF",
 "Sputum",
 "Nasal swab"
 ].map((spec) => (
 <Button
 key={spec}
 type="button"
 variant="outline"
 size="sm"
 className="h-7 text-xs"
 onClick={() => {
 if (!formData.biologicalSpecimens.includes(spec)) {
 setFormData(prev => ({
 ...prev,
 biologicalSpecimens: [...prev.biologicalSpecimens, spec]
 }));
 toast.success(`Added ${spec}`);
 }
 }}
 disabled={formData.biologicalSpecimens.includes(spec)}
 >
 {formData.biologicalSpecimens.includes(spec) ? (
 <>✓ {spec}</>
 ) : (
 <>+ {spec}</>
 )}
 </Button>
 ))}
 </div>
 </div>
 
 {/* Specimen List */}
 {formData.biologicalSpecimens.map((specimen, index) => (
 <div key={index} className="flex items-center gap-2 rounded-lg border p-3 bg-white">
 <Microscope className="h-4 w-4 text-blue-500" />
 <span className="flex-1 text-sm font-medium">{specimen}</span>
 <Button
 size="icon"
 variant="ghost"
 onClick={() => removeSpecimen(index)}
 >
 <X className="h-4 w-4" />
 </Button>
 </div>
 ))}
 
 {/* Custom Specimen Input */}
 <div className="flex gap-2">
 <Input
 placeholder="Add custom specimen type..."
 value={newSpecimen}
 onChange={(e) => setNewSpecimen(e.target.value)}
 onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addSpecimen())}
 />
 <Button onClick={addSpecimen} size="icon">
 <Plus className="h-4 w-4" />
 </Button>
 </div>
 </div>

 <div className="space-y-2">
 <Label htmlFor="collectionAndProcessing">Scientifically Detail Collection and Processing</Label>
 <Textarea
 id="collectionAndProcessing"
 placeholder="Describe collection procedures, processing requirements, storage conditions, shipment protocols, analysis plans, etc..."
 rows={4}
 value={formData.collectionAndProcessing}
 onChange={(e) => {
 setFormData({ ...formData, collectionAndProcessing: e.target.value });
 if (fieldErrors.collectionAndProcessing) {
 setFieldErrors(prev => ({ ...prev, collectionAndProcessing: false }));
 }
 }}
 className={fieldErrors.collectionAndProcessing ? "border-red-500 focus:ring-red-500" : ""}
 />
 </div>

 {/* Specialized Studies */}
 <div className="space-y-3">
 <Label className="text-base">Specialized Laboratory Studies</Label>
 
 {/* PK Studies */}
 <div className="rounded-lg border p-4 space-y-3">
 <div className="flex items-start justify-between gap-4">
 <div className="flex-1 space-y-2">
 <div className="flex items-center gap-2">
 <Activity className="h-4 w-4 text-purple-500" />
 <Label className="text-sm font-medium">
 Pharmacokinetics (PK) Studies
 </Label>
 </div>
 <p className="text-xs text-muted-foreground">
 Measure drug concentration in blood/plasma over time to understand absorption, distribution, metabolism, and excretion
 </p>
 </div>
 <div className="space-y-1">
 <Label htmlFor="willConductPK" className="text-xs text-muted-foreground">
 Will conduct?
 </Label>
 <Select
 value={formData.willConductPK}
 onValueChange={(value) => {
 setFormData({ ...formData, willConductPK: value });
 if (fieldErrors.willConductPK) {
 setFieldErrors(prev => ({ ...prev, willConductPK: false }));
 }
 }}
 >
 <SelectTrigger id="willConductPK" className={cn("w-24", fieldErrors.willConductPK && "border-red-500 focus:ring-red-500")}>
 <SelectValue placeholder="Select" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="Yes">Yes</SelectItem>
 <SelectItem value="No">No</SelectItem>
 </SelectContent>
 </Select>
 </div>
 </div>
 </div>
 
 {/* Biomarker Testing */}
 <div className="rounded-lg border p-4 space-y-3">
 <div className="flex items-start justify-between gap-4">
 <div className="flex-1 space-y-2">
 <div className="flex items-center gap-2">
 <Activity className="h-4 w-4 text-green-500" />
 <Label className="text-sm font-medium">
 Biomarker Testing
 </Label>
 </div>
 <p className="text-xs text-muted-foreground">
 Measure biological markers for efficacy, safety, or patient stratification (e.g., cytokines, antibodies, proteins)
 </p>
 </div>
 <div className="space-y-1">
 <Label htmlFor="willConductBiomarker" className="text-xs text-muted-foreground">
 Will conduct?
 </Label>
 <Select
 value={formData.willConductBiomarker}
 onValueChange={(value) => {
 setFormData({ ...formData, willConductBiomarker: value });
 if (fieldErrors.willConductBiomarker) {
 setFieldErrors(prev => ({ ...prev, willConductBiomarker: false }));
 }
 }}
 >
 <SelectTrigger id="willConductBiomarker" className={cn("w-24", fieldErrors.willConductBiomarker && "border-red-500 focus:ring-red-500")}>
 <SelectValue placeholder="Select" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="Yes">Yes</SelectItem>
 <SelectItem value="No">No</SelectItem>
 </SelectContent>
 </Select>
 </div>
 </div>
 </div>
 
 {/* Immunogenicity */}
 <div className="rounded-lg border p-4 space-y-3">
 <div className="flex items-start justify-between gap-4">
 <div className="flex-1 space-y-2">
 <div className="flex items-center gap-2">
 <Activity className="h-4 w-4 text-orange-500" />
 <Label className="text-sm font-medium">
 Immunogenicity Testing
 </Label>
 </div>
 <p className="text-xs text-muted-foreground">
 Test for anti-drug antibodies (ADAs) or immune responses to biological therapies
 </p>
 </div>
 <div className="space-y-1">
 <Label htmlFor="willConductImmunogenicity" className="text-xs text-muted-foreground">
 Will conduct?
 </Label>
 <Select
 value={formData.willConductImmunogenicity}
 onValueChange={(value) => {
 setFormData({ ...formData, willConductImmunogenicity: value });
 if (fieldErrors.willConductImmunogenicity) {
 setFieldErrors(prev => ({ ...prev, willConductImmunogenicity: false }));
 }
 }}
 >
 <SelectTrigger id="willConductImmunogenicity" className={cn("w-24", fieldErrors.willConductImmunogenicity && "border-red-500 focus:ring-red-500")}>
 <SelectValue placeholder="Select" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="Yes">Yes</SelectItem>
 <SelectItem value="No">No</SelectItem>
 </SelectContent>
 </Select>
 </div>
 </div>
 </div>
 
 {/* Genetic Testing */}
 <div className="rounded-lg border p-4 space-y-3">
 <div className="flex items-start justify-between gap-4">
 <div className="flex-1 space-y-2">
 <div className="flex items-center gap-2">
 <Activity className="h-4 w-4 text-blue-500" />
 <Label className="text-sm font-medium">
 Genetic Testing/Sequencing
 </Label>
 </div>
 <p className="text-xs text-muted-foreground">
 Pharmacogenomics, disease susceptibility markers, or genetic biomarkers for patient selection
 </p>
 </div>
 <div className="space-y-1">
 <Label htmlFor="willConductGeneticTesting" className="text-xs text-muted-foreground">
 Will conduct?
 </Label>
 <Select
 value={formData.willConductGeneticTesting}
 onValueChange={(value) => {
 setFormData({ ...formData, willConductGeneticTesting: value });
 if (fieldErrors.willConductGeneticTesting) {
 setFieldErrors(prev => ({ ...prev, willConductGeneticTesting: false }));
 }
 }}
 >
 <SelectTrigger id="willConductGeneticTesting" className={cn("w-24", fieldErrors.willConductGeneticTesting && "border-red-500 focus:ring-red-500")}>
 <SelectValue placeholder="Select" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="Yes">Yes</SelectItem>
 <SelectItem value="No">No</SelectItem>
 </SelectContent>
 </Select>
 </div>
 </div>
 </div>
 </div>
 </div>
 )}
 </CardContent>
 </Card>
 </BlurFade>

 {/* Operational Strategy */}
 <BlurFade delay={0.06} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <Building2 className="h-5 w-5" />
 Operational Strategy
 </CardTitle>
 <CardDescription>
 Define recruitment strategy, data management, and monitoring approach
 </CardDescription>
 </div>
 <InsightsButton
 onClick={() => handleGetInsights("operational-strategy")}
 onRefresh={() => handleGetInsights("operational-strategy", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "operational-strategy"}
 hasCachedData={!!cachedInsights["operational-strategy"]}
 showRefresh={!!cachedInsights["operational-strategy"]}
 />
 </div>
 </CardHeader>
 <CardContent className="space-y-6">
 {/* Recruitment Metrics Section */}
 <div className="rounded-lg border p-4 space-y-4">
 <div className="flex items-center gap-2 mb-2">
 <TrendingUp className="h-4 w-4 text-blue-500" />
 <h4 className="font-medium">Recruitment Metrics</h4>
 </div>
 <div className="grid grid-cols-3 gap-4">
 <div className="space-y-2">
 <Label htmlFor="recruitmentRate">
 Expected Recruitment Rate *
 <span className="text-xs text-muted-foreground ml-1">(per site)</span>
 </Label>
 <Input
 id="recruitmentRate"
 placeholder="e.g., 10 participants/month"
 value={formData.recruitmentRate}
 onChange={(e) => {
 setFormData({ ...formData, recruitmentRate: e.target.value });
 if (fieldErrors.recruitmentRate) {
 setFieldErrors(prev => ({ ...prev, recruitmentRate: false }));
 }
 }}
 className={fieldErrors.recruitmentRate ? "border-red-500 focus:ring-red-500" : ""}
 />
 </div>
 <div className="space-y-2">
 <Label htmlFor="screenFailureRate">
 Screen Failure Rate *
 <span className="text-xs text-muted-foreground ml-1">(%)</span>
 </Label>
 <Input
 id="screenFailureRate"
 placeholder="e.g., 30% or 25-35%"
 value={formData.screenFailureRate}
 onChange={(e) => {
 setFormData({ ...formData, screenFailureRate: e.target.value });
 if (fieldErrors.screenFailureRate) {
 setFieldErrors(prev => ({ ...prev, screenFailureRate: false }));
 }
 }}
 className={fieldErrors.screenFailureRate ? "border-red-500 focus:ring-red-500" : ""}
 />
 </div>
 <div className="space-y-2">
 <Label htmlFor="dropoutRate">
 Expected Dropout Rate *
 <span className="text-xs text-muted-foreground ml-1">(%)</span>
 </Label>
 <Input
 id="dropoutRate"
 placeholder="e.g., 15% or 10-20%"
 value={formData.dropoutRate}
 onChange={(e) => {
 setFormData({ ...formData, dropoutRate: e.target.value });
 if (fieldErrors.dropoutRate) {
 setFieldErrors(prev => ({ ...prev, dropoutRate: false }));
 }
 }}
 className={fieldErrors.dropoutRate ? "border-red-500 focus:ring-red-500" : ""}
 />
 </div>
 </div>
 <p className="text-xs text-muted-foreground">
 These metrics help determine enrollment targets and site requirements
 </p>
 </div>

 {/* Data Management Section */}
 <div className="rounded-lg border p-4 space-y-4">
 <div className="flex items-center gap-2 mb-2">
 <Database className="h-4 w-4 text-green-500" />
 <h4 className="font-medium">Data Management</h4>
 </div>
 <div className="grid grid-cols-2 gap-4">
 <div className="space-y-2">
 <Label htmlFor="dataManagementSystem">Data Management System *</Label>
 <Select
 value={formData.dataManagementSystem}
 onValueChange={(value) => {
 setFormData({ ...formData, dataManagementSystem: value as any });
 if (fieldErrors.dataManagementSystem) {
 setFieldErrors(prev => ({ ...prev, dataManagementSystem: false }));
 }
 }}
 >
 <SelectTrigger id="dataManagementSystem" className={fieldErrors.dataManagementSystem ? "border-red-500 focus:ring-red-500" : ""}>
 <SelectValue placeholder="Select system" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="edc">Electronic Data Capture (EDC)</SelectItem>
 <SelectItem value="paper">Paper-based</SelectItem>
 <SelectItem value="hybrid">Hybrid (EDC + Paper)</SelectItem>
 </SelectContent>
 </Select>
 </div>
 
 {(formData.dataManagementSystem === "edc" || formData.dataManagementSystem === "hybrid") && (
 <div className="space-y-2">
 <Label htmlFor="edcCTMSName">EDC/CTMS Platform</Label>
 <Input
 id="edcCTMSName"
 placeholder="e.g., Medidata Rave, Veeva Vault"
 value={formData.edcCTMSName}
 onChange={(e) => {
 setFormData({ ...formData, edcCTMSName: e.target.value });
 if (fieldErrors.edcCTMSName) {
 setFieldErrors(prev => ({ ...prev, edcCTMSName: false }));
 }
 }}
 className={fieldErrors.edcCTMSName ? "border-red-500 focus:ring-red-500" : ""}
 />
 </div>
 )}
 </div>
 <p className="text-xs text-muted-foreground">
 EDC systems provide real-time data access and built-in quality checks
 </p>
 </div>

 {/* Monitoring Approach Section */}
 <div className="rounded-lg border p-4 space-y-4">
 <div className="flex items-center gap-2 mb-2">
 <Eye className="h-4 w-4 text-purple-500" />
 <h4 className="font-medium">Monitoring Strategy</h4>
 </div>
 <div className="space-y-2">
 <Label htmlFor="monitoringApproach">Monitoring Approach *</Label>
 <Select
 value={formData.monitoringApproach}
 onValueChange={(value) => {
 setFormData({ ...formData, monitoringApproach: value as any });
 if (fieldErrors.monitoringApproach) {
 setFieldErrors(prev => ({ ...prev, monitoringApproach: false }));
 }
 }}
 >
 <SelectTrigger id="monitoringApproach" className={fieldErrors.monitoringApproach ? "border-red-500 focus:ring-red-500" : ""}>
 <SelectValue placeholder="Select approach" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="on-site">On-site Monitoring</SelectItem>
 <SelectItem value="remote">Remote Monitoring</SelectItem>
 <SelectItem value="risk-based">Risk-based Monitoring</SelectItem>
 <SelectItem value="hybrid">Hybrid Approach</SelectItem>
 </SelectContent>
 </Select>
 </div>
 <p className="text-xs text-muted-foreground">
 Risk-based monitoring focuses resources on critical data and processes
 </p>
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Navigation */}
 <BlurFade delay={0.07} inView>
 <div className="flex justify-between pt-6">
 <Button
 variant="outline"
 onClick={() => router.push("/study/new/safety-assessment")}
 >
 <ChevronLeft className="mr-2 h-4 w-4" />
 Back to Safety Assessment
 </Button>
 <Button
 onClick={handleContinue}
 disabled={saveDiscovery.isPending}
 >
 {saveDiscovery.isPending ? "Saving..." : "Continue to Regulatory, Financial & Legal"}
 </Button>
 </div>
 </BlurFade>

 {/* Insights Panel */}
 {activeInsightsPanel && (
 <InsightsPanelPortal
 isOpen={true}
 onClose={() => setActiveInsightsPanel(null)}
 title={`Insights: ${activeInsightsPanel.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase())}`}
 description="Recommendations based on similar studies"
 loading={queryInsights.isPending}
 sections={insightsData[activeInsightsPanel]?.sections || cachedInsights[activeInsightsPanel]?.sections || []}
 sources={insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || []}
 progressStatus={insightsData[activeInsightsPanel]?.progressStatus}
 progressMessages={insightsData[activeInsightsPanel]?.progressMessages}
 onDocumentClick={(url) => setDocumentViewerUrl(url)}
 onApplySuggestion={(suggestion, actionableData) => handleApplySuggestion(activeInsightsPanel, suggestion, actionableData)}
 />
 )}

 {/* Document Viewer */}
 {documentViewerUrl && (
 <DocumentViewerPortal
 citations={(() => {
 // Extract all unique citations from all sections
 const allCitations = new Map<string, any>();
 Object.values(insightsData).forEach(data => {
 data.sections?.forEach((section: any) => {
 if (section.citations && Array.isArray(section.citations)) {
 section.citations.forEach((citation: any) => {
 if (citation.id && !allCitations.has(citation.id)) {
 allCitations.set(citation.id, citation);
 }
 });
 }
 });
 });
 return Array.from(allCitations.values());
 })()}
 sources={(() => {
 // Extract all unique sources from all insights data
 const allSources = new Map<string, any>();
 Object.values(insightsData).forEach(data => {
 data.sources?.forEach((source: any) => {
 if (source.nctId && !allSources.has(source.nctId)) {
 allSources.set(source.nctId, source);
 }
 });
 });
 return Array.from(allSources.values());
 })()}
 onStudySelect={(studyUrl) => setDocumentViewerUrl(studyUrl)}
 currentStudyId={(() => {
 // Extract NCT ID from the current document URL
 if (!documentViewerUrl) return undefined;
 const match = documentViewerUrl.match(/NCT\\d+/i);
 return match ? match[0] : undefined;
 })()}
 isOpen={true}
 onClose={() => setDocumentViewerUrl(null)}
 documentUrl={documentViewerUrl}
 loading={false}
 />
 )}
 </div>
 );
}