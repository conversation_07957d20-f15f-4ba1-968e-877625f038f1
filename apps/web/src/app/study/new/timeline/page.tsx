"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Switch } from "~/components/ui/switch";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { InsightsButton } from "~/components/insights/InsightsButton";
import { InsightsPanelPortal } from "~/components/insights/InsightsPanelPortal";
import { DocumentViewerPortal } from "~/components/insights/DocumentViewerPortal";
import { SourceStudiesViewerPortal } from "~/components/insights/SourceStudiesViewerPortal";

import { api } from "~/trpc/react";
import { toast } from "sonner";
import { 
 ChevronLeft, 
 Calendar, 
 Clock, 
 Plus, 
 X, 
 CheckCircle2,
 AlertCircle,
 ClipboardList,
 Activity
} from "lucide-react";

interface Visit {
 name: string;
 timepoint: string;
 procedures: string[];
 critical?: boolean;
}

export default function TimelinePage() {
 const router = useRouter();
 const store = useTrialDesignStore();
 
 const [formData, setFormData] = useState({
 screeningPeriod: store.discovery.timeline?.screeningPeriod || "2 weeks",
 baselinePeriod: store.discovery.timeline?.baselinePeriod || "1 week",
 treatmentPeriod: store.discovery.objectives?.studyDuration || "12 weeks",
 followUpPeriod: store.discovery.objectives?.followUpPeriod || "4 weeks",
 totalDuration: store.discovery.timeline?.totalDuration || "",
 visits: store.discovery.timeline?.visits || [
 { name: "Screening", timepoint: "Day -14 to -1", procedures: ["Informed consent", "Medical history", "Physical exam"], critical: true },
 { name: "Baseline", timepoint: "Day 0", procedures: ["Randomization", "First dose", "Baseline assessments"], critical: true },
 { name: "Week 4", timepoint: "Day 28 ± 3", procedures: ["Safety assessments", "Drug dispensing"], critical: false },
 { name: "Week 8", timepoint: "Day 56 ± 3", procedures: ["Efficacy assessments", "Safety labs"], critical: false },
 { name: "Week 12 (EOT)", timepoint: "Day 84 ± 3", procedures: ["Primary endpoint", "Final assessments"], critical: true },
 { name: "Follow-up", timepoint: "Day 112 ± 7", procedures: ["Safety follow-up", "AE assessment"], critical: false },
 ] as Visit[],
 });

 const [newVisit, setNewVisit] = useState<Partial<Visit>>({
 name: "",
 timepoint: "",
 procedures: [],
 critical: false,
 });
 const [newProcedure, setNewProcedure] = useState("");
 const [editingVisitIndex, setEditingVisitIndex] = useState<number | null>(null);
 const [activeInsightsPanel, setActiveInsightsPanel] = useState<string | null>(null);
 const [insightsData, setInsightsData] = useState<Record<string, { sections: any[]; sources?: any[]; progressStatus?: string; progressMessages?: string[] }>>({});
 const [documentViewerUrl, setDocumentViewerUrl] = useState<string | null>(null);
 const [sourceStudiesViewerOpen, setSourceStudiesViewerOpen] = useState(false);
 const [sourceStudiesViewerSources, setSourceStudiesViewerSources] = useState<any[]>([]);
 
 const cachedInsights = store.insightsCache || {};

 const queryInsights = api.knowledgeBase.queryInsights.useMutation({
 onSuccess: (data, variables) => {
 const insightsPayload = {
 sections: data.sections || [],
 sources: data.sources || [],
 progressStatus: undefined, // Clear progress status when real data arrives
 progressMessages: [],
 };
 
 setInsightsData(prev => ({
 ...prev,
 [variables.field]: insightsPayload
 }));
 
 store.cacheInsights(variables.field, insightsPayload);
 setActiveInsightsPanel(variables.field);
 },
 onError: (error) => {
 toast.error("Failed to get insights: " + error.message);
 // Clear progress status on error
 if (error && typeof error === 'object' && 'data' in error) {
 const errorData = error as any;
 const field = errorData.data?.field;
 if (field) {
 setInsightsData(prev => ({
 ...prev,
 [field]: {
 sections: [],
 sources: [],
 progressStatus: undefined,
 progressMessages: [],
 }
 }));
 }
 }
 },
 });

 const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
 onSuccess: () => {
 store.markStepCompleted("timeline");
 router.push("/study/new/operational");
 },
 onError: (error) => {
 toast.error("Failed to save: " + error.message);
 },
 });

 const handleGetInsights = async (field: string, forceRefresh = false) => {
 // Check cache first unless forcing refresh
 if (!forceRefresh && cachedInsights[field]) {
 setActiveInsightsPanel(field);
 return;
 }
 
 // Show panel immediately with loading state
 setActiveInsightsPanel(field);
 
 // Set initial progress status
 setInsightsData(prev => ({
 ...prev,
 [field]: {
 sections: [],
 sources: [],
 progressStatus: 'Initializing search...',
 progressMessages: [],
 }
 }));
 
 // Progressive status updates (10-12 second timeline)
 const progressUpdates = [
 { delay: 0, message: 'Initializing search...' },
 { delay: 1500, message: 'Searching knowledge base...' },
 { delay: 3500, message: 'Analyzing timeline patterns...' },
 { delay: 6000, message: 'Extracting visit schedules...' },
 { delay: 8500, message: 'Generating recommendations...' },
 ];
 
 // Update progress status progressively
 progressUpdates.forEach(({ delay, message }) => {
 setTimeout(() => {
 setInsightsData(prev => {
 const current = prev[field];
 // Only update if we're still loading (haven't received results yet)
 if (current && !current.sections?.length) {
 return {
 ...prev,
 [field]: {
 ...current,
 progressStatus: message,
 progressMessages: [...(current.progressMessages || []), message],
 }
 };
 }
 return prev;
 });
 }, delay);
 });
 
 const context = {
 studyType: store.discovery.studyType || undefined,
 condition: store.discovery.condition || undefined,
 phase: store.discovery.phase || undefined,
 primaryEndpoint: store.discovery.objectives?.primaryGoal || undefined,
 };

 const queries: Record<string, string> = {
 "visit-schedule": `What is a typical visit schedule for ${store.discovery.condition || "clinical"} trials in ${store.discovery.phase || "Phase 2/3"}?`,
 "study-periods": `What are typical screening, treatment, and follow-up periods for ${store.discovery.condition || "this condition"}?`,
 };

 await queryInsights.mutateAsync({
 sessionId: store.sessionId!,
 field,
 context,
 query: queries[field] || "",
 });
 };

 const calculateTotalDuration = () => {
 const screening = parseInt(formData.screeningPeriod) || 0;
 const baseline = parseInt(formData.baselinePeriod) || 0;
 const treatment = parseInt(formData.treatmentPeriod) || 0;
 const followUp = parseInt(formData.followUpPeriod) || 0;
 
 const total = screening + baseline + treatment + followUp;
 const unit = formData.treatmentPeriod.includes("month") ? "months" : "weeks";
 
 setFormData(prev => ({
 ...prev,
 totalDuration: `${total} ${unit}`
 }));
 };

 const addVisit = () => {
 if (newVisit.name && newVisit.timepoint) {
 const visit: Visit = {
 name: newVisit.name,
 timepoint: newVisit.timepoint,
 procedures: newVisit.procedures || [],
 critical: newVisit.critical || false,
 };
 
 if (editingVisitIndex !== null) {
 const updatedVisits = [...formData.visits];
 updatedVisits[editingVisitIndex] = visit;
 setFormData({ ...formData, visits: updatedVisits });
 setEditingVisitIndex(null);
 } else {
 setFormData({ ...formData, visits: [...formData.visits, visit] });
 }
 
 setNewVisit({ name: "", timepoint: "", procedures: [], critical: false });
 setNewProcedure("");
 }
 };

 const editVisit = (index: number) => {
 const visit = formData.visits[index];
 setNewVisit(visit);
 setEditingVisitIndex(index);
 };

 const removeVisit = (index: number) => {
 setFormData({
 ...formData,
 visits: formData.visits.filter((_, i) => i !== index),
 });
 };

 const addProcedure = () => {
 if (newProcedure.trim()) {
 setNewVisit({
 ...newVisit,
 procedures: [...(newVisit.procedures || []), newProcedure.trim()],
 });
 setNewProcedure("");
 }
 };

 const removeProcedure = (index: number) => {
 setNewVisit({
 ...newVisit,
 procedures: newVisit.procedures?.filter((_, i) => i !== index) || [],
 });
 };

 const handleContinue = () => {
 if (!store.sessionId) {
 toast.error("Session not found");
 return;
 }

 if (formData.visits.length === 0) {
 toast.error("Please add at least one study visit");
 return;
 }

 store.updateDiscovery({ 
 timeline: formData,
 objectives: {
 ...store.discovery.objectives,
 studyDuration: formData.treatmentPeriod,
 followUpPeriod: formData.followUpPeriod,
 }
 });

 saveDiscovery.mutate({
 sessionId: store.sessionId,
 data: {
 timeline: formData,
 },
 });
 };

 const handleApplySuggestion = (field: string, suggestion: string, actionableData?: any) => {
 if (field === "study-periods") {
 // Use actionableData from JSON response if available
 if (actionableData) {
 const updates: any = {};
 
 if (actionableData.field === 'allPeriods' && actionableData.periods) {
 // Apply all periods at once
 if (actionableData.periods.screening) {
 updates.screeningPeriod = actionableData.periods.screening;
 }
 if (actionableData.periods.baseline) {
 updates.baselinePeriod = actionableData.periods.baseline;
 }
 if (actionableData.periods.treatment) {
 updates.treatmentPeriod = actionableData.periods.treatment;
 }
 if (actionableData.periods.followUp) {
 updates.followUpPeriod = actionableData.periods.followUp;
 }
 
 setFormData(prev => ({ ...prev, ...updates }));
 toast.success("All study periods updated");
 } else if (actionableData.field === 'screeningPeriod') {
 setFormData(prev => ({ ...prev, screeningPeriod: actionableData.value }));
 toast.success("Screening period updated");
 } else if (actionableData.field === 'treatmentPeriod') {
 setFormData(prev => ({ ...prev, treatmentPeriod: actionableData.value }));
 toast.success("Treatment period updated");
 } else if (actionableData.field === 'followUpPeriod') {
 setFormData(prev => ({ ...prev, followUpPeriod: actionableData.value }));
 toast.success("Follow-up period updated");
 }
 
 calculateTotalDuration();
 } else {
 // Fallback to regex parsing
 const screeningMatch = suggestion.match(/screening[:\s]+(\d+\s*(?:weeks?|days?))/i);
 const treatmentMatch = suggestion.match(/treatment[:\s]+(\d+\s*(?:weeks?|months?))/i);
 const followUpMatch = suggestion.match(/follow[\s-]?up[:\s]+(\d+\s*(?:weeks?|months?))/i);
 
 if (screeningMatch) {
 setFormData(prev => ({ ...prev, screeningPeriod: screeningMatch[1] }));
 }
 if (treatmentMatch) {
 setFormData(prev => ({ ...prev, treatmentPeriod: treatmentMatch[1] }));
 }
 if (followUpMatch) {
 setFormData(prev => ({ ...prev, followUpPeriod: followUpMatch[1] }));
 }
 
 toast.success("Study periods updated");
 calculateTotalDuration();
 }
 return;
 }
 
 if (field === "visit-schedule") {
 // Use actionableData from JSON response if available
 if (actionableData) {
 if (actionableData.field === 'visitSchedule' && actionableData.visits) {
 // Replace entire visit schedule
 const newVisits: Visit[] = actionableData.visits.map((v: any) => ({
 name: v.name,
 timepoint: v.timepoint,
 procedures: v.procedures || [],
 critical: v.critical || false
 }));
 
 setFormData(prev => ({ ...prev, visits: newVisits }));
 toast.success(`Visit schedule updated with ${newVisits.length} visits`);
 }
 } else {
 // Fallback: Try to parse visits from text
 toast.info("Unable to apply visit schedule automatically. Please add visits manually.");
 }
 return;
 }
 };

 const handleViewAllSources = () => {
 if (activeInsightsPanel && (insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources)) {
 const sources = insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || [];
 setSourceStudiesViewerSources(sources);
 setSourceStudiesViewerOpen(true);
 }
 };

 return (
 <div className="space-y-8">
 <BlurFade delay={0.01} inView>
 <div>
 <h1 className="text-3xl font-bold text-gray-900">
 Study Timeline & Visits
 </h1>
 <p className="mt-2 text-lg text-gray-600">
 Define your study periods and visit schedule
 </p>
 </div>
 </BlurFade>

 <BlurFade delay={0.02} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <Clock className="h-5 w-5" />
 Study Periods
 </CardTitle>
 <CardDescription>
 Define the duration of each study phase
 </CardDescription>
 </div>
 <InsightsButton
 onClick={() => handleGetInsights("study-periods")}
 onRefresh={() => handleGetInsights("study-periods", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "study-periods"}
 hasCachedData={!!cachedInsights["study-periods"]}
 showRefresh={!!cachedInsights["study-periods"]}
 />
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="grid grid-cols-2 gap-4">
 <div className="space-y-2">
 <Label htmlFor="screeningPeriod">Screening Period</Label>
 <Input
 id="screeningPeriod"
 placeholder="e.g., 2 weeks, 14 days"
 value={formData.screeningPeriod}
 onChange={(e) => setFormData({ ...formData, screeningPeriod: e.target.value })}
 onBlur={calculateTotalDuration}
 />
 </div>
 <div className="space-y-2">
 <Label htmlFor="baselinePeriod">Baseline Period</Label>
 <Input
 id="baselinePeriod"
 placeholder="e.g., 1 week, 7 days"
 value={formData.baselinePeriod}
 onChange={(e) => setFormData({ ...formData, baselinePeriod: e.target.value })}
 onBlur={calculateTotalDuration}
 />
 </div>
 </div>
 <div className="grid grid-cols-2 gap-4">
 <div className="space-y-2">
 <Label htmlFor="treatmentPeriod">Treatment Period</Label>
 <Input
 id="treatmentPeriod"
 placeholder="e.g., 12 weeks, 3 months"
 value={formData.treatmentPeriod}
 onChange={(e) => setFormData({ ...formData, treatmentPeriod: e.target.value })}
 onBlur={calculateTotalDuration}
 />
 </div>
 <div className="space-y-2">
 <Label htmlFor="followUpPeriod">Follow-up Period</Label>
 <Input
 id="followUpPeriod"
 placeholder="e.g., 4 weeks, 1 month"
 value={formData.followUpPeriod}
 onChange={(e) => setFormData({ ...formData, followUpPeriod: e.target.value })}
 onBlur={calculateTotalDuration}
 />
 </div>
 </div>
 {formData.totalDuration && (
 <div className="rounded-lg bg-purple-50 p-4">
 <p className="text-sm font-medium text-purple-900 ">
 Estimated Total Study Duration: {formData.totalDuration}
 </p>
 </div>
 )}
 </CardContent>
 </Card>
 </BlurFade>

 <BlurFade delay={0.03} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <Calendar className="h-5 w-5" />
 Visit Schedule
 </CardTitle>
 <CardDescription>
 Define study visits and procedures
 </CardDescription>
 </div>
 <InsightsButton
 onClick={() => handleGetInsights("visit-schedule")}
 onRefresh={() => handleGetInsights("visit-schedule", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "visit-schedule"}
 hasCachedData={!!cachedInsights["visit-schedule"]}
 showRefresh={!!cachedInsights["visit-schedule"]}
 />
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 {formData.visits.map((visit, index) => (
 <div key={index} className="rounded-lg border p-4 space-y-3">
 <div className="flex items-center justify-between">
 <div className="flex items-center gap-3">
 <div>
 <div className="flex items-center gap-2">
 <span className="font-medium">{visit.name}</span>
 {visit.critical && (
 <Badge variant="destructive" className="text-xs">Critical</Badge>
 )}
 </div>
 <p className="text-sm text-gray-600">{visit.timepoint}</p>
 </div>
 </div>
 <div className="flex gap-2">
 <Button
 size="sm"
 variant="outline"
 onClick={() => editVisit(index)}
 >
 Edit
 </Button>
 <Button
 size="icon"
 variant="ghost"
 onClick={() => removeVisit(index)}
 >
 <X className="h-4 w-4" />
 </Button>
 </div>
 </div>
 {visit.procedures.length > 0 && (
 <div className="flex flex-wrap gap-2">
 {visit.procedures.map((proc, pIndex) => (
 <Badge key={pIndex} variant="secondary">
 <ClipboardList className="mr-1 h-3 w-3" />
 {proc}
 </Badge>
 ))}
 </div>
 )}
 </div>
 ))}
 
 <div className="border-t pt-4 space-y-4">
 <h4 className="font-medium">
 {editingVisitIndex !== null ? "Edit Visit" : "Add New Visit"}
 </h4>
 <div className="grid grid-cols-2 gap-4">
 <div className="space-y-2">
 <Label>Visit Name</Label>
 <Input
 placeholder="e.g., Week 4, Month 3"
 value={newVisit.name}
 onChange={(e) => setNewVisit({ ...newVisit, name: e.target.value })}
 />
 </div>
 <div className="space-y-2">
 <Label>Timepoint</Label>
 <Input
 placeholder="e.g., Day 28 ± 3"
 value={newVisit.timepoint}
 onChange={(e) => setNewVisit({ ...newVisit, timepoint: e.target.value })}
 />
 </div>
 </div>
 
 <div className="space-y-2">
 <Label>Procedures</Label>
 <div className="flex gap-2">
 <Input
 placeholder="Add procedure..."
 value={newProcedure}
 onChange={(e) => setNewProcedure(e.target.value)}
 onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addProcedure())}
 />
 <Button onClick={addProcedure} size="icon">
 <Plus className="h-4 w-4" />
 </Button>
 </div>
 {newVisit.procedures && newVisit.procedures.length > 0 && (
 <div className="flex flex-wrap gap-2 mt-2">
 {newVisit.procedures.map((proc, index) => (
 <Badge key={index} variant="outline">
 {proc}
 <button
 onClick={() => removeProcedure(index)}
 className="ml-2 hover:text-red-500"
 >
 <X className="h-3 w-3" />
 </button>
 </Badge>
 ))}
 </div>
 )}
 </div>
 
 <div className="flex items-center justify-between">
 <div className="flex items-center space-x-2">
 <Switch
 id="critical"
 checked={newVisit.critical}
 onCheckedChange={(checked) => setNewVisit({ ...newVisit, critical: checked })}
 />
 <Label htmlFor="critical" className="font-normal">
 Mark as critical visit
 </Label>
 </div>
 <Button
 onClick={addVisit}
 disabled={!newVisit.name || !newVisit.timepoint}
 >
 {editingVisitIndex !== null ? "Update Visit" : "Add Visit"}
 </Button>
 </div>
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 <BlurFade delay={0.04} inView>
 <div className="flex justify-between pt-6">
 <Button
 variant="outline"
 onClick={() => router.push("/study/new/study-population")}
 >
 <ChevronLeft className="mr-2 h-4 w-4" />
 Back
 </Button>
 <Button
 onClick={handleContinue}
 disabled={saveDiscovery.isPending || formData.visits.length === 0}
 >
 {saveDiscovery.isPending ? "Saving..." : "Continue to Operational Planning"}
 </Button>
 </div>
 </BlurFade>

 {activeInsightsPanel && (
 <InsightsPanelPortal
 isOpen={true}
 onClose={() => setActiveInsightsPanel(null)}
 title={`Insights: ${activeInsightsPanel.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase())}`}
 description="Recommendations based on similar studies"
 loading={queryInsights.isPending}
 sections={insightsData[activeInsightsPanel]?.sections || cachedInsights[activeInsightsPanel]?.sections || []}
 sources={insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || []}
 progressStatus={insightsData[activeInsightsPanel]?.progressStatus}
 progressMessages={insightsData[activeInsightsPanel]?.progressMessages}
 onDocumentClick={(url) => setDocumentViewerUrl(url)}
 onViewAllSources={handleViewAllSources}
 onApplySuggestion={(suggestion, actionableData) => handleApplySuggestion(activeInsightsPanel, suggestion, actionableData)}
 />
 )}

 <DocumentViewerPortal
 citations={(() => {
 // Extract all unique citations from all sections
 const allCitations = new Map<string, any>();
 Object.values(insightsData).forEach(data => {
 data.sections?.forEach((section: any) => {
 if (section.citations && Array.isArray(section.citations)) {
 section.citations.forEach((citation: any) => {
 if (citation.id && !allCitations.has(citation.id)) {
 allCitations.set(citation.id, citation);
 }
 });
 }
 });
 });
 return Array.from(allCitations.values());
 })()}
 sources={(() => {
 // Extract all unique sources from all insights data
 const allSources = new Map<string, any>();
 Object.values(insightsData).forEach(data => {
 data.sources?.forEach((source: any) => {
 if (source.nctId && !allSources.has(source.nctId)) {
 allSources.set(source.nctId, source);
 }
 });
 });
 return Array.from(allSources.values());
 })()}
 onStudySelect={(studyUrl) => setDocumentViewerUrl(studyUrl)}
 currentStudyId={(() => {
 // Extract NCT ID from the current document URL
 if (!documentViewerUrl) return undefined;
 const match = documentViewerUrl.match(/NCT\\d+/i);
 return match ? match[0] : undefined;
 })()}
 isOpen={!!documentViewerUrl}
 onClose={() => setDocumentViewerUrl(null)}
 documentUrl={documentViewerUrl}
 loading={false}
 />
 
 <SourceStudiesViewerPortal
 isOpen={sourceStudiesViewerOpen}
 onClose={() => setSourceStudiesViewerOpen(false)}
 sources={sourceStudiesViewerSources}
 onDocumentClick={(url) => setDocumentViewerUrl(url)}
 />

 </div>
 );
}