"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { Textarea } from "~/components/ui/textarea";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { ChevronLeft, Pill, Info, Target } from "lucide-react";

export default function BasicsPage() {
 const router = useRouter();
 const store = useTrialDesignStore();
 const studyType = store.discovery.studyType;
 
 const [formData, setFormData] = useState({
 // Intervention details
 interventionName: store.discovery.intervention.name || "",
 drugClass: store.discovery.intervention.class || "",
 isNewCompound: store.discovery.intervention.isNewCompound || false,
 category: store.discovery.intervention.category || "",
 mechanism: store.discovery.intervention.mechanism || "",
 
 // Condition
 condition: store.discovery.condition || "",
 });

 const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
 onSuccess: () => {
 store.markStepCompleted("basics");
 router.push("/study/new/study-design");
 },
 onError: (error) => {
 toast.error("Failed to save: " + error.message);
 },
 });

 const handleContinue = () => {
 if (!formData.condition) {
 toast.error("Please provide the condition being studied");
 return;
 }

 if (studyType === "drug" && !formData.drugClass) {
 toast.error("Please provide the drug class");
 return;
 }

 if (!store.sessionId) {
 toast.error("Session not found");
 return;
 }

 // Update store
 store.updateDiscovery({ 
 intervention: {
 name: formData.interventionName,
 class: formData.drugClass,
 isNewCompound: formData.isNewCompound,
 category: formData.category,
 mechanism: formData.mechanism,
 },
 condition: formData.condition,
 });

 // Save to backend
 saveDiscovery.mutate({
 sessionId: store.sessionId,
 data: {
 intervention: {
 name: formData.interventionName,
 class: formData.drugClass,
 isNewCompound: formData.isNewCompound,
 category: formData.category,
 mechanism: formData.mechanism,
 },
 condition: formData.condition,
 },
 });
 };

 return (
 <div className="space-y-8">
 <BlurFade delay={0.01} inView>
 <div>
 <h1 className="text-3xl font-bold text-gray-900">
 Study Basics
 </h1>
 <p className="mt-2 text-lg text-gray-600">
 Let's start with the factual information about your {studyType || "study"}
 </p>
 </div>
 </BlurFade>

 <BlurFade delay={0.02} inView>
 <Card>
 <CardHeader>
 <CardTitle className="flex items-center gap-2">
 <Target className="h-5 w-5" />
 Condition Being Studied
 </CardTitle>
 <CardDescription>
 What medical condition or disease are you targeting?
 </CardDescription>
 </CardHeader>
 <CardContent>
 <div className="space-y-2">
 <Label htmlFor="condition">Target Condition *</Label>
 <Input
 id="condition"
 placeholder="e.g., Type 2 Diabetes Mellitus, Hypertension, Major Depressive Disorder"
 value={formData.condition}
 onChange={(e) => setFormData({ ...formData, condition: e.target.value })}
 />
 <p className="text-xs text-muted-foreground">
 Be specific about the condition, including any relevant subtypes or stages
 </p>
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 {studyType === "drug" && (
 <BlurFade delay={0.03} inView>
 <Card>
 <CardHeader>
 <CardTitle className="flex items-center gap-2">
 <Pill className="h-5 w-5" />
 Drug Information
 </CardTitle>
 <CardDescription>
 Core details about your investigational drug
 </CardDescription>
 </CardHeader>
 <CardContent className="space-y-6">
 <div className="space-y-2">
 <Label htmlFor="interventionName">Drug Name (Optional)</Label>
 <Input
 id="interventionName"
 placeholder="e.g., XYZ-123, leave blank if not yet determined"
 value={formData.interventionName}
 onChange={(e) => setFormData({ ...formData, interventionName: e.target.value })}
 />
 <p className="text-xs text-muted-foreground">
 You can leave this blank and we'll help you determine an appropriate name later
 </p>
 </div>

 <div className="space-y-2">
 <Label htmlFor="drugClass">Drug Class *</Label>
 <Input
 id="drugClass"
 placeholder="e.g., SGLT-2 inhibitor, PD-1 inhibitor, SSRI"
 value={formData.drugClass}
 onChange={(e) => setFormData({ ...formData, drugClass: e.target.value })}
 />
 </div>

 <div className="space-y-3">
 <Label>Is this a novel compound? *</Label>
 <RadioGroup
 value={formData.isNewCompound ? "yes" : "no"}
 onValueChange={(value) => setFormData({ ...formData, isNewCompound: value === "yes" })}
 >
 <div className="flex items-center space-x-2">
 <RadioGroupItem value="yes" id="new-yes" />
 <Label htmlFor="new-yes" className="font-normal">
 Yes - First-in-class or new molecular entity
 </Label>
 </div>
 <div className="flex items-center space-x-2">
 <RadioGroupItem value="no" id="new-no" />
 <Label htmlFor="new-no" className="font-normal">
 No - Existing compound (repurposing, new indication, or me-too drug)
 </Label>
 </div>
 </RadioGroup>
 </div>

 <div className="space-y-2">
 <Label htmlFor="category">Formulation/Route</Label>
 <Input
 id="category"
 placeholder="e.g., Oral tablet, IV infusion, Subcutaneous injection"
 value={formData.category}
 onChange={(e) => setFormData({ ...formData, category: e.target.value })}
 />
 </div>

 <div className="space-y-2">
 <Label htmlFor="mechanism">Mechanism of Action</Label>
 <Textarea
 id="mechanism"
 placeholder="Briefly describe how the drug works at a molecular/cellular level..."
 value={formData.mechanism}
 onChange={(e) => setFormData({ ...formData, mechanism: e.target.value })}
 rows={3}
 />
 </div>
 </CardContent>
 </Card>
 </BlurFade>
 )}

 {studyType === "device" && (
 <BlurFade delay={0.03} inView>
 <Card>
 <CardHeader>
 <CardTitle className="flex items-center gap-2">
 <Info className="h-5 w-5" />
 Device Information
 </CardTitle>
 <CardDescription>
 Basic information about your medical device
 </CardDescription>
 </CardHeader>
 <CardContent className="space-y-6">
 <div className="space-y-2">
 <Label htmlFor="interventionName">Device Name</Label>
 <Input
 id="interventionName"
 placeholder="e.g., Device name or identifier"
 value={formData.interventionName}
 onChange={(e) => setFormData({ ...formData, interventionName: e.target.value })}
 />
 </div>

 <div className="space-y-2">
 <Label htmlFor="category">Device Type</Label>
 <Input
 id="category"
 placeholder="e.g., Implantable, Wearable, Diagnostic, Therapeutic"
 value={formData.category}
 onChange={(e) => setFormData({ ...formData, category: e.target.value })}
 />
 </div>

 <div className="space-y-2">
 <Label htmlFor="mechanism">How It Works</Label>
 <Textarea
 id="mechanism"
 placeholder="Briefly describe the device function and mechanism..."
 value={formData.mechanism}
 onChange={(e) => setFormData({ ...formData, mechanism: e.target.value })}
 rows={3}
 />
 </div>
 </CardContent>
 </Card>
 </BlurFade>
 )}

 {studyType === "behavioral" && (
 <BlurFade delay={0.03} inView>
 <Card>
 <CardHeader>
 <CardTitle className="flex items-center gap-2">
 <Info className="h-5 w-5" />
 Intervention Details
 </CardTitle>
 <CardDescription>
 Information about your behavioral intervention
 </CardDescription>
 </CardHeader>
 <CardContent className="space-y-6">
 <div className="space-y-2">
 <Label htmlFor="interventionName">Intervention Name</Label>
 <Input
 id="interventionName"
 placeholder="e.g., Cognitive Behavioral Therapy, Exercise Program"
 value={formData.interventionName}
 onChange={(e) => setFormData({ ...formData, interventionName: e.target.value })}
 />
 </div>

 <div className="space-y-2">
 <Label htmlFor="category">Type of Intervention</Label>
 <Input
 id="category"
 placeholder="e.g., Therapy, Education, Lifestyle modification"
 value={formData.category}
 onChange={(e) => setFormData({ ...formData, category: e.target.value })}
 />
 </div>

 <div className="space-y-2">
 <Label htmlFor="mechanism">Approach/Method</Label>
 <Textarea
 id="mechanism"
 placeholder="Describe the intervention approach and theoretical basis..."
 value={formData.mechanism}
 onChange={(e) => setFormData({ ...formData, mechanism: e.target.value })}
 rows={3}
 />
 </div>
 </CardContent>
 </Card>
 </BlurFade>
 )}

 <BlurFade delay={0.04} inView>
 <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
 <p className="text-sm text-blue-800">
 <strong>Note:</strong> We're collecting the factual information you should already know. 
 In the next steps, we'll use our knowledge base to help you with more complex decisions 
 like study phase, endpoints, and population criteria.
 </p>
 </div>
 </BlurFade>

 <BlurFade delay={0.05} inView>
 <div className="flex justify-between pt-6">
 <Button
 variant="outline"
 onClick={() => router.push("/study/new")}
 >
 <ChevronLeft className="mr-2 h-4 w-4" />
 Back
 </Button>
 <Button
 onClick={handleContinue}
 disabled={saveDiscovery.isPending}
 >
 {saveDiscovery.isPending ? "Saving..." : "Continue to Study Design"}
 </Button>
 </div>
 </BlurFade>
 </div>
 );
}