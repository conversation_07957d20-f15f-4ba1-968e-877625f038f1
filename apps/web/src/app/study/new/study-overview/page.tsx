"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { InsightsButton } from "~/components/insights/InsightsButton";
import { InsightsPanelPortal } from "~/components/insights/InsightsPanelPortal";
import { DocumentViewerPortal } from "~/components/insights/DocumentViewerPortal";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { 
 ChevronLeft, 
 FlaskConical,
 FileText,
 Lightbulb,
 BookOpen,
 Target,
 Info
} from "lucide-react";
import type { StudyType, StudyPhase } from "~/types/trial-design";

export default function StudyOverviewPage() {
 const router = useRouter();
 const store = useTrialDesignStore();
 
 const [formData, setFormData] = useState({
 // Study Type Selection
 studyType: store.discovery.studyType || null,
 phase: store.discovery.phase || null,
 condition: store.discovery.condition || "",
 
 // Protocol Information (Step 1 Critical Questions)
 protocolAcronym: store.discovery.protocol?.protocolAcronym || "",
 protocolFullTitle: store.discovery.protocol?.protocolFullTitle || "",
 studyBackground: store.discovery.protocol?.studyBackground || "",
 studyDetailsForAI: store.discovery.protocol?.studyDetailsForAI || "",
 protocolIdNumber: store.discovery.protocol?.protocolIdNumber || "",
 });

 const [activeInsightsPanel, setActiveInsightsPanel] = useState<string | null>(null);
 const [insightsData, setInsightsData] = useState<Record<string, { sections: any[]; sources?: any[]; progressStatus?: string; progressMessages?: string[] }>>({});
 const [documentViewerUrl, setDocumentViewerUrl] = useState<string | null>(null);
 
 const cachedInsights = store.insightsCache || {};

 const queryInsights = api.knowledgeBase.queryInsights.useMutation({
 onSuccess: (data, variables) => {
 const insightsPayload = {
 sections: data.sections || [],
 sources: data.sources || [],
 progressStatus: undefined,
 progressMessages: [],
 };
 
 setInsightsData(prev => ({
 ...prev,
 [variables.field]: insightsPayload
 }));
 
 store.cacheInsights(variables.field, insightsPayload);
 setActiveInsightsPanel(variables.field);
 },
 onError: (error) => {
 toast.error("Failed to get insights: " + error.message);
 },
 });

 const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
 onSuccess: () => {
 store.markStepCompleted("study-overview");
 router.push("/study/new/investigational-product");
 },
 onError: (error) => {
 toast.error("Failed to save: " + error.message);
 },
 });

 const handleGetInsights = async (field: string, forceRefresh = false) => {
 if (!forceRefresh && cachedInsights[field]) {
 setActiveInsightsPanel(field);
 return;
 }
 
 setActiveInsightsPanel(field);
 setInsightsData(prev => ({
 ...prev,
 [field]: {
 sections: [],
 sources: [],
 progressStatus: 'Searching for study background examples...',
 progressMessages: [],
 }
 }));
 
 const progressUpdates = [
 { delay: 0, message: 'Initializing search...' },
 { delay: 1500, message: 'Searching knowledge base...' },
 { delay: 3500, message: 'Analyzing study backgrounds...' },
 { delay: 6000, message: 'Extracting best practices...' },
 { delay: 8500, message: 'Generating recommendations...' },
 ];
 
 progressUpdates.forEach(({ delay, message }) => {
 setTimeout(() => {
 setInsightsData(prev => {
 const current = prev[field];
 if (current && !current.sections?.length) {
 return {
 ...prev,
 [field]: {
 ...current,
 progressStatus: message,
 progressMessages: [...(current.progressMessages || []), message],
 }
 };
 }
 return prev;
 });
 }, delay);
 });
 
 const context = {
 studyType: formData.studyType,
 condition: formData.condition || undefined,
 phase: formData.phase || undefined,
 };

 const queries: Record<string, string> = {
 "study-background": `What are examples of strong study backgrounds and rationales for ${formData.condition || "clinical"} studies?`,
 "protocol-naming": `What are examples of well-structured protocol titles and acronyms for ${formData.studyType || "clinical"} trials?`,
 };

 await queryInsights.mutateAsync({
 sessionId: store.sessionId!,
 field,
 context,
 query: queries[field] || "",
 });
 };

 const handleContinue = () => {
 if (!store.sessionId) {
 toast.error("Session not found");
 return;
 }

 // Validation
 if (!formData.studyType) {
 toast.error("Please select a study type");
 return;
 }
 
 if (!formData.condition) {
 toast.error("Please enter the condition being studied");
 return;
 }

 if (!formData.protocolFullTitle) {
 toast.error("Please enter the full protocol title");
 return;
 }

 // Update store
 store.updateDiscovery({ 
 studyType: formData.studyType,
 phase: formData.phase,
 condition: formData.condition,
 protocol: {
 protocolAcronym: formData.protocolAcronym,
 protocolFullTitle: formData.protocolFullTitle,
 studyBackground: formData.studyBackground,
 studyDetailsForAI: formData.studyDetailsForAI,
 protocolIdNumber: formData.protocolIdNumber,
 }
 });

 saveDiscovery.mutate({
 sessionId: store.sessionId,
 data: {
 studyType: formData.studyType,
 phase: formData.phase,
 condition: formData.condition,
 protocol: {
 protocolAcronym: formData.protocolAcronym,
 protocolFullTitle: formData.protocolFullTitle,
 studyBackground: formData.studyBackground,
 studyDetailsForAI: formData.studyDetailsForAI,
 protocolIdNumber: formData.protocolIdNumber,
 }
 },
 });
 };

 const handleApplySuggestion = (field: string, suggestion: string, actionableData?: any) => {
 if (field === "study-background") {
 if (actionableData && actionableData.field === 'studyBackground') {
 setFormData(prev => ({ ...prev, studyBackground: actionableData.value }));
 toast.success("Study background updated");
 }
 return;
 }
 
 if (field === "protocol-naming") {
 if (actionableData) {
 const updates: any = {};
 if (actionableData.field === 'protocolTitle') {
 updates.protocolFullTitle = actionableData.value;
 toast.success("Protocol title updated");
 }
 if (actionableData.field === 'protocolAcronym') {
 updates.protocolAcronym = actionableData.value;
 toast.success("Protocol acronym updated");
 }
 if (Object.keys(updates).length > 0) {
 setFormData(prev => ({ ...prev, ...updates }));
 }
 }
 return;
 }
 };

 return (
 <div className="space-y-8">
 <BlurFade delay={0.01} inView>
 <div>
 <h1 className="text-3xl font-bold text-gray-900">
 Study Overview & Background
 </h1>
 <p className="mt-2 text-lg text-gray-600">
 Define your study foundation and provide essential background information
 </p>
 </div>
 </BlurFade>

 {/* Study Type & Phase Selection */}
 <BlurFade delay={0.02} inView>
 <Card>
 <CardHeader>
 <CardTitle className="flex items-center gap-2">
 <FlaskConical className="h-5 w-5" />
 Study Type & Phase
 </CardTitle>
 <CardDescription>
 Select the type of study and development phase
 </CardDescription>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="grid grid-cols-2 gap-4">
 <div className="space-y-2">
 <Label htmlFor="studyType">Study Type</Label>
 <Select
 value={formData.studyType || ""}
 onValueChange={(value) => setFormData({ ...formData, studyType: value as StudyType })}
 >
 <SelectTrigger id="studyType">
 <SelectValue placeholder="Select study type" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="drug">Drug Study</SelectItem>
 <SelectItem value="device">Medical Device Study</SelectItem>
 <SelectItem value="behavioral">Behavioral Intervention</SelectItem>
 <SelectItem value="diagnostic">Diagnostic Study</SelectItem>
 <SelectItem value="other">Other</SelectItem>
 </SelectContent>
 </Select>
 </div>

 {formData.studyType === "drug" && (
 <div className="space-y-2">
 <Label htmlFor="phase">Study Phase</Label>
 <Select
 value={formData.phase || ""}
 onValueChange={(value) => setFormData({ ...formData, phase: value as StudyPhase })}
 >
 <SelectTrigger id="phase">
 <SelectValue placeholder="Select phase" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="phase1">Phase I</SelectItem>
 <SelectItem value="phase2">Phase II</SelectItem>
 <SelectItem value="phase3">Phase III</SelectItem>
 <SelectItem value="phase4">Phase IV</SelectItem>
 </SelectContent>
 </Select>
 </div>
 )}
 </div>

 <div className="space-y-2">
 <Label htmlFor="condition">Condition Being Studied</Label>
 <Input
 id="condition"
 placeholder="e.g., Type 2 Diabetes, Hypertension, Major Depressive Disorder"
 value={formData.condition}
 onChange={(e) => setFormData({ ...formData, condition: e.target.value })}
 />
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Protocol Information */}
 <BlurFade delay={0.03} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <FileText className="h-5 w-5" />
 Protocol Information
 </CardTitle>
 <CardDescription>
 Provide formal protocol identifiers and titles
 </CardDescription>
 </div>
 <InsightsButton
 onClick={() => handleGetInsights("protocol-naming")}
 onRefresh={() => handleGetInsights("protocol-naming", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "protocol-naming"}
 hasCachedData={!!cachedInsights["protocol-naming"]}
 showRefresh={!!cachedInsights["protocol-naming"]}
 />
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="grid grid-cols-2 gap-4">
 <div className="space-y-2">
 <Label htmlFor="protocolAcronym">Protocol Acronym (Short Name)</Label>
 <Input
 id="protocolAcronym"
 placeholder="e.g., HARMONY-DM2, CLARITY-HF"
 value={formData.protocolAcronym}
 onChange={(e) => setFormData({ ...formData, protocolAcronym: e.target.value })}
 />
 </div>
 <div className="space-y-2">
 <Label htmlFor="protocolIdNumber">Protocol ID Number</Label>
 <Input
 id="protocolIdNumber"
 placeholder="e.g., ABC-2024-001, XYZ-P3-202"
 value={formData.protocolIdNumber}
 onChange={(e) => setFormData({ ...formData, protocolIdNumber: e.target.value })}
 />
 </div>
 </div>

 <div className="space-y-2">
 <Label htmlFor="protocolFullTitle">Protocol Full Title *</Label>
 <Input
 id="protocolFullTitle"
 placeholder="e.g., A Phase 3, Randomized, Double-blind Study of..."
 value={formData.protocolFullTitle}
 onChange={(e) => setFormData({ ...formData, protocolFullTitle: e.target.value })}
 />
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Study Background */}
 <BlurFade delay={0.04} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <BookOpen className="h-5 w-5" />
 Study Background & Rationale
 </CardTitle>
 <CardDescription>
 Provide the scientific rationale and background for your study
 </CardDescription>
 </div>
 <InsightsButton
 onClick={() => handleGetInsights("study-background")}
 onRefresh={() => handleGetInsights("study-background", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "study-background"}
 hasCachedData={!!cachedInsights["study-background"]}
 showRefresh={!!cachedInsights["study-background"]}
 />
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="space-y-2">
 <Label htmlFor="studyBackground">Study Background</Label>
 <Textarea
 id="studyBackground"
 placeholder="Describe the medical need, current treatment landscape, and rationale for this study..."
 rows={6}
 value={formData.studyBackground}
 onChange={(e) => setFormData({ ...formData, studyBackground: e.target.value })}
 />
 </div>

 <div className="space-y-2">
 <Label htmlFor="studyDetailsForAI">
 <span className="flex items-center gap-2">
 <Target className="h-4 w-4" />
 Study Details for AI Assistant
 </span>
 </Label>
 <Textarea
 id="studyDetailsForAI"
 placeholder="Provide any additional context or specific details you want the AI to consider when generating insights..."
 rows={4}
 value={formData.studyDetailsForAI}
 onChange={(e) => setFormData({ ...formData, studyDetailsForAI: e.target.value })}
 />
 <p className="text-xs text-muted-foreground">
 This information helps our AI provide more relevant recommendations throughout the design process
 </p>
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Summary */}
 <BlurFade delay={0.05} inView>
 <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
 <CardContent className="p-6">
 <div className="flex items-center gap-3 mb-4">
 <Info className="h-5 w-5 text-blue-600" />
 <h3 className="font-medium text-blue-900">Summary</h3>
 </div>
 <div className="space-y-2 text-sm">
 <p><strong>Study Type:</strong> {formData.studyType ? (
 <Badge variant="secondary" className="ml-2">
 {formData.studyType} {formData.phase && `- ${formData.phase.toUpperCase()}`}
 </Badge>
 ) : "Not selected"}</p>
 <p><strong>Condition:</strong> {formData.condition || "Not specified"}</p>
 <p><strong>Protocol Title:</strong> {formData.protocolFullTitle || "Not specified"}</p>
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Navigation */}
 <BlurFade delay={0.06} inView>
 <div className="flex justify-between pt-6">
 <Button
 variant="outline"
 onClick={() => router.push("/dashboard")}
 >
 <ChevronLeft className="mr-2 h-4 w-4" />
 Back to Dashboard
 </Button>
 <Button
 onClick={handleContinue}
 disabled={saveDiscovery.isPending}
 >
 {saveDiscovery.isPending ? "Saving..." : "Continue to Investigational Product"}
 </Button>
 </div>
 </BlurFade>

 {/* Insights Panel */}
 {activeInsightsPanel && (
 <InsightsPanelPortal
 isOpen={true}
 onClose={() => setActiveInsightsPanel(null)}
 title={`Insights: ${activeInsightsPanel.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase())}`}
 description="Recommendations based on similar studies"
 loading={queryInsights.isPending}
 sections={insightsData[activeInsightsPanel]?.sections || cachedInsights[activeInsightsPanel]?.sections || []}
 sources={insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || []}
 progressStatus={insightsData[activeInsightsPanel]?.progressStatus}
 progressMessages={insightsData[activeInsightsPanel]?.progressMessages}
 onDocumentClick={(url) => setDocumentViewerUrl(url)}
 onApplySuggestion={(suggestion, actionableData) => handleApplySuggestion(activeInsightsPanel, suggestion, actionableData)}
 />
 )}

 {/* Document Viewer */}
 {documentViewerUrl && (
 <DocumentViewerPortal
 citations={(() => {
 // Extract all unique citations from all sections
 const allCitations = new Map<string, any>();
 Object.values(insightsData).forEach(data => {
 data.sections?.forEach((section: any) => {
 if (section.citations && Array.isArray(section.citations)) {
 section.citations.forEach((citation: any) => {
 if (citation.id && !allCitations.has(citation.id)) {
 allCitations.set(citation.id, citation);
 }
 });
 }
 });
 });
 return Array.from(allCitations.values());
 })()}
 sources={(() => {
 // Extract all unique sources from all insights data
 const allSources = new Map<string, any>();
 Object.values(insightsData).forEach(data => {
 data.sources?.forEach((source: any) => {
 if (source.nctId && !allSources.has(source.nctId)) {
 allSources.set(source.nctId, source);
 }
 });
 });
 return Array.from(allSources.values());
 })()}
 onStudySelect={(studyUrl) => setDocumentViewerUrl(studyUrl)}
 currentStudyId={(() => {
 // Extract NCT ID from the current document URL
 if (!documentViewerUrl) return undefined;
 const match = documentViewerUrl.match(/NCT\\d+/i);
 return match ? match[0] : undefined;
 })()}
 isOpen={true}
 onClose={() => setDocumentViewerUrl(null)}
 documentUrl={documentViewerUrl}
 loading={false}
 />
 )}
 </div>
 );
}