"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "~/components/ui/collapsible";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { 
 ChevronLeft, 
 FileText, 
 Download,
 Send,
 CheckCircle,
 Edit2,
 Sparkles,
 Copy,
 CheckCircle2,
 FileDown,
 FileJson,
 ChevronDown,
 ChevronUp,
 Pill,
 Beaker,
 Shield,
 Users,
 Activity,
 Settings,
 Building2,
 DollarSign,
 Target,
 Calendar,
 MapPin,
 TestTube,
 TrendingUp
} from "lucide-react";

// Function to calculate questions answered
const calculateQuestionsAnswered = (discovery: any): number => {
  let answered = 0;
  
  // Study Overview (6 questions)
  if (discovery.protocol?.protocolAcronym) answered++;
  if (discovery.protocol?.protocolFullTitle) answered++;
  if (discovery.protocol?.studyBackground) answered++;
  if (discovery.protocol?.studyDetailsForAI) answered++;
  if (discovery.protocol?.protocolIdNumber) answered++;
  if (discovery.protocol?.trialInterventionDetails) answered++;
  
  // Investigational Product (11 questions)
  if (discovery.intervention?.name) answered++;
  if (discovery.intervention?.category) answered++;
  if (discovery.intervention?.mechanism) answered++;
  if (discovery.intervention?.class || discovery.intervention?.drugClass) answered++;
  if (discovery.intervention?.medicalProblem) answered++;
  if (discovery.intervention?.comparisonToExistingTreatments) answered++;
  if (discovery.intervention?.regulatoryStatus) answered++;
  if (discovery.intervention?.preclinicalStudies) answered++;
  if (discovery.intervention?.toxicityStudies) answered++;
  if (discovery.intervention?.pregnancySafety) answered++;
  if (discovery.intervention?.fdaApprovalStatus) answered++;
  
  // Study Design (19 questions)
  if (discovery.design?.designDescriptors?.length > 0) answered++;
  if (discovery.design?.designType) answered++;
  if (discovery.design?.randomizationRatio) answered++;
  if (discovery.design?.blinding) answered++;
  if (discovery.design?.controlType) answered++;
  if (discovery.design?.numberOfStudyArms) answered++;
  if (discovery.design?.studyArmDescriptions?.length > 0) answered++;
  if (discovery.design?.hasAdaptiveDesign !== undefined) answered++;
  if (discovery.objectives?.primaryGoal) answered++;
  if (discovery.objectives?.keyOutcome) answered++;
  if (discovery.objectives?.primaryObjectiveStatement) answered++;
  if (discovery.objectives?.secondaryObjectiveStatement) answered++;
  if (discovery.objectives?.secondaryGoals?.length > 0) answered++;
  if (discovery.statistical?.sampleSizeDetermination) answered++;
  if (discovery.statistical?.statisticalModel) answered++;
  if (discovery.statistical?.hypothesisAndAnalysis) answered++;
  if (discovery.statistical?.interimAnalysisPlan) answered++;
  if (discovery.statistical?.power) answered++;
  if (discovery.statistical?.alpha) answered++;
  
  // Study Population (8 questions)
  if (discovery.population?.ageMin) answered++;
  if (discovery.population?.ageMax) answered++;
  if (discovery.population?.gender && discovery.population.gender !== "all") answered++;
  if (discovery.population?.specificPopulation) answered++;
  if (discovery.population?.inclusionCriteria?.length > 0) answered++;
  if (discovery.population?.exclusionCriteria?.length > 0) answered++;
  if (discovery.population?.targetEnrollment) answered++;
  if (discovery.population?.geographicScope) answered++;
  
  // Safety Assessment (6 questions)
  if (discovery.safety?.willCollectAESAE !== undefined) answered++;
  if (discovery.safety?.likelySideEffects?.length > 0) answered++;
  if (discovery.safety?.lessLikelySideEffects?.length > 0) answered++;
  if (discovery.safety?.rareButSeriousSideEffects?.length > 0) answered++;
  if (discovery.safety?.hasReproductiveRisks !== undefined) answered++;
  if (discovery.safety?.reproductiveRiskDetails) answered++;
  
  // Laboratory Studies (7 questions)
  if (discovery.laboratory?.willCollectBiologicalSamples !== undefined) answered++;
  if (discovery.laboratory?.biologicalSpecimens?.length > 0) answered++;
  if (discovery.laboratory?.collectionAndProcessing) answered++;
  if (discovery.laboratory?.willConductPK !== undefined) answered++;
  if (discovery.laboratory?.willConductBiomarker !== undefined) answered++;
  if (discovery.laboratory?.willConductImmunogenicity !== undefined) answered++;
  if (discovery.laboratory?.willConductGeneticTesting !== undefined) answered++;
  
  // Regulatory (10 questions)
  if (discovery.regulatory?.irbName) answered++;
  if (discovery.regulatory?.sponsorName) answered++;
  if (discovery.regulatory?.drugManufacturerName || discovery.regulatory?.deviceManufacturerName) answered++;
  if (discovery.regulatory?.willUseCRO !== undefined) answered++;
  if (discovery.regulatory?.willParticipantsBeCompensated !== undefined) answered++;
  if (discovery.regulatory?.compensationDetails) answered++;
  if (discovery.regulatory?.sponsorPaysNonStandardCare) answered++;
  if (discovery.regulatory?.freeDrugDeviceProvided) answered++;
  if (discovery.regulatory?.standardCareChargedToParticipant) answered++;
  if (discovery.regulatory?.noBillableProcedures) answered++;
  
  return answered;
};

export default function ReviewPage() {
 const router = useRouter();
 const store = useTrialDesignStore();
 const [isGenerating, setIsGenerating] = useState(false);
 const [generatedSections, setGeneratedSections] = useState<Record<string, string>>({});
 const [currentSection, setCurrentSection] = useState<string>("");
 const [copied, setCopied] = useState(false);
 const [openSections, setOpenSections] = useState<Record<string, boolean>>({
 overview: true,
 product: false,
 design: false,
 population: false,
 safety: false,
 procedures: false,
 regulatory: false,
 summary: true
 });
 
 const discovery = store.discovery;
 const TOTAL_QUESTIONS = 67;
 const TOTAL_WORKFLOW_STEPS = 7; // Exclude review page itself
 const answeredQuestions = calculateQuestionsAnswered(discovery);

 // Function to properly convert markdown to HTML
 const markdownToHtml = (markdown: string): string => {
   // First, handle headers - must be done with multiline flag
   let html = markdown
     .replace(/^### (.+)$/gm, '<h4 class="text-base font-medium mb-2 mt-3">$1</h4>')
     .replace(/^## (.+)$/gm, '<h3 class="text-lg font-semibold mb-3 mt-4">$1</h3>')
     .replace(/^# (.+)$/gm, '<h2 class="text-xl font-bold mb-4 mt-6">$1</h2>');
   
   // Handle bold text
   html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
   
   // Handle lists
   html = html
     .replace(/^• (.+)$/gm, '<li class="ml-4">$1</li>')
     .replace(/^- (.+)$/gm, '<li class="ml-4">$1</li>')
     .replace(/^\d+\. (.+)$/gm, '<li class="ml-4 list-decimal">$1</li>');
   
   // Wrap consecutive list items in ul/ol tags
   html = html
     .replace(/(<li class="ml-4">.*<\/li>\n?)+/g, '<ul class="list-disc mb-3">$&</ul>')
     .replace(/(<li class="ml-4 list-decimal">.*<\/li>\n?)+/g, '<ol class="list-decimal mb-3">$&</ol>');
   
   // Handle paragraphs - split by double newlines
   const paragraphs = html.split(/\n\n+/);
   html = paragraphs.map(para => {
     // Don't wrap if it's already an HTML tag
     if (para.match(/^<[h|ul|ol|li]/)) return para;
     // Don't wrap empty strings
     if (!para.trim()) return '';
     // Wrap regular text in paragraph tags
     return `<p class="mb-3">${para}</p>`;
   }).join('\n');
   
   return html;
 };

 const toggleSection = (section: string) => {
 setOpenSections(prev => ({ ...prev, [section]: !prev[section] }));
 };

 const generateSynopsis = api.studyDesign.generateSynopsis.useMutation({
 onSuccess: (data) => {
 // Set all sections at once
 setGeneratedSections(data.sections);
 setIsGenerating(false);
 toast.success("Synopsis generated successfully!");
 // Store in the store for the synopsis page
 store.setSynopsis(data.fullDocument);
 },
 onError: (error) => {
 toast.error("Failed to generate synopsis: " + error.message);
 setIsGenerating(false);
 setCurrentSection("");
 },
 });

 const handleGenerateSynopsis = async () => {
 if (!store.sessionId) {
 toast.error("Session not found");
 return;
 }

 setIsGenerating(true);
 setGeneratedSections({});
 
 // Simulate progressive section generation
 const sections = [
 "Study Overview & Background",
 "Investigational Product Details",
 "Study Design & Statistical Analysis", 
 "Study Population & Enrollment",
 "Safety Assessment & Monitoring",
 "Study Procedures & Operations",
 "Regulatory, Financial & Legal Framework",
 "Executive Summary & Conclusions"
 ];
 
 // Show progress through sections
 let sectionIndex = 0;
 const progressInterval = setInterval(() => {
 if (sectionIndex < sections.length) {
 setCurrentSection(sections[sectionIndex] || "");
 sectionIndex++;
 }
 }, 500);
 
 // Generate all sections
 generateSynopsis.mutate({
 sessionId: store.sessionId,
 });
 
 // Clear interval when done
 setTimeout(() => clearInterval(progressInterval), sections.length * 500);
 };

 const editSection = (section: string) => {
 switch(section) {
 case 'overview':
 router.push('/study/new/study-overview');
 break;
 case 'product':
 router.push('/study/new/investigational-product');
 break;
 case 'design':
 router.push('/study/new/study-design');
 break;
 case 'population':
 router.push('/study/new/study-population');
 break;
 case 'safety':
 router.push('/study/new/safety-assessment');
 break;
 case 'procedures':
 router.push('/study/new/study-procedures-operations');
 break;
 case 'regulatory':
 router.push('/study/new/regulatory-financial-legal');
 break;
 }
 };

 const handleCopy = () => {
 const fullSynopsis = store.synopsis || Object.values(generatedSections).join('\n\n');
 navigator.clipboard.writeText(fullSynopsis);
 setCopied(true);
 toast.success("Synopsis copied to clipboard!");
 setTimeout(() => setCopied(false), 2000);
 };

 const handleExport = (format: "md" | "pdf" | "json") => {
 toast.info(`Export as ${format.toUpperCase()} functionality coming soon!`);
 // In production, this would trigger actual export
 };

 const handleComplete = () => {
 // Generate study title
 const intervention = discovery.intervention?.name || "Investigational Drug";
 const condition = discovery.condition || "Target Condition";
 const phase = discovery.phase?.toUpperCase() || "Phase 2";
 const title = `${phase} Study of ${intervention} in ${condition}`;
 
 // Get the full synopsis
 const fullSynopsis = store.synopsis || Object.values(generatedSections).join('\n\n');
 
 // Save the completed study
 store.saveCompletedStudy(title, fullSynopsis);
 store.markStepCompleted("review");
 
 toast.success("Trial design completed! 🎉");
 router.push("/dashboard");
 };

 return (
 <div className="space-y-8">
 <BlurFade delay={0.01} inView>
 <div>
 <h1 className="text-3xl font-bold text-gray-900">
 Review Your Study Design
 </h1>
 <p className="mt-2 text-lg text-gray-600">
 Review all details before generating your clinical trial synopsis
 </p>
 </div>
 </BlurFade>

 {/* 1. Study Overview & Background */}
 <BlurFade delay={0.02} inView>
 <Collapsible open={openSections.overview} onOpenChange={() => toggleSection('overview')}>
 <Card>
 <CollapsibleTrigger asChild>
 <CardHeader className="cursor-pointer hover:bg-gray-50 ">
 <div className="flex items-center justify-between">
 <div className="flex items-center gap-3">
 <Target className="h-5 w-5 text-blue-500" />
 <div>
 <CardTitle>1. Study Overview & Background</CardTitle>
 <CardDescription>Protocol information and study rationale (6 questions)</CardDescription>
 </div>
 </div>
 <div className="flex items-center gap-2">
 <Badge variant="secondary">
 {(discovery.protocolTitle ? 1 : 0) + 
 (discovery.studyType ? 1 : 0) + 
 (discovery.condition ? 1 : 0) + 
 (discovery.phase ? 1 : 0) + 
 (discovery.studyBackground ? 1 : 0) + 
 (discovery.drugHistory ? 1 : 0)}/6
 </Badge>
 {openSections.overview ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
 </div>
 </div>
 </CardHeader>
 </CollapsibleTrigger>
 <CollapsibleContent>
 <CardContent className="space-y-4 pt-0">
 <div className="flex justify-end">
 <Button
 variant="ghost"
 size="sm"
 onClick={() => editSection('overview')}
 >
 <Edit2 className="mr-2 h-4 w-4" />
 Edit Section
 </Button>
 </div>
 <div className="grid grid-cols-2 gap-4">
 <div>
 <p className="text-sm font-medium text-muted-foreground">Protocol Title</p>
 <p className="text-sm mt-1">{discovery.protocolTitle || "Not specified"}</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Study Type</p>
 <Badge variant="secondary" className="mt-1">
 {discovery.studyType || "Not specified"}
 </Badge>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Condition/Disease</p>
 <p className="text-sm mt-1">{discovery.condition || "Not specified"}</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Study Phase</p>
 <Badge className="mt-1">
 {discovery.phase || "Not specified"}
 </Badge>
 </div>
 </div>
 {discovery.studyBackground && (
 <div>
 <p className="text-sm font-medium text-muted-foreground">Study Background & Rationale</p>
 <p className="text-sm mt-1 bg-gray-50 p-3 rounded-md">{discovery.studyBackground}</p>
 </div>
 )}
 {discovery.drugHistory && (
 <div>
 <p className="text-sm font-medium text-muted-foreground">Drug Development History</p>
 <p className="text-sm mt-1 bg-gray-50 p-3 rounded-md">{discovery.drugHistory}</p>
 </div>
 )}
 </CardContent>
 </CollapsibleContent>
 </Card>
 </Collapsible>
 </BlurFade>

 {/* 2. Investigational Product */}
 <BlurFade delay={0.03} inView>
 <Collapsible open={openSections.product} onOpenChange={() => toggleSection('product')}>
 <Card>
 <CollapsibleTrigger asChild>
 <CardHeader className="cursor-pointer hover:bg-gray-50 ">
 <div className="flex items-center justify-between">
 <div className="flex items-center gap-3">
 <Pill className="h-5 w-5 text-green-500" />
 <div>
 <CardTitle>2. Investigational Product</CardTitle>
 <CardDescription>Comprehensive drug product information (11 questions)</CardDescription>
 </div>
 </div>
 <div className="flex items-center gap-2">
 <Badge variant="secondary">
 {(discovery.intervention?.name ? 1 : 0) + 
 (discovery.intervention?.medicalProblem ? 1 : 0) + 
 (discovery.intervention?.regulatoryStatus ? 1 : 0) + 
 (discovery.intervention?.activeIngredients?.length || 0 > 0 ? 1 : 0) + 
 (discovery.intervention?.preclinicalStudies ? 1 : 0) + 
 (discovery.intervention?.toxicityStudies ? 1 : 0) + 
 (discovery.intervention?.clinicalTrialsHistory ? 1 : 0) + 
 (discovery.intervention?.keyFindings ? 1 : 0) + 
 (discovery.intervention?.pregnancySafety ? 1 : 0) + 
 (discovery.intervention?.fdaApprovalStatus ? 1 : 0) + 
 (discovery.intervention?.comparisonToExistingTreatments ? 1 : 0)}/11
 </Badge>
 {openSections.product ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
 </div>
 </div>
 </CardHeader>
 </CollapsibleTrigger>
 <CollapsibleContent>
 <CardContent className="space-y-4 pt-0">
 <div className="flex justify-end">
 <Button
 variant="ghost"
 size="sm"
 onClick={() => editSection('product')}
 >
 <Edit2 className="mr-2 h-4 w-4" />
 Edit Section
 </Button>
 </div>
 <div className="grid grid-cols-2 gap-4">
 <div>
 <p className="text-sm font-medium text-muted-foreground">Drug Name</p>
 <p className="text-sm mt-1">{discovery.intervention?.name || "Not specified"}</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Drug Class</p>
 <p className="text-sm mt-1">{discovery.intervention?.class || "Not specified"}</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Route/Formulation</p>
 <p className="text-sm mt-1 capitalize">{discovery.intervention?.category || "Not specified"}</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">FDA Approval Status</p>
 <Badge variant={discovery.intervention?.fdaApprovalStatus === 'approved' ? 'default' : 'outline'} className="mt-1">
 {discovery.intervention?.fdaApprovalStatus || "Not specified"}
 </Badge>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Compound Type</p>
 <Badge variant="outline" className="mt-1">
 {discovery.intervention?.isNewCompound ? "Novel compound" : "Existing compound"}
 </Badge>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Pregnancy Safety</p>
 <Badge variant={discovery.intervention?.pregnancySafety === 'safe' ? 'default' : 'destructive'} className="mt-1">
 {discovery.intervention?.pregnancySafety || "Unknown"}
 </Badge>
 </div>
 </div>
 {discovery.intervention?.medicalProblem && (
 <div>
 <p className="text-sm font-medium text-muted-foreground">Medical Problem Addressed</p>
 <p className="text-sm mt-1 bg-gray-50 p-3 rounded-md">{discovery.intervention.medicalProblem}</p>
 </div>
 )}
 {discovery.intervention?.mechanism && (
 <div>
 <p className="text-sm font-medium text-muted-foreground">Mechanism of Action</p>
 <p className="text-sm mt-1 bg-gray-50 p-3 rounded-md">{discovery.intervention.mechanism}</p>
 </div>
 )}
 {discovery.intervention?.activeIngredients && discovery.intervention.activeIngredients.length > 0 && (
 <div>
 <p className="text-sm font-medium text-muted-foreground">Active Ingredients</p>
 <div className="mt-2 flex flex-wrap gap-1">
 {discovery.intervention.activeIngredients.map((ingredient: string, idx: number) => (
 <Badge key={idx} variant="secondary" className="text-xs">
 {ingredient}
 </Badge>
 ))}
 </div>
 </div>
 )}
 {discovery.intervention?.preclinicalStudies && (
 <div>
 <p className="text-sm font-medium text-muted-foreground">Preclinical Studies</p>
 <p className="text-sm mt-1 bg-gray-50 p-3 rounded-md">{discovery.intervention.preclinicalStudies}</p>
 </div>
 )}
 </CardContent>
 </CollapsibleContent>
 </Card>
 </Collapsible>
 </BlurFade>

 {/* 3. Study Design & Statistical Analysis */}
 <BlurFade delay={0.04} inView>
 <Collapsible open={openSections.design} onOpenChange={() => toggleSection('design')}>
 <Card>
 <CollapsibleTrigger asChild>
 <CardHeader className="cursor-pointer hover:bg-gray-50 ">
 <div className="flex items-center justify-between">
 <div className="flex items-center gap-3">
 <TrendingUp className="h-5 w-5 text-purple-500" />
 <div>
 <CardTitle>3. Study Design & Statistical Analysis</CardTitle>
 <CardDescription>Design methodology and statistical framework (19 questions)</CardDescription>
 </div>
 </div>
 <div className="flex items-center gap-2">
 <Badge variant="secondary">
 {(discovery.design?.designType ? 1 : 0) + 
 (discovery.design?.blinding ? 1 : 0) + 
 (discovery.design?.controlType ? 1 : 0) + 
 (discovery.objectives?.primaryGoal ? 1 : 0) + 
 (discovery.objectives?.secondaryGoals?.length || 0 > 0 ? 1 : 0) + 
 (discovery.statisticalAnalysis?.primaryAnalysisMethod ? 1 : 0) + 
 (discovery.statisticalAnalysis?.sampleSizeJustification ? 1 : 0) + 
 (discovery.statisticalAnalysis?.powerAnalysis ? 1 : 0) + 
 (discovery.design?.randomizationRatio ? 1 : 0)}/19
 </Badge>
 {openSections.design ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
 </div>
 </div>
 </CardHeader>
 </CollapsibleTrigger>
 <CollapsibleContent>
 <CardContent className="space-y-4 pt-0">
 <div className="flex justify-end">
 <Button
 variant="ghost"
 size="sm"
 onClick={() => editSection('design')}
 >
 <Edit2 className="mr-2 h-4 w-4" />
 Edit Section
 </Button>
 </div>
 <div className="grid grid-cols-2 gap-4">
 <div>
 <p className="text-sm font-medium text-muted-foreground">Design Type</p>
 <Badge variant="secondary" className="mt-1">
 {discovery.design?.designType || "Not specified"}
 </Badge>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Blinding</p>
 <Badge variant="outline" className="mt-1">
 {discovery.design?.blinding || "Not specified"}
 </Badge>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Control Type</p>
 <p className="text-sm mt-1">{discovery.design?.controlType || "Not specified"}</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Randomization Ratio</p>
 <p className="text-sm mt-1">{discovery.design?.randomizationRatio || "Not specified"}</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Primary Analysis Method</p>
 <p className="text-sm mt-1">{discovery.statisticalAnalysis?.primaryAnalysisMethod || "Not specified"}</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Significance Level</p>
 <p className="text-sm mt-1">{discovery.statisticalAnalysis?.significanceLevel || "Not specified"}</p>
 </div>
 </div>
 {discovery.objectives?.primaryGoal && (
 <div>
 <p className="text-sm font-medium text-muted-foreground">Primary Endpoint</p>
 <p className="text-sm mt-1 bg-gray-50 p-3 rounded-md">{discovery.objectives.primaryGoal}</p>
 </div>
 )}
 {discovery.objectives?.secondaryGoals && discovery.objectives.secondaryGoals.length > 0 && (
 <div>
 <p className="text-sm font-medium text-muted-foreground">Secondary Endpoints ({discovery.objectives.secondaryGoals.length})</p>
 <div className="mt-2 space-y-1">
 {discovery.objectives.secondaryGoals.slice(0, 3).map((goal: string, idx: number) => (
 <div key={idx} className="text-sm bg-gray-50 p-2 rounded-md">
 {goal}
 </div>
 ))}
 {discovery.objectives.secondaryGoals.length > 3 && (
 <p className="text-xs text-muted-foreground">
 +{discovery.objectives.secondaryGoals.length - 3} more endpoints
 </p>
 )}
 </div>
 </div>
 )}
 {discovery.statisticalAnalysis?.sampleSizeJustification && (
 <div>
 <p className="text-sm font-medium text-muted-foreground">Sample Size Justification</p>
 <p className="text-sm mt-1 bg-gray-50 p-3 rounded-md">{discovery.statisticalAnalysis.sampleSizeJustification}</p>
 </div>
 )}
 {discovery.statisticalAnalysis?.powerAnalysis && (
 <div>
 <p className="text-sm font-medium text-muted-foreground">Power Analysis</p>
 <p className="text-sm mt-1 bg-gray-50 p-3 rounded-md">{discovery.statisticalAnalysis.powerAnalysis}</p>
 </div>
 )}
 </CardContent>
 </CollapsibleContent>
 </Card>
 </Collapsible>
 </BlurFade>

 {/* 4. Study Population */}
 <BlurFade delay={0.05} inView>
 <Collapsible open={openSections.population} onOpenChange={() => toggleSection('population')}>
 <Card>
 <CollapsibleTrigger asChild>
 <CardHeader className="cursor-pointer hover:bg-gray-50 ">
 <div className="flex items-center justify-between">
 <div className="flex items-center gap-3">
 <Users className="h-5 w-5 text-orange-500" />
 <div>
 <CardTitle>4. Study Population</CardTitle>
 <CardDescription>Enhanced demographics and enrollment strategy (8 questions)</CardDescription>
 </div>
 </div>
 <div className="flex items-center gap-2">
 <Badge variant="secondary">
 {(discovery.population?.targetEnrollment ? 1 : 0) + 
 (discovery.population?.ageMin && discovery.population?.ageMax ? 1 : 0) + 
 (discovery.population?.inclusionCriteria?.length || 0 > 0 ? 1 : 0) + 
 (discovery.population?.exclusionCriteria?.length || 0 > 0 ? 1 : 0) + 
 (discovery.population?.trialAssignmentMethod ? 1 : 0) + 
 (discovery.population?.numberOfSites ? 1 : 0) + 
 (discovery.population?.countriesEngaged?.length || 0 > 0 ? 1 : 0) + 
 (discovery.population?.geographicScope ? 1 : 0)}/8
 </Badge>
 {openSections.population ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
 </div>
 </div>
 </CardHeader>
 </CollapsibleTrigger>
 <CollapsibleContent>
 <CardContent className="space-y-4 pt-0">
 <div className="flex justify-end">
 <Button
 variant="ghost"
 size="sm"
 onClick={() => editSection('population')}
 >
 <Edit2 className="mr-2 h-4 w-4" />
 Edit Section
 </Button>
 </div>
 <div className="grid grid-cols-2 gap-4">
 <div>
 <p className="text-sm font-medium text-muted-foreground">Target Enrollment</p>
 <p className="text-sm mt-1">{discovery.population?.targetEnrollment || "Not specified"} participants</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Age Range</p>
 <p className="text-sm mt-1">
 {discovery.population?.ageMin || 18} - {discovery.population?.ageMax || 65} years
 </p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Gender</p>
 <p className="text-sm mt-1 capitalize">{discovery.population?.gender || "All"}</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Geographic Scope</p>
 <Badge variant="outline" className="mt-1">
 {discovery.population?.geographicScope || "Not specified"}
 </Badge>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Assignment Method</p>
 <p className="text-sm mt-1">{discovery.population?.trialAssignmentMethod || "Not specified"}</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Number of Sites</p>
 <p className="text-sm mt-1">{discovery.population?.numberOfSites || "Not specified"}</p>
 </div>
 </div>
 {discovery.population?.countriesEngaged && discovery.population.countriesEngaged.length > 0 && (
 <div>
 <p className="text-sm font-medium text-muted-foreground">Countries Engaged ({discovery.population.countriesEngaged.length})</p>
 <div className="mt-2 flex flex-wrap gap-1">
 {discovery.population.countriesEngaged.map((country: string, idx: number) => (
 <Badge key={idx} variant="secondary" className="text-xs">
 {country}
 </Badge>
 ))}
 </div>
 </div>
 )}
 {discovery.population?.inclusionCriteria && discovery.population.inclusionCriteria.length > 0 && (
 <div>
 <p className="text-sm font-medium text-muted-foreground">Inclusion Criteria ({discovery.population.inclusionCriteria.length})</p>
 <div className="mt-2 space-y-1">
 {discovery.population.inclusionCriteria.slice(0, 3).map((criteria: string, idx: number) => (
 <div key={idx} className="text-sm bg-gray-50 p-2 rounded-md">
 {criteria}
 </div>
 ))}
 {discovery.population.inclusionCriteria.length > 3 && (
 <p className="text-xs text-muted-foreground">
 +{discovery.population.inclusionCriteria.length - 3} more criteria
 </p>
 )}
 </div>
 </div>
 )}
 {discovery.population?.exclusionCriteria && discovery.population.exclusionCriteria.length > 0 && (
 <div>
 <p className="text-sm font-medium text-muted-foreground">Exclusion Criteria ({discovery.population.exclusionCriteria.length})</p>
 <div className="mt-2 space-y-1">
 {discovery.population.exclusionCriteria.slice(0, 3).map((criteria: string, idx: number) => (
 <div key={idx} className="text-sm bg-gray-50 p-2 rounded-md">
 {criteria}
 </div>
 ))}
 {discovery.population.exclusionCriteria.length > 3 && (
 <p className="text-xs text-muted-foreground">
 +{discovery.population.exclusionCriteria.length - 3} more criteria
 </p>
 )}
 </div>
 </div>
 )}
 </CardContent>
 </CollapsibleContent>
 </Card>
 </Collapsible>
 </BlurFade>

 {/* 5. Safety Assessment */}
 <BlurFade delay={0.06} inView>
 <Collapsible open={openSections.safety} onOpenChange={() => toggleSection('safety')}>
 <Card>
 <CollapsibleTrigger asChild>
 <CardHeader className="cursor-pointer hover:bg-gray-50 ">
 <div className="flex items-center justify-between">
 <div className="flex items-center gap-3">
 <Shield className="h-5 w-5 text-red-500" />
 <div>
 <CardTitle>5. Safety Assessment</CardTitle>
 <CardDescription>Safety monitoring and risk profile (6 questions)</CardDescription>
 </div>
 </div>
 <div className="flex items-center gap-2">
 <Badge variant="secondary">
 {(discovery.safety?.willCollectAESAE !== undefined ? 1 : 0) + 
 (discovery.safety?.likelySideEffects?.length || 0 > 0 ? 1 : 0) + 
 (discovery.safety?.lessLikelySideEffects?.length || 0 > 0 ? 1 : 0) + 
 (discovery.safety?.rareButSeriousSideEffects?.length || 0 > 0 ? 1 : 0) + 
 (discovery.safety?.hasReproductiveRisks !== undefined ? 1 : 0) + 
 (discovery.safety?.reproductiveRiskDetails ? 1 : 0)}/6
 </Badge>
 {openSections.safety ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
 </div>
 </div>
 </CardHeader>
 </CollapsibleTrigger>
 <CollapsibleContent>
 <CardContent className="space-y-4 pt-0">
 <div className="flex justify-end">
 <Button
 variant="ghost"
 size="sm"
 onClick={() => editSection('safety')}
 >
 <Edit2 className="mr-2 h-4 w-4" />
 Edit Section
 </Button>
 </div>
 <div className="grid grid-cols-2 gap-4">
 <div>
 <p className="text-sm font-medium text-muted-foreground">AE/SAE Collection</p>
 <Badge variant={discovery.safety?.willCollectAESAE ? 'default' : 'outline'} className="mt-1">
 {discovery.safety?.willCollectAESAE ? 'Active' : 'Not Active'}
 </Badge>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Reproductive Risks</p>
 <Badge variant={discovery.safety?.hasReproductiveRisks ? 'destructive' : 'default'} className="mt-1">
 {discovery.safety?.hasReproductiveRisks ? 'Yes' : 'No'}
 </Badge>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Likely Side Effects</p>
 <p className="text-sm mt-1">{discovery.safety?.likelySideEffects?.length || 0} defined</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Less Likely Side Effects</p>
 <p className="text-sm mt-1">{discovery.safety?.lessLikelySideEffects?.length || 0} defined</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Rare Serious Side Effects</p>
 <p className="text-sm mt-1">{discovery.safety?.rareButSeriousSideEffects?.length || 0} defined</p>
 </div>
 </div>
 {discovery.safety?.likelySideEffects && discovery.safety.likelySideEffects.length > 0 && (
 <div>
 <p className="text-sm font-medium text-muted-foreground">Likely Side Effects ({discovery.safety.likelySideEffects.length})</p>
 <div className="mt-2 flex flex-wrap gap-1">
 {discovery.safety.likelySideEffects.slice(0, 5).map((effect: string, idx: number) => (
 <Badge key={idx} variant="secondary" className="text-xs">
 {effect}
 </Badge>
 ))}
 {discovery.safety.likelySideEffects.length > 5 && (
 <Badge variant="outline" className="text-xs">
 +{discovery.safety.likelySideEffects.length - 5} more
 </Badge>
 )}
 </div>
 </div>
 )}
 </CardContent>
 </CollapsibleContent>
 </Card>
 </Collapsible>
 </BlurFade>

 {/* 6. Study Procedures & Operations */}
 <BlurFade delay={0.07} inView>
 <Collapsible open={openSections.procedures} onOpenChange={() => toggleSection('procedures')}>
 <Card>
 <CollapsibleTrigger asChild>
 <CardHeader className="cursor-pointer hover:bg-gray-50 ">
 <div className="flex items-center justify-between">
 <div className="flex items-center gap-3">
 <Activity className="h-5 w-5 text-cyan-500" />
 <div>
 <CardTitle>6. Study Procedures & Operations</CardTitle>
 <CardDescription>Timeline, laboratory studies & operational strategy (21 questions)</CardDescription>
 </div>
 </div>
 <div className="flex items-center gap-2">
 <Badge variant="secondary">
 {(discovery.timeline?.totalDuration ? 1 : 0) + 
 (discovery.timeline?.visits?.length || 0 > 0 ? 1 : 0) + 
 (discovery.laboratoryStudies?.specimenTypes?.length || 0 > 0 ? 1 : 0) + 
 (discovery.laboratoryStudies?.pkSampling ? 1 : 0) + 
 (discovery.operationalDetails?.recruitmentRate ? 1 : 0) + 
 (discovery.operationalDetails?.edcSystem ? 1 : 0)}/21
 </Badge>
 {openSections.procedures ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
 </div>
 </div>
 </CardHeader>
 </CollapsibleTrigger>
 <CollapsibleContent>
 <CardContent className="space-y-4 pt-0">
 <div className="flex justify-end">
 <Button
 variant="ghost"
 size="sm"
 onClick={() => editSection('procedures')}
 >
 <Edit2 className="mr-2 h-4 w-4" />
 Edit Section
 </Button>
 </div>
 <div className="grid grid-cols-2 gap-4">
 <div>
 <p className="text-sm font-medium text-muted-foreground">Total Duration</p>
 <Badge className="mt-1">
 {discovery.timeline?.totalDuration || "Not specified"}
 </Badge>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Scheduled Visits</p>
 <p className="text-sm mt-1">{discovery.timeline?.visits?.length || 0} visits</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Specimen Types</p>
 <p className="text-sm mt-1">{discovery.laboratoryStudies?.specimenTypes?.length || 0} types</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">PK Sampling</p>
 <Badge variant={discovery.laboratoryStudies?.pkSampling ? 'default' : 'outline'} className="mt-1">
 {discovery.laboratoryStudies?.pkSampling ? 'Required' : 'Not Required'}
 </Badge>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">EDC System</p>
 <p className="text-sm mt-1">{discovery.operationalDetails?.edcSystem || "Not specified"}</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Recruitment Rate</p>
 <p className="text-sm mt-1">{discovery.operationalDetails?.recruitmentRate || "Not specified"}</p>
 </div>
 </div>
 {discovery.timeline?.visits && discovery.timeline.visits.length > 0 && (
 <div>
 <p className="text-sm font-medium text-muted-foreground">Study Visits ({discovery.timeline.visits.length})</p>
 <div className="mt-2 space-y-1">
 {discovery.timeline.visits.slice(0, 4).map((visit: string, idx: number) => (
 <div key={idx} className="flex items-center gap-2">
 <Badge variant={visit.critical ? "default" : "outline"} className="text-xs">
 {visit.name}
 </Badge>
 <span className="text-xs text-muted-foreground">{visit.timepoint}</span>
 </div>
 ))}
 {discovery.timeline.visits.length > 4 && (
 <p className="text-xs text-muted-foreground">
 +{discovery.timeline.visits.length - 4} more visits
 </p>
 )}
 </div>
 </div>
 )}
 </CardContent>
 </CollapsibleContent>
 </Card>
 </Collapsible>
 </BlurFade>

 {/* 7. Regulatory, Financial & Legal */}
 <BlurFade delay={0.08} inView>
 <Collapsible open={openSections.regulatory} onOpenChange={() => toggleSection('regulatory')}>
 <Card>
 <CollapsibleTrigger asChild>
 <CardHeader className="cursor-pointer hover:bg-gray-50 ">
 <div className="flex items-center justify-between">
 <div className="flex items-center gap-3">
 <Building2 className="h-5 w-5 text-indigo-500" />
 <div>
 <CardTitle>7. Regulatory, Financial & Legal</CardTitle>
 <CardDescription>Governance framework and financial arrangements (10 questions)</CardDescription>
 </div>
 </div>
 <div className="flex items-center gap-2">
 <Badge variant="secondary">
 {(discovery.regulatory?.irbName ? 1 : 0) + 
 (discovery.regulatory?.sponsorName ? 1 : 0) + 
 (discovery.regulatory?.willUseCRO !== undefined ? 1 : 0) + 
 (discovery.regulatory?.dataEvaluationCommittees?.length || 0 > 0 ? 1 : 0) + 
 (discovery.regulatory?.independentCommittees?.length || 0 > 0 ? 1 : 0) + 
 (discovery.regulatory?.willParticipantsBeCompensated !== undefined ? 1 : 0) + 
 (discovery.regulatory?.billingScenarios?.length || 0 > 0 ? 1 : 0)}/10
 </Badge>
 {openSections.regulatory ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
 </div>
 </div>
 </CardHeader>
 </CollapsibleTrigger>
 <CollapsibleContent>
 <CardContent className="space-y-4 pt-0">
 <div className="flex justify-end">
 <Button
 variant="ghost"
 size="sm"
 onClick={() => editSection('regulatory')}
 >
 <Edit2 className="mr-2 h-4 w-4" />
 Edit Section
 </Button>
 </div>
 <div className="grid grid-cols-2 gap-4">
 <div>
 <p className="text-sm font-medium text-muted-foreground">IRB/Ethics Committee</p>
 <p className="text-sm mt-1">{discovery.regulatory?.irbName || "Not specified"}</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Study Sponsor</p>
 <p className="text-sm mt-1">{discovery.regulatory?.sponsorName || "Not specified"}</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">CRO Usage</p>
 <Badge variant={discovery.regulatory?.willUseCRO ? 'default' : 'outline'} className="mt-1">
 {discovery.regulatory?.willUseCRO ? 'Yes' : 'No'}
 </Badge>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Participant Compensation</p>
 <Badge variant={discovery.regulatory?.willParticipantsBeCompensated ? 'default' : 'outline'} className="mt-1">
 {discovery.regulatory?.willParticipantsBeCompensated ? 'Yes' : 'No'}
 </Badge>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Data Evaluation Committees</p>
 <p className="text-sm mt-1">{discovery.regulatory?.dataEvaluationCommittees?.length || 0} committees</p>
 </div>
 <div>
 <p className="text-sm font-medium text-muted-foreground">Independent Committees</p>
 <p className="text-sm mt-1">{discovery.regulatory?.independentCommittees?.length || 0} committees</p>
 </div>
 </div>
 {discovery.regulatory?.billingScenarios && discovery.regulatory.billingScenarios.length > 0 && (
 <div>
 <p className="text-sm font-medium text-muted-foreground">Billing Scenarios ({discovery.regulatory.billingScenarios.length})</p>
 <div className="mt-2 flex flex-wrap gap-1">
 {discovery.regulatory.billingScenarios.map((scenario: string, idx: number) => (
 <Badge key={idx} variant="secondary" className="text-xs">
 {scenario.split('.')[0]}...
 </Badge>
 ))}
 </div>
 </div>
 )}
 </CardContent>
 </CollapsibleContent>
 </Card>
 </Collapsible>
 </BlurFade>

 {/* 8. Progress & Completion Summary */}
 <BlurFade delay={0.09} inView>
 <Collapsible open={openSections.summary} onOpenChange={() => toggleSection('summary')}>
 <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-indigo-50">
 <CollapsibleTrigger asChild>
 <CardHeader className="cursor-pointer hover:bg-purple-50/50">
 <div className="flex items-center justify-between">
 <div className="flex items-center gap-3">
 <CheckCircle className="h-5 w-5 text-green-500" />
 <div>
 <CardTitle className="text-purple-900">8. Progress & Completion Summary</CardTitle>
 <CardDescription className="text-purple-700">Comprehensive workflow coverage and validation status</CardDescription>
 </div>
 </div>
 <div className="flex items-center gap-2">
 <Badge variant="default" className="bg-purple-600">
 {store.completedSteps.length}/8 Steps
 </Badge>
 {openSections.summary ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
 </div>
 </div>
 </CardHeader>
 </CollapsibleTrigger>
 <CollapsibleContent>
 <CardContent className="pt-0">
 <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
 <Card>
 <CardContent className="p-4">
 <div className="flex items-center justify-between">
 <div>
 <p className="text-sm font-medium text-muted-foreground">Workflow Steps</p>
 <p className="text-2xl font-bold">
 {store.completedSteps.length}/{TOTAL_WORKFLOW_STEPS}
 </p>
 </div>
 <CheckCircle className="h-6 w-6 text-green-500" />
 </div>
 </CardContent>
 </Card>

 <Card>
 <CardContent className="p-4">
 <div className="flex items-center justify-between">
 <div>
 <p className="text-sm font-medium text-muted-foreground">Questions Coverage</p>
 <p className="text-2xl font-bold">
 {answeredQuestions}/{TOTAL_QUESTIONS}
 </p>
 </div>
 <TrendingUp className="h-6 w-6 text-purple-500" />
 </div>
 </CardContent>
 </Card>

 <Card>
 <CardContent className="p-4">
 <div className="flex items-center justify-between">
 <div>
 <p className="text-sm font-medium text-muted-foreground">Study Type</p>
 <p className="text-lg font-bold capitalize">
 {discovery.studyType || "Not set"}
 </p>
 </div>
 <FileText className="h-6 w-6 text-blue-500" />
 </div>
 </CardContent>
 </Card>

 <Card>
 <CardContent className="p-4">
 <div className="flex items-center justify-between">
 <div>
 <p className="text-sm font-medium text-muted-foreground">Phase</p>
 <p className="text-lg font-bold uppercase">
 {discovery.phase || "Not set"}
 </p>
 </div>
 <Sparkles className="h-6 w-6 text-purple-500" />
 </div>
 </CardContent>
 </Card>
 </div>

 <div className="rounded-lg bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 p-6">
 <div className="flex items-center gap-3 mb-4">
 <CheckCircle className="h-6 w-6 text-green-600" />
 <h3 className="text-lg font-semibold text-green-900">Comprehensive Protocol Coverage</h3>
 </div>
 <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
 <div className="text-center">
 <p className="text-2xl font-bold text-green-600">{Math.round((answeredQuestions / TOTAL_QUESTIONS) * 100)}%</p>
 <p className="text-green-700">Coverage Rate</p>
 <p className="text-xs text-green-600 mt-1">{answeredQuestions} of {TOTAL_QUESTIONS} critical questions</p>
 </div>
 <div className="text-center">
 <p className="text-2xl font-bold text-blue-600">{TOTAL_WORKFLOW_STEPS}</p>
 <p className="text-blue-700">Workflow Steps</p>
 <p className="text-xs text-blue-600 mt-1">Comprehensive coverage</p>
 </div>
 <div className="text-center">
 <p className="text-2xl font-bold text-purple-600">
 {store.appliedInsightsCount}
 </p>
 <p className="text-purple-700">AI Insights Applied</p>
 <p className="text-xs text-purple-600 mt-1">Evidence-based recommendations</p>
 </div>
 <div className="text-center">
 <p className="text-2xl font-bold text-orange-600">100%</p>
 <p className="text-orange-700">Regulatory Ready</p>
 <p className="text-xs text-orange-600 mt-1">All stakeholder needs addressed</p>
 </div>
 </div>
 <div className="mt-4 text-center">
 <p className="text-sm text-green-800">
 Your study protocol now addresses <strong>all critical regulatory questions</strong> across 
 study design, safety assessment, operational planning, and governance frameworks.
 </p>
 </div>
 </div>
 </CardContent>
 </CollapsibleContent>
 </Card>
 </Collapsible>
 </BlurFade>

 {/* Actions */}
 <BlurFade delay={0.08} inView>
 <Card className="border-[#5A32FA]/20 bg-gradient-to-br from-purple-50 to-teal-50">
 <CardContent className="p-6">
 <div className="flex items-center justify-between">
 <div>
 <h3 className="text-lg font-semibold">Ready to Generate Synopsis?</h3>
 <p className="text-sm text-muted-foreground mt-1">
 Our AI will create a comprehensive clinical trial synopsis based on your inputs and similar studies
 </p>
 </div>
 <Button
 size="lg"
 onClick={handleGenerateSynopsis}
 disabled={isGenerating || generateSynopsis.isPending}
 className="ml-4"
 >
 {isGenerating || generateSynopsis.isPending ? (
 <>
 <Sparkles className="mr-2 h-5 w-5 animate-pulse" />
 Generating...
 </>
 ) : (
 <>
 <Send className="mr-2 h-5 w-5" />
 Generate Synopsis
 </>
 )}
 </Button>
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Generated Synopsis Display */}
 {(isGenerating || Object.keys(generatedSections).length > 0) && (
 <BlurFade delay={0.85} inView>
 <Card className="mt-8">
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <FileText className="h-5 w-5" />
 Generated Study Synopsis
 </CardTitle>
 {isGenerating && currentSection && (
 <CardDescription className="flex items-center gap-2 mt-1">
 <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
 Generating: {currentSection}...
 </CardDescription>
 )}
 </div>
 {Object.keys(generatedSections).length === 8 && (
 <div className="flex gap-2">
 <Button
 size="sm"
 variant="outline"
 onClick={handleCopy}
 >
 {copied ? (
 <>
 <CheckCircle2 className="mr-2 h-4 w-4" />
 Copied!
 </>
 ) : (
 <>
 <Copy className="mr-2 h-4 w-4" />
 Copy
 </>
 )}
 </Button>
 <Button
 size="sm"
 variant="outline"
 onClick={() => handleExport("md")}
 >
 <FileDown className="mr-2 h-4 w-4" />
 Markdown
 </Button>
 <Button
 size="sm"
 variant="outline"
 onClick={() => handleExport("pdf")}
 >
 <Download className="mr-2 h-4 w-4" />
 PDF
 </Button>
 <Button
 size="sm"
 variant="outline"
 onClick={() => handleExport("json")}
 >
 <FileJson className="mr-2 h-4 w-4" />
 JSON
 </Button>
 </div>
 )}
 </div>
 </CardHeader>
 <CardContent>
 <div className="prose prose-sm max-w-none space-y-6">
 {Object.entries(generatedSections).map(([key, content]) => (
 <div key={key} className="border-b pb-4 last:border-0">
 <div 
 className="synopsis-section"
 dangerouslySetInnerHTML={{ 
 __html: markdownToHtml(content)
 }} 
 />
 </div>
 ))}
 {isGenerating && Object.keys(generatedSections).length === 0 && (
 <div className="text-center py-8 text-muted-foreground">
 <div className="mb-4">
 <div className="h-8 w-8 mx-auto animate-spin rounded-full border-4 border-primary border-t-transparent" />
 </div>
 Preparing your study synopsis...
 </div>
 )}
 </div>
 </CardContent>
 </Card>
 </BlurFade>
 )}

 <BlurFade delay={0.09} inView>
 <div className="flex justify-between pt-6">
 <Button
 variant="outline"
 onClick={() => router.push("/study/new/regulatory-financial-legal")}
 >
 <ChevronLeft className="mr-2 h-4 w-4" />
 Back to Regulatory, Financial & Legal
 </Button>
 <div className="flex gap-2">
 {Object.keys(generatedSections).length === 8 && (
 <Button 
 onClick={handleComplete}
 className="bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8]"
 >
 <CheckCircle2 className="mr-2 h-4 w-4" />
 Complete & Save to Dashboard
 </Button>
 )}
 </div>
 </div>
 </BlurFade>
 </div>
 );
}