"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { InsightsButton } from "~/components/insights/InsightsButton";
import { InsightsPanelPortal } from "~/components/insights/InsightsPanelPortal";
import { DocumentViewerPortal } from "~/components/insights/DocumentViewerPortal";
import { SourceStudiesViewerPortal } from "~/components/insights/SourceStudiesViewerPortal";

import { api } from "~/trpc/react";
import { toast } from "sonner";
import { ChevronLeft, Target, Plus, X, FlaskConical, Clock, Activity, Tags } from "lucide-react";
import type { StudyPhase } from "~/types/trial-design";

interface InsightSection {
 title: string;
 content: string;
 citations: Array<{
 id: string;
 title: string;
 url: string;
 relevance: number;
 }>;
 confidence?: number;
}

export default function StudyDesignPage() {
 const router = useRouter();
 const store = useTrialDesignStore();
 
 const [errors, setErrors] = useState<Record<string, boolean>>({});
 const [formData, setFormData] = useState({
 phase: store.discovery.phase || null as StudyPhase | null,
 primaryEndpoint: store.discovery.objectives?.primaryGoal || "",
 keyOutcomeMeasure: store.discovery.objectives?.keyOutcome || "",
 secondaryEndpoints: store.discovery.objectives?.secondaryGoals || [],
 studyDuration: store.discovery.objectives?.studyDuration || "",
 followUpPeriod: store.discovery.objectives?.followUpPeriod || "",
 // Design fields with defaults from store or fallbacks
 designType: store.discovery.design?.designType || "parallel" as "parallel" | "crossover" | "factorial" | "sequential",
 randomizationRatio: store.discovery.design?.randomizationRatio || "1:1",
 blinding: store.discovery.design?.blinding || "double-blind" as "open-label" | "single-blind" | "double-blind" | "triple-blind",
 controlType: store.discovery.design?.controlType || "placebo" as "placebo" | "active-comparator" | "standard-of-care" | "historical" | "none",
 designDescriptors: store.discovery.design?.designDescriptors || [],
 });

 const [newSecondaryEndpoint, setNewSecondaryEndpoint] = useState("");
 const [newDesignDescriptor, setNewDesignDescriptor] = useState("");
 const [activeInsightsPanel, setActiveInsightsPanel] = useState<string | null>(null);
 const [insightsData, setInsightsData] = useState<Record<string, { 
 sections: InsightSection[]; 
 sources?: any[];
 progressStatus?: string;
 progressMessages?: string[];
 }>>({});
 const [documentViewerUrl, setDocumentViewerUrl] = useState<string | null>(null);
 const [sourceStudiesViewerOpen, setSourceStudiesViewerOpen] = useState(false);
 const [sourceStudiesViewerSources, setSourceStudiesViewerSources] = useState<any[]>([]);
 
 // Get cached insights from store
 const cachedInsights = store.insightsCache || {};

 // Helper function to clear error when field is updated
 const handleFieldChange = (fieldName: string, value: any) => {
 setFormData(prev => ({ ...prev, [fieldName]: value }));
 if (errors[fieldName]) {
 setErrors(prev => ({ ...prev, [fieldName]: false }));
 }
 };

 // Query knowledge base for insights
 const queryInsights = api.knowledgeBase.queryInsights.useMutation({
 onSuccess: (data, variables) => {
 const insightsPayload = {
 sections: data.sections || [],
 sources: data.sources || []
 };
 
 // Update local state
 setInsightsData(prev => ({
 ...prev,
 [variables.field]: insightsPayload
 }));
 
 // Cache the insights in the store
 store.cacheInsights(variables.field, insightsPayload);
 
 setActiveInsightsPanel(variables.field);
 },
 onError: (error) => {
 toast.error("Failed to get insights: " + error.message);
 },
 });

 const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
 onSuccess: () => {
 store.markStepCompleted("study-design");
 router.push("/study/new/study-population");
 },
 onError: (error) => {
 toast.error("Failed to save: " + error.message);
 },
 });

 const getProgressUpdatesForField = (field: string) => {
 switch (field) {
 case "phase":
 return [
 { delay: 0, message: 'Initializing search...' },
 { delay: 1500, message: 'Searching knowledge base...' },
 { delay: 3500, message: 'Analyzing phase recommendations...' },
 { delay: 6000, message: 'Evaluating similar studies...' },
 { delay: 8500, message: 'Generating phase guidance...' },
 ];
 case "primary-endpoint":
 return [
 { delay: 0, message: 'Initializing search...' },
 { delay: 1500, message: 'Searching knowledge base...' },
 { delay: 3500, message: 'Analyzing endpoint strategies...' },
 { delay: 6000, message: 'Reviewing regulatory precedents...' },
 { delay: 8500, message: 'Generating endpoint recommendations...' },
 ];
 case "secondary-endpoints":
 return [
 { delay: 0, message: 'Initializing search...' },
 { delay: 1500, message: 'Searching knowledge base...' },
 { delay: 3500, message: 'Identifying secondary measures...' },
 { delay: 6000, message: 'Analyzing outcome hierarchies...' },
 { delay: 8500, message: 'Compiling endpoint options...' },
 ];
 case "study-duration":
 return [
 { delay: 0, message: 'Initializing search...' },
 { delay: 1500, message: 'Searching knowledge base...' },
 { delay: 3500, message: 'Analyzing timeline patterns...' },
 { delay: 6000, message: 'Calculating optimal durations...' },
 { delay: 8500, message: 'Generating timeline recommendations...' },
 ];
 case "design-parameters":
 return [
 { delay: 0, message: 'Initializing search...' },
 { delay: 1500, message: 'Searching knowledge base...' },
 { delay: 3500, message: 'Analyzing design patterns...' },
 { delay: 6000, message: 'Evaluating blinding strategies...' },
 { delay: 8500, message: 'Generating design recommendations...' },
 ];
 default:
 return [
 { delay: 0, message: 'Initializing search...' },
 { delay: 1500, message: 'Searching knowledge base...' },
 { delay: 3500, message: 'Analyzing relevant studies...' },
 { delay: 8500, message: 'Generating recommendations...' },
 ];
 }
 };

 const handleGetInsights = async (field: string, forceRefresh = false) => {
 // Check cache first unless forcing refresh
 if (!forceRefresh && cachedInsights[field]) {
 // Use cached data
 setInsightsData(prev => ({
 ...prev,
 [field]: cachedInsights[field]
 }));
 setActiveInsightsPanel(field);
 return;
 }
 
 // Show panel immediately with loading state
 setActiveInsightsPanel(field);
 
 // Set initial progress status
 setInsightsData(prev => ({
 ...prev,
 [field]: {
 sections: [],
 sources: [],
 progressStatus: 'Initializing search...',
 progressMessages: [],
 }
 }));
 
 // Progressive status updates based on field type
 const progressUpdates = getProgressUpdatesForField(field);
 
 // Update progress status progressively
 progressUpdates.forEach(({ delay, message }) => {
 setTimeout(() => {
 setInsightsData(prev => {
 const current = prev[field];
 // Only update if we're still loading (haven't received results yet)
 if (current && !current.sections?.length) {
 return {
 ...prev,
 [field]: {
 ...current,
 progressStatus: message,
 progressMessages: [...(current.progressMessages || []), message],
 }
 };
 }
 return prev;
 });
 }, delay);
 });
 
 const context = {
 studyType: store.discovery.studyType || undefined,
 condition: store.discovery.condition || undefined,
 drugClass: store.discovery.intervention.class || undefined,
 deviceClass: store.discovery.intervention.deviceClass || undefined,
 isNewCompound: store.discovery.intervention.isNewCompound || undefined,
 mechanism: store.discovery.intervention.mechanism || undefined,
 phase: formData.phase || undefined,
 // Include design parameters when requesting primary endpoint
 ...(field === 'primary-endpoint' ? {
 designType: formData.designType || undefined,
 blinding: formData.blinding || undefined,
 controlType: formData.controlType || undefined,
 randomizationRatio: formData.randomizationRatio || undefined,
 } : {}),
 // Include primary endpoint when requesting secondary endpoints or design parameters
 ...(field === 'secondary-endpoints' || field === 'design-parameters' ? {
 primaryEndpoint: formData.primaryEndpoint && formData.primaryEndpoint.trim() !== '' 
 ? formData.primaryEndpoint.trim() 
 : 'Not yet determined'
 } : {}),
 // Include ALL design parameters when requesting design descriptors
 ...(field === 'design-descriptors' ? {
 designType: formData.designType || undefined,
 blinding: formData.blinding || undefined,
 controlType: formData.controlType || undefined,
 randomizationRatio: formData.randomizationRatio || undefined,
 primaryEndpoint: formData.primaryEndpoint && formData.primaryEndpoint.trim() !== '' 
 ? formData.primaryEndpoint.trim() 
 : 'Not yet determined'
 } : {})
 };

 const query = getQueryForField(field);

 // Log the query details to browser console
 console.log("=== INSIGHTS QUERY TO KNOWLEDGE BASE ===");
 console.log("Field:", field);
 console.log("Query:", query);
 console.log("Context:", context);
 console.log("Session ID:", store.sessionId);
 console.log("Using cache:", !forceRefresh && !!cachedInsights[field]);
 console.log("==========================================");

 await queryInsights.mutateAsync({
 sessionId: store.sessionId!,
 field,
 context,
 query,
 });
 };

 const getQueryForField = (field: string) => {
 switch (field) {
 case "phase":
 return `What phase should I choose for a ${store.discovery.intervention.isNewCompound ? 'novel' : 'existing'} ${store.discovery.intervention.class || 'drug'} targeting ${store.discovery.condition}?`;
 case "primary-endpoint":
 return `What are appropriate primary endpoints for a ${store.discovery.studyType} study in ${store.discovery.condition}?`;
 case "secondary-endpoints":
 return `What secondary endpoints are commonly used in ${store.discovery.condition} trials?`;
 case "study-duration":
 return `What is the typical study duration and follow-up period for ${store.discovery.condition} trials?`;
 case "design-parameters":
 return `What study design parameters (design type, blinding, control type, randomization) are most appropriate for a ${formData.phase || 'Phase 2'} ${store.discovery.studyType} trial in ${store.discovery.condition}?`;
 case "design-descriptors":
 return `What study design descriptors and classifications are appropriate for a ${formData.designType || 'parallel'} ${formData.blinding || 'double-blind'} ${formData.controlType || 'placebo'} ${formData.phase || 'Phase 2'} trial in ${store.discovery.condition}?`;
 default:
 return "";
 }
 };

 const handleApplySuggestion = (field: string, suggestion: string, actionableData?: any) => {
 // Parse and apply the suggestion based on the field
 if (field === "phase") {
 // Extract phase from suggestion text
 const phaseMatch = suggestion.match(/phase\s*([1-4]|I{1,3}|IV)/i);
 if (phaseMatch) {
 const phaseMap: Record<string, StudyPhase> = {
 "1": "phase1", "I": "phase1",
 "2": "phase2", "II": "phase2", 
 "3": "phase3", "III": "phase3",
 "4": "phase4", "IV": "phase4",
 };
 const matchKey = phaseMatch[1];
 if (matchKey) {
 const phase = phaseMap[matchKey.toUpperCase()];
 if (phase) {
 setFormData(prev => ({ ...prev, phase }));
 toast.success("Phase recommendation applied");
 }
 }
 }
 } else if (field === "primary-endpoint") {
 // Use actionableData if available (from JSON response)
 if (actionableData && actionableData.field === 'primaryEndpoint') {
 setFormData(prev => ({ 
 ...prev, 
 primaryEndpoint: actionableData.value,
 keyOutcomeMeasure: actionableData.measurementMethod || prev.keyOutcomeMeasure
 }));
 toast.success("Primary endpoint applied");
 } else {
 // Fallback to setting suggestion directly
 setFormData(prev => ({ ...prev, primaryEndpoint: suggestion }));
 toast.success("Primary endpoint applied");
 }
 } else if (field === "secondary-endpoints") {
 // Keep the full endpoint text with description, just format it nicely
 const endpointText = suggestion.replace(/:\s+/, ' - ').trim();
 
 // Check if this endpoint is already in the list (check both exact match and title match)
 const endpointTitle = endpointText.split(' - ')[0];
 const isDuplicate = formData.secondaryEndpoints.some(ep => 
 ep === endpointText || ep.startsWith(endpointTitle + ' - ')
 );
 
 if (isDuplicate) {
 toast.info("This endpoint is already in your list");
 return; // Don't close panel
 }
 
 // Add to the array of secondary endpoints
 setFormData(prev => ({
 ...prev,
 secondaryEndpoints: [...prev.secondaryEndpoints, endpointText]
 }));
 toast.success("Secondary endpoint added");
 
 // Don't close the panel so user can add more endpoints
 return;
 } else if (field === "study-duration") {
 // Check if the suggestion contains "Recommended:" which indicates structured data
 if (suggestion.includes("Recommended:")) {
 // Parse structured recommendation
 const recommendedMatch = suggestion.match(/Recommended:\s*([^\n]+)/);
 if (recommendedMatch && recommendedMatch[1]) {
 const value = recommendedMatch[1].trim();
 
 // Check if this is for follow-up period or treatment duration
 if (suggestion.toLowerCase().includes("follow") || suggestion.toLowerCase().includes("follow-up")) {
 setFormData(prev => ({ ...prev, followUpPeriod: value }));
 toast.success("Follow-up period applied");
 } else {
 setFormData(prev => ({ ...prev, studyDuration: value }));
 toast.success("Study duration applied");
 }
 }
 } else {
 // Fallback: Extract duration from plain text suggestion
 const durationMatch = suggestion.match(/(\d+)\s*(weeks?|months?|years?)/i);
 if (durationMatch) {
 setFormData(prev => ({ ...prev, studyDuration: `${durationMatch[1]} ${durationMatch[2]}` }));
 toast.success("Study duration applied");
 }
 }
 // Don't close the panel so user can apply both duration fields
 return;
 } else if (field === "design-parameters") {
 // Use actionableData if available (from JSON response)
 if (actionableData) {
 const updates: any = {};
 
 if (actionableData.field === 'designType' && actionableData.value) {
 updates.designType = actionableData.value;
 } else if (actionableData.field === 'blinding' && actionableData.value) {
 updates.blinding = actionableData.value;
 } else if (actionableData.field === 'controlType' && actionableData.value) {
 updates.controlType = actionableData.value;
 } else if (actionableData.field === 'randomizationRatio' && actionableData.value) {
 updates.randomizationRatio = actionableData.value;
 }
 
 if (Object.keys(updates).length > 0) {
 setFormData(prev => ({ ...prev, ...updates }));
 toast.success(`${actionableData.field.replace(/([A-Z])/g, ' $1').replace(/^./, (str: string) => str.toUpperCase())} updated`);
 }
 } else {
 // Fallback to regex parsing if no actionableData
 const designTypeMatch = suggestion.match(/(?:parallel|crossover|factorial|adaptive|platform|basket|umbrella|sequential|cluster|stepped-wedge|n-of-1|single-arm)/i);
 const blindingMatch = suggestion.match(/(?:open-label|single-blind|double-blind|triple-blind|quadruple-blind)/i);
 const controlMatch = suggestion.match(/(?:placebo|active-comparator|standard-of-care|dose-comparison|sham|historical|waitlist|usual-care|no-treatment)/i);
 const ratioMatch = suggestion.match(/(\d+):(\d+)(?::(\d+))?/);
 
 if (designTypeMatch) {
 const designType = designTypeMatch[0].toLowerCase().replace(/-/g, '-');
 setFormData(prev => ({ ...prev, designType: designType as any }));
 }
 if (blindingMatch) {
 const blinding = blindingMatch[0].toLowerCase();
 setFormData(prev => ({ ...prev, blinding: blinding as any }));
 }
 if (controlMatch) {
 const controlType = controlMatch[0].toLowerCase().replace(/ /g, '-');
 setFormData(prev => ({ ...prev, controlType: controlType as any }));
 }
 if (ratioMatch) {
 setFormData(prev => ({ ...prev, randomizationRatio: ratioMatch[0] }));
 }
 
 toast.success("Design parameters updated");
 }
 // Don't close panel so user can review all recommendations
 return;
 } else if (field === "study-design") {
 // Use actionableData if available (from JSON response)
 if (actionableData) {
 const updates: any = {};
 
 if (actionableData.field === 'designType' && actionableData.value) {
 updates.designType = actionableData.value;
 } else if (actionableData.field === 'blinding' && actionableData.value) {
 updates.blinding = actionableData.value;
 } else if (actionableData.field === 'controlType' && actionableData.value) {
 updates.controlType = actionableData.value;
 } else if (actionableData.field === 'randomizationRatio' && actionableData.value) {
 updates.randomizationRatio = actionableData.value;
 }
 
 if (Object.keys(updates).length > 0) {
 setFormData(prev => ({ ...prev, ...updates }));
 toast.success(`${actionableData.field.replace(/([A-Z])/g, ' $1').replace(/^./, (str: string) => str.toUpperCase())} updated`);
 }
 } else {
 // Fallback to regex parsing if no actionableData
 const designTypeMatch = suggestion.match(/(?:parallel|crossover|factorial|adaptive|platform|basket|umbrella|sequential|cluster|stepped-wedge|n-of-1|single-arm)/i);
 const blindingMatch = suggestion.match(/(?:open-label|single-blind|double-blind|triple-blind|quadruple-blind)/i);
 const controlMatch = suggestion.match(/(?:placebo|active-comparator|standard-of-care|dose-comparison|sham|historical|waitlist|usual-care|no-treatment)/i);
 const ratioMatch = suggestion.match(/(\d+):(\d+)(?::(\d+))?/);
 
 if (designTypeMatch) {
 const designType = designTypeMatch[0].toLowerCase().replace(/-/g, '-');
 setFormData(prev => ({ ...prev, designType: designType as any }));
 }
 if (blindingMatch) {
 const blinding = blindingMatch[0].toLowerCase();
 setFormData(prev => ({ ...prev, blinding: blinding as any }));
 }
 if (controlMatch) {
 const controlType = controlMatch[0].toLowerCase().replace(/ /g, '-');
 setFormData(prev => ({ ...prev, controlType: controlType as any }));
 }
 if (ratioMatch) {
 setFormData(prev => ({ ...prev, randomizationRatio: ratioMatch[0] }));
 }
 
 toast.success("Study design updated");
 }
 // Don't close panel so user can review all recommendations
 return;
 } else if (field === "design-descriptors") {
 // Use actionableData if available (from JSON response)
 if (actionableData && actionableData.field === 'designDescriptor') {
 const descriptor = actionableData.value.trim();
 
 // Check for duplicates
 if (!formData.designDescriptors.includes(descriptor)) {
 setFormData(prev => ({
 ...prev,
 designDescriptors: [...prev.designDescriptors, descriptor]
 }));
 toast.success(`"${descriptor}" added to design descriptors`);
 } else {
 toast.info("This descriptor is already in your list");
 }
 } else {
 // Fallback: treat the suggestion text as a descriptor
 const descriptor = suggestion.trim();
 if (!formData.designDescriptors.includes(descriptor)) {
 setFormData(prev => ({
 ...prev,
 designDescriptors: [...prev.designDescriptors, descriptor]
 }));
 toast.success("Design descriptor added");
 } else {
 toast.info("This descriptor is already in your list");
 }
 }
 // Don't close panel so user can add multiple descriptors
 return;
 } else {
 setActiveInsightsPanel(null);
 }
 };

 const handleContinue = () => {
 if (!store.sessionId) {
 toast.error("Session not found");
 return;
 }

 // Comprehensive validation
 const newErrors: Record<string, boolean> = {};
 let hasError = false;
 let firstErrorField: string | null = null;

 // Required field validations
 if (!formData.phase) {
 newErrors.phase = true;
 hasError = true;
 if (!firstErrorField) firstErrorField = "phase";
 }

 if (!formData.primaryEndpoint) {
 newErrors.primaryEndpoint = true;
 hasError = true;
 if (!firstErrorField) firstErrorField = "primaryEndpoint";
 }

 if (!formData.keyOutcomeMeasure) {
 newErrors.keyOutcomeMeasure = true;
 hasError = true;
 if (!firstErrorField) firstErrorField = "keyOutcomeMeasure";
 }

 if (!formData.studyDuration) {
 newErrors.studyDuration = true;
 hasError = true;
 if (!firstErrorField) firstErrorField = "studyDuration";
 }

 if (!formData.followUpPeriod) {
 newErrors.followUpPeriod = true;
 hasError = true;
 if (!firstErrorField) firstErrorField = "followUpPeriod";
 }

 if (!formData.designType) {
 newErrors.designType = true;
 hasError = true;
 if (!firstErrorField) firstErrorField = "designType";
 }

 if (!formData.randomizationRatio) {
 newErrors.randomizationRatio = true;
 hasError = true;
 if (!firstErrorField) firstErrorField = "randomizationRatio";
 }

 if (!formData.blinding) {
 newErrors.blinding = true;
 hasError = true;
 if (!firstErrorField) firstErrorField = "blinding";
 }

 if (!formData.controlType) {
 newErrors.controlType = true;
 hasError = true;
 if (!firstErrorField) firstErrorField = "controlType";
 }

 setErrors(newErrors);

 if (hasError) {
 toast.error("Please complete all required fields");
 
 // Scroll to first error field
 if (firstErrorField) {
 const element = document.getElementById(firstErrorField);
 if (element) {
 element.scrollIntoView({ behavior: "smooth", block: "center" });
 setTimeout(() => {
 element.focus();
 }, 500);
 }
 }
 return;
 }

 // Update store
 store.updateDiscovery({ 
 phase: formData.phase,
 objectives: {
 primaryGoal: formData.primaryEndpoint,
 keyOutcome: formData.keyOutcomeMeasure,
 secondaryGoals: formData.secondaryEndpoints,
 studyDuration: formData.studyDuration,
 followUpPeriod: formData.followUpPeriod,
 },
 design: {
 designType: formData.designType,
 randomizationRatio: formData.randomizationRatio,
 blinding: formData.blinding,
 controlType: formData.controlType,
 designDescriptors: formData.designDescriptors,
 }
 });

 // Save to backend
 saveDiscovery.mutate({
 sessionId: store.sessionId,
 data: {
 phase: formData.phase,
 objectives: {
 primaryGoal: formData.primaryEndpoint,
 keyOutcome: formData.keyOutcomeMeasure,
 secondaryGoals: formData.secondaryEndpoints,
 studyDuration: formData.studyDuration,
 followUpPeriod: formData.followUpPeriod,
 },
 design: {
 designType: formData.designType,
 randomizationRatio: formData.randomizationRatio,
 blinding: formData.blinding,
 controlType: formData.controlType,
 designDescriptors: formData.designDescriptors,
 }
 },
 });
 };

 const addSecondaryEndpoint = () => {
 if (newSecondaryEndpoint.trim()) {
 setFormData({
 ...formData,
 secondaryEndpoints: [...formData.secondaryEndpoints, newSecondaryEndpoint.trim()],
 });
 setNewSecondaryEndpoint("");
 }
 };

 const removeSecondaryEndpoint = (index: number) => {
 setFormData({
 ...formData,
 secondaryEndpoints: formData.secondaryEndpoints.filter((_, i) => i !== index),
 });
 };

 const addDesignDescriptor = () => {
 if (newDesignDescriptor.trim()) {
 // Check for duplicates
 const descriptor = newDesignDescriptor.trim();
 if (!formData.designDescriptors.includes(descriptor)) {
 setFormData({
 ...formData,
 designDescriptors: [...formData.designDescriptors, descriptor],
 });
 setNewDesignDescriptor("");
 } else {
 toast.info("This descriptor is already in your list");
 }
 }
 };

 const removeDesignDescriptor = (index: number) => {
 setFormData({
 ...formData,
 designDescriptors: formData.designDescriptors.filter((_, i) => i !== index),
 });
 };

 const handleViewAllSources = () => {
 if (activeInsightsPanel && (insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources)) {
 const sources = insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || [];
 setSourceStudiesViewerSources(sources);
 setSourceStudiesViewerOpen(true);
 }
 };

 return (
 <div className="space-y-8">
 <BlurFade delay={0.01} inView>
 <div>
 <h1 className="text-3xl font-bold text-gray-900">
 Study Design
 </h1>
 <p className="mt-2 text-lg text-gray-600">
 Let's design your study with insights from similar trials
 </p>
 </div>
 </BlurFade>


 <BlurFade delay={0.03} inView>
 <Card className={errors.phase ? "border-red-500" : ""}>
 <CardHeader>
 <CardTitle className="flex items-center gap-2">
 <FlaskConical className="h-5 w-5" />
 Study Phase *
 </CardTitle>
 <CardDescription>
 Select the appropriate phase for your trial
 </CardDescription>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="flex items-center justify-between" id="phase">
 <RadioGroup
 value={formData.phase || ""}
 onValueChange={(value) => handleFieldChange("phase", value as StudyPhase)}
 className="flex-1"
 >
 <div className="flex items-center space-x-2">
 <RadioGroupItem value="phase1" id="phase1" />
 <Label htmlFor="phase1" className="font-normal">
 Phase 1 - Safety, dosing, pharmacokinetics
 </Label>
 </div>
 <div className="flex items-center space-x-2">
 <RadioGroupItem value="phase2" id="phase2" />
 <Label htmlFor="phase2" className="font-normal">
 Phase 2 - Efficacy and side effects
 </Label>
 </div>
 <div className="flex items-center space-x-2">
 <RadioGroupItem value="phase3" id="phase3" />
 <Label htmlFor="phase3" className="font-normal">
 Phase 3 - Efficacy comparison and monitoring
 </Label>
 </div>
 <div className="flex items-center space-x-2">
 <RadioGroupItem value="phase4" id="phase4" />
 <Label htmlFor="phase4" className="font-normal">
 Phase 4 - Post-market surveillance
 </Label>
 </div>
 </RadioGroup>
 <InsightsButton
 onClick={() => handleGetInsights("phase")}
 onRefresh={() => handleGetInsights("phase", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "phase"}
 hasCachedData={!!cachedInsights["phase"]}
 showRefresh={!!cachedInsights["phase"]}
 className="ml-4"
 />
 </div>
 {errors.phase && (
 <p className="text-sm text-red-500">Please select a study phase</p>
 )}
 </CardContent>
 </Card>
 </BlurFade>

 <BlurFade delay={0.35} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <Activity className="h-5 w-5" />
 Study Design Parameters
 </CardTitle>
 <CardDescription>
 Design structure, randomization, and blinding
 </CardDescription>
 </div>
 <InsightsButton
 onClick={() => handleGetInsights("design-parameters")}
 onRefresh={() => handleGetInsights("design-parameters", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "design-parameters"}
 hasCachedData={!!cachedInsights["design-parameters"]}
 showRefresh={!!cachedInsights["design-parameters"]}
 />
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="grid grid-cols-2 gap-4">
 <div className="space-y-2">
 <Label htmlFor="designType">Study Design Type *</Label>
 <Select
 value={formData.designType}
 onValueChange={(value) => handleFieldChange("designType", value)}
 >
 <SelectTrigger id="designType" className={errors.designType ? "border-red-500 focus:ring-red-500" : ""}>
 <SelectValue />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="parallel">Parallel Group</SelectItem>
 <SelectItem value="crossover">Crossover</SelectItem>
 <SelectItem value="factorial">Factorial</SelectItem>
 <SelectItem value="sequential">Sequential</SelectItem>
 <SelectItem value="adaptive">Adaptive</SelectItem>
 <SelectItem value="cluster">Cluster Randomized</SelectItem>
 <SelectItem value="stepped-wedge">Stepped Wedge</SelectItem>
 <SelectItem value="n-of-1">N-of-1</SelectItem>
 <SelectItem value="single-arm">Single Arm</SelectItem>
 <SelectItem value="basket">Basket Trial</SelectItem>
 <SelectItem value="umbrella">Umbrella Trial</SelectItem>
 <SelectItem value="platform">Platform Trial</SelectItem>
 </SelectContent>
 </Select>
 {errors.designType && (
 <p className="text-sm text-red-500">This field is required</p>
 )}
 </div>
 <div className="space-y-2">
 <Label htmlFor="randomizationRatio">Randomization Ratio *</Label>
 <Input
 id="randomizationRatio"
 placeholder="e.g., 1:1, 2:1, 1:1:1"
 value={formData.randomizationRatio}
 onChange={(e) => handleFieldChange("randomizationRatio", e.target.value)}
 className={errors.randomizationRatio ? "border-red-500 focus:ring-red-500" : ""}
 />
 {errors.randomizationRatio && (
 <p className="text-sm text-red-500">This field is required</p>
 )}
 </div>
 </div>
 <div className="grid grid-cols-2 gap-4">
 <div className="space-y-2">
 <Label htmlFor="blinding">Blinding *</Label>
 <Select
 value={formData.blinding}
 onValueChange={(value) => handleFieldChange("blinding", value)}
 >
 <SelectTrigger id="blinding" className={errors.blinding ? "border-red-500 focus:ring-red-500" : ""}>
 <SelectValue />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="open-label">Open Label (No Blinding)</SelectItem>
 <SelectItem value="single-blind">Single Blind (Participant)</SelectItem>
 <SelectItem value="single-blind-investigator">Single Blind (Investigator)</SelectItem>
 <SelectItem value="single-blind-assessor">Single Blind (Assessor)</SelectItem>
 <SelectItem value="double-blind">Double Blind (Participant + Investigator)</SelectItem>
 <SelectItem value="triple-blind">Triple Blind (Participant + Investigator + Assessor)</SelectItem>
 <SelectItem value="quadruple-blind">Quadruple Blind (All Parties)</SelectItem>
 </SelectContent>
 </Select>
 {errors.blinding && (
 <p className="text-sm text-red-500">This field is required</p>
 )}
 </div>
 <div className="space-y-2">
 <Label htmlFor="controlType">Control Type *</Label>
 <Select
 value={formData.controlType}
 onValueChange={(value) => handleFieldChange("controlType", value)}
 >
 <SelectTrigger id="controlType" className={errors.controlType ? "border-red-500 focus:ring-red-500" : ""}>
 <SelectValue />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="placebo">Placebo Control</SelectItem>
 <SelectItem value="active-comparator">Active Comparator</SelectItem>
 <SelectItem value="standard-of-care">Standard of Care</SelectItem>
 <SelectItem value="dose-comparison">Dose Comparison</SelectItem>
 <SelectItem value="sham">Sham Procedure</SelectItem>
 <SelectItem value="historical">Historical Control</SelectItem>
 <SelectItem value="waitlist">Waitlist Control</SelectItem>
 <SelectItem value="usual-care">Usual Care</SelectItem>
 <SelectItem value="no-treatment">No Treatment</SelectItem>
 <SelectItem value="none">No Control (Single Arm)</SelectItem>
 </SelectContent>
 </Select>
 {errors.controlType && (
 <p className="text-sm text-red-500">This field is required</p>
 )}
 </div>
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 <BlurFade delay={0.375} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <Tags className="h-5 w-5" />
 Study Design Descriptors
 </CardTitle>
 <CardDescription>
 Additional design classifications and descriptors
 </CardDescription>
 </div>
 <InsightsButton
 onClick={() => handleGetInsights("design-descriptors")}
 onRefresh={() => handleGetInsights("design-descriptors", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "design-descriptors"}
 hasCachedData={!!cachedInsights["design-descriptors"]}
 showRefresh={!!cachedInsights["design-descriptors"]}
 />
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 {formData.designDescriptors.map((descriptor, index) => (
 <div key={index} className="flex items-center gap-2 rounded-lg border p-3">
 <span className="flex-1 text-sm">{descriptor}</span>
 <Button
 size="icon"
 variant="ghost"
 onClick={() => removeDesignDescriptor(index)}
 >
 <X className="h-4 w-4" />
 </Button>
 </div>
 ))}
 
 <div className="flex gap-2">
 <Input
 placeholder="Add design descriptor..."
 value={newDesignDescriptor}
 onChange={(e) => setNewDesignDescriptor(e.target.value)}
 onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addDesignDescriptor())}
 />
 <Button onClick={addDesignDescriptor} size="icon">
 <Plus className="h-4 w-4" />
 </Button>
 </div>
 </CardContent>
 </Card>
 </BlurFade>


 <BlurFade delay={0.425} inView>
 <Card>
 <CardHeader>
 <CardTitle className="flex items-center gap-2">
 <Target className="h-5 w-5" />
 Primary Endpoint
 </CardTitle>
 <CardDescription>
 The main outcome measure for your study
 </CardDescription>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="space-y-2">
 <div className="flex items-center justify-between">
 <Label htmlFor="primaryEndpoint">Primary Endpoint</Label>
 <InsightsButton
 onClick={() => handleGetInsights("primary-endpoint")}
 onRefresh={() => handleGetInsights("primary-endpoint", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "primary-endpoint"}
 hasCachedData={!!cachedInsights["primary-endpoint"]}
 showRefresh={!!cachedInsights["primary-endpoint"]}
 />
 </div>
 <Textarea
 id="primaryEndpoint"
 placeholder="e.g., Change in HbA1c from baseline at 12 weeks"
 value={formData.primaryEndpoint}
 onChange={(e) => handleFieldChange("primaryEndpoint", e.target.value)}
 rows={2}
 className={errors.primaryEndpoint ? "border-red-500 focus:ring-red-500" : ""}
 />
 {errors.primaryEndpoint && (
 <p className="text-sm text-red-500">This field is required</p>
 )}
 </div>

 <div className="space-y-2">
 <Label htmlFor="keyOutcomeMeasure">Measurement Details *</Label>
 <Input
 id="keyOutcomeMeasure"
 placeholder="e.g., Percentage point change, measured via laboratory test"
 value={formData.keyOutcomeMeasure}
 onChange={(e) => handleFieldChange("keyOutcomeMeasure", e.target.value)}
 className={errors.keyOutcomeMeasure ? "border-red-500 focus:ring-red-500" : ""}
 />
 {errors.keyOutcomeMeasure && (
 <p className="text-sm text-red-500">This field is required</p>
 )}
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 <BlurFade delay={0.525} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle>Secondary Endpoints</CardTitle>
 <CardDescription>
 Additional outcomes to measure (optional)
 </CardDescription>
 </div>
 <InsightsButton
 onClick={() => handleGetInsights("secondary-endpoints")}
 onRefresh={() => handleGetInsights("secondary-endpoints", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "secondary-endpoints"}
 hasCachedData={!!cachedInsights["secondary-endpoints"]}
 showRefresh={!!cachedInsights["secondary-endpoints"]}
 />
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 {formData.secondaryEndpoints.map((endpoint, index) => (
 <div key={index} className="flex items-center gap-2 rounded-lg border p-3">
 <span className="flex-1 text-sm">{endpoint}</span>
 <Button
 size="icon"
 variant="ghost"
 onClick={() => removeSecondaryEndpoint(index)}
 >
 <X className="h-4 w-4" />
 </Button>
 </div>
 ))}
 
 <div className="flex gap-2">
 <Input
 placeholder="Add secondary endpoint..."
 value={newSecondaryEndpoint}
 onChange={(e) => setNewSecondaryEndpoint(e.target.value)}
 onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addSecondaryEndpoint())}
 />
 <Button onClick={addSecondaryEndpoint} size="icon">
 <Plus className="h-4 w-4" />
 </Button>
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 <BlurFade delay={0.625} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <Clock className="h-5 w-5" />
 Study Timeline
 </CardTitle>
 <CardDescription>
 Expected duration and follow-up period
 </CardDescription>
 </div>
 <InsightsButton
 onClick={() => handleGetInsights("study-duration")}
 onRefresh={() => handleGetInsights("study-duration", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "study-duration"}
 hasCachedData={!!cachedInsights["study-duration"]}
 showRefresh={!!cachedInsights["study-duration"]}
 />
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="grid grid-cols-2 gap-4">
 <div className="space-y-2">
 <Label htmlFor="studyDuration">Study Duration *</Label>
 <Input
 id="studyDuration"
 placeholder="e.g., 12 weeks, 6 months"
 value={formData.studyDuration}
 onChange={(e) => handleFieldChange("studyDuration", e.target.value)}
 className={errors.studyDuration ? "border-red-500 focus:ring-red-500" : ""}
 />
 {errors.studyDuration && (
 <p className="text-sm text-red-500">This field is required</p>
 )}
 </div>
 <div className="space-y-2">
 <Label htmlFor="followUpPeriod">Follow-up Period *</Label>
 <Input
 id="followUpPeriod"
 placeholder="e.g., 4 weeks, 3 months"
 value={formData.followUpPeriod}
 onChange={(e) => handleFieldChange("followUpPeriod", e.target.value)}
 className={errors.followUpPeriod ? "border-red-500 focus:ring-red-500" : ""}
 />
 {errors.followUpPeriod && (
 <p className="text-sm text-red-500">This field is required</p>
 )}
 </div>
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 <BlurFade delay={0.725} inView>
 <div className="flex justify-between pt-6">
 <Button
 variant="outline"
 onClick={() => router.push("/study/new/basics")}
 >
 <ChevronLeft className="mr-2 h-4 w-4" />
 Back
 </Button>
 <Button
 onClick={handleContinue}
 disabled={saveDiscovery.isPending}
 >
 {saveDiscovery.isPending ? "Saving..." : "Continue to Population"}
 </Button>
 </div>
 </BlurFade>

 {/* Insights Panel */}
 {activeInsightsPanel && (
 <InsightsPanelPortal
 isOpen={true}
 onClose={() => setActiveInsightsPanel(null)}
 title={`Insights: ${activeInsightsPanel.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase())}`}
 description="Recommendations based on similar studies in our knowledge base"
 loading={queryInsights.isPending}
 progressStatus={insightsData[activeInsightsPanel]?.progressStatus}
 progressMessages={insightsData[activeInsightsPanel]?.progressMessages || []}
 sections={insightsData[activeInsightsPanel]?.sections || cachedInsights[activeInsightsPanel]?.sections || []}
 sources={insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || []}
 onDocumentClick={(url) => setDocumentViewerUrl(url)}
 onViewAllSources={handleViewAllSources}
 onApplySuggestion={(suggestion, actionableData) => handleApplySuggestion(activeInsightsPanel, suggestion, actionableData)}
 />
 )}

 {/* Document Viewer */}
 <DocumentViewerPortal
 isOpen={!!documentViewerUrl}
 onClose={() => setDocumentViewerUrl(null)}
 documentUrl={documentViewerUrl}
 loading={false}
 citations={(() => {
 // Extract all unique citations from all sections
 const allCitations = new Map<string, any>();
 Object.values(insightsData).forEach(data => {
 data.sections?.forEach((section: any) => {
 if (section.citations && Array.isArray(section.citations)) {
 section.citations.forEach((citation: any) => {
 if (citation.id && !allCitations.has(citation.id)) {
 allCitations.set(citation.id, citation);
 }
 });
 }
 });
 });
 return Array.from(allCitations.values());
 })()}
 sources={(() => {
 // Extract all unique sources from all insights data
 const allSources = new Map<string, any>();
 Object.values(insightsData).forEach(data => {
 data.sources?.forEach((source: any) => {
 if (source.nctId && !allSources.has(source.nctId)) {
 allSources.set(source.nctId, source);
 }
 });
 });
 return Array.from(allSources.values());
 })()}
 onStudySelect={(studyUrl) => setDocumentViewerUrl(studyUrl)}
 currentStudyId={(() => {
 // Extract NCT ID from the current document URL
 if (!documentViewerUrl) return undefined;
 const match = documentViewerUrl.match(/NCT\d+/i);
 return match ? match[0] : undefined;
 })()}
 />
 
 <SourceStudiesViewerPortal
 isOpen={sourceStudiesViewerOpen}
 onClose={() => setSourceStudiesViewerOpen(false)}
 sources={sourceStudiesViewerSources}
 onDocumentClick={(url) => setDocumentViewerUrl(url)}
 />

 </div>
 );
}