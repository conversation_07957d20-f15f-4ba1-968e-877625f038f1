"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { InsightsButton } from "~/components/insights/InsightsButton";
import { InsightsPanelPortal } from "~/components/insights/InsightsPanelPortal";
import { DocumentViewerPortal } from "~/components/insights/DocumentViewerPortal";
import { ConfirmationPortal } from "~/components/ui/confirmation-portal";
import { api } from "~/trpc/react";
import { toast } from "sonner"
import { 
 ChevronLeft, 
 Shield,
 AlertTriangle,
 Heart,
 Zap,
 FileWarning,
 Baby,
 Plus,
 X,
 Info
} from "lucide-react";

export default function SafetyAssessmentPage() {
 const router = useRouter();
 const store = useTrialDesignStore();
 
 const [errors, setErrors] = useState<Record<string, boolean>>({});
 const [formData, setFormData] = useState({
 // Safety collection framework
 willCollectAESAE: store.discovery.safety?.willCollectAESAE ?? null,
 
 // Side effects categorization (critical questions)
 likelySideEffects: store.discovery.safety?.likelySideEffects || [],
 lessLikelySideEffects: store.discovery.safety?.lessLikelySideEffects || [],
 rareButSeriousSideEffects: store.discovery.safety?.rareButSeriousSideEffects || [],
 
 // Reproductive risks (critical questions)
 hasReproductiveRisks: store.discovery.safety?.hasReproductiveRisks ?? null,
 reproductiveRiskDetails: store.discovery.safety?.reproductiveRiskDetails || "",
 });

 const [newLikelySideEffect, setNewLikelySideEffect] = useState("");
 const [newLessLikelySideEffect, setNewLessLikelySideEffect] = useState("");
 const [newRareSeriousSideEffect, setNewRareSeriousSideEffect] = useState("");
 const [activeInsightsPanel, setActiveInsightsPanel] = useState<string | null>(null);
 const [insightsData, setInsightsData] = useState<Record<string, { sections: any[]; sources?: any[]; progressStatus?: string; progressMessages?: string[] }>>({});
 const [documentViewerUrl, setDocumentViewerUrl] = useState<string | null>(null);
 const [showConfirmDialog, setShowConfirmDialog] = useState(false);
 const [emptyCategories, setEmptyCategories] = useState<string[]>([]);
 
 const cachedInsights = store.insightsCache || {};

 // Helper function to clear error when field is updated
 const handleFieldChange = (fieldName: string, value: any) => {
 setFormData(prev => ({ ...prev, [fieldName]: value }));
 if (errors[fieldName]) {
 setErrors(prev => ({ ...prev, [fieldName]: false }));
 }
 };

 const queryInsights = api.knowledgeBase.queryInsights.useMutation({
 onSuccess: (data, variables) => {
 const insightsPayload = {
 sections: data.sections || [],
 sources: data.sources || [],
 progressStatus: undefined,
 progressMessages: [],
 };
 
 setInsightsData(prev => ({
 ...prev,
 [variables.field]: insightsPayload
 }));
 
 store.cacheInsights(variables.field, insightsPayload);
 setActiveInsightsPanel(variables.field);
 },
 onError: (error) => {
 toast.error("Failed to get insights: " + error.message);
 },
 });

 const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
 onSuccess: () => {
 store.markStepCompleted("safety-assessment");
 router.push("/study/new/study-procedures-operations");
 },
 onError: (error) => {
 toast.error("Failed to save: " + error.message);
 },
 });

 const handleGetInsights = async (field: string, forceRefresh = false) => {
 if (!forceRefresh && cachedInsights[field]) {
 setActiveInsightsPanel(field);
 return;
 }
 
 setActiveInsightsPanel(field);
 setInsightsData(prev => ({
 ...prev,
 [field]: {
 sections: [],
 sources: [],
 progressStatus: 'Analyzing safety profiles...',
 progressMessages: [],
 }
 }));
 
 const progressUpdates = field === 'safety-profile' ? [
 { delay: 0, message: 'Initializing search...' },
 { delay: 1500, message: 'Searching drug safety data...' },
 { delay: 3500, message: 'Analyzing adverse event patterns...' },
 { delay: 6000, message: 'Extracting safety profiles...' },
 { delay: 8500, message: 'Generating recommendations...' },
 ] : field === 'reproductive-risks' ? [
 { delay: 0, message: 'Initializing search...' },
 { delay: 1500, message: 'Searching reproductive safety data...' },
 { delay: 3500, message: 'Analyzing pregnancy risks...' },
 { delay: 6000, message: 'Extracting reproductive toxicity...' },
 { delay: 8500, message: 'Generating recommendations...' },
 ] : [
 { delay: 0, message: 'Initializing search...' },
 { delay: 1500, message: 'Searching safety monitoring...' },
 { delay: 3500, message: 'Analyzing AE collection methods...' },
 { delay: 6000, message: 'Extracting best practices...' },
 { delay: 8500, message: 'Generating recommendations...' },
 ];
 
 progressUpdates.forEach(({ delay, message }) => {
 setTimeout(() => {
 setInsightsData(prev => {
 const current = prev[field];
 if (current && !current.sections?.length) {
 return {
 ...prev,
 [field]: {
 ...current,
 progressStatus: message,
 progressMessages: [...(current.progressMessages || []), message],
 }
 };
 }
 return prev;
 });
 }, delay);
 });
 
 const context = {
 studyType: store.discovery.studyType || undefined,
 condition: store.discovery.condition || undefined,
 phase: store.discovery.phase || undefined,
 drugName: store.discovery.intervention.name || undefined,
 drugClass: store.discovery.intervention.class || undefined,
 };

 const queries: Record<string, string> = {
 "safety-profile": `What are typical adverse events and safety profiles for ${store.discovery.intervention.class || "drugs"} targeting ${store.discovery.condition || "this condition"}?`,
 "reproductive-risks": `What are reproductive safety considerations and pregnancy risks for ${store.discovery.intervention.class || "drugs"} treating ${store.discovery.condition || "this condition"}?`,
 "ae-collection": `What are best practices for adverse event collection and safety monitoring in ${store.discovery.phase || "Phase 2/3"} trials?`,
 };

 await queryInsights.mutateAsync({
 sessionId: store.sessionId!,
 field,
 context,
 query: queries[field] || "",
 });
 };

 // Array management functions
 const addLikelySideEffect = () => {
 if (newLikelySideEffect.trim()) {
 setFormData(prev => ({
 ...prev,
 likelySideEffects: [...prev.likelySideEffects, newLikelySideEffect.trim()]
 }));
 setNewLikelySideEffect("");
      // Clear error when adding an item
      if (errors.likelySideEffects) {
        setErrors(prev => ({ ...prev, likelySideEffects: false }));
      } }
 };

 const removeLikelySideEffect = (index: number) => {
 setFormData(prev => ({
 ...prev,
 likelySideEffects: prev.likelySideEffects.filter((_: string, i: number) => i !== index)
 }));
 };

 const addLessLikelySideEffect = () => {
 if (newLessLikelySideEffect.trim()) {
 setFormData(prev => ({
 ...prev,
 lessLikelySideEffects: [...prev.lessLikelySideEffects, newLessLikelySideEffect.trim()]
 }));
 setNewLessLikelySideEffect("");
 }
      // Clear error when adding an item
      if (errors.lessLikelySideEffects) {
        setErrors(prev => ({ ...prev, lessLikelySideEffects: false }));
      } };

 const removeLessLikelySideEffect = (index: number) => {
 setFormData(prev => ({
 ...prev,
 lessLikelySideEffects: prev.lessLikelySideEffects.filter((_: string, i: number) => i !== index)
 }));
 };

 const addRareSeriousSideEffect = () => {
 if (newRareSeriousSideEffect.trim()) {
 setFormData(prev => ({
 ...prev,
 rareButSeriousSideEffects: [...prev.rareButSeriousSideEffects, newRareSeriousSideEffect.trim()]
 }));
 setNewRareSeriousSideEffect("");
 }
      // Clear error when adding an item
      if (errors.rareButSeriousSideEffects) {
        setErrors(prev => ({ ...prev, rareButSeriousSideEffects: false }));
      } };

 const removeRareSeriousSideEffect = (index: number) => {
 setFormData(prev => ({
 ...prev,
 rareButSeriousSideEffects: prev.rareButSeriousSideEffects.filter((_: string, i: number) => i !== index)
 }));
 };

 const getTotalSideEffects = () => {
 // Don't count "None" entries in the total
 const likelyCount = formData.likelySideEffects.filter((e: string) => e !== "None").length;
 const lessLikelyCount = formData.lessLikelySideEffects.filter((e: string) => e !== "None").length;
 const rareCount = formData.rareButSeriousSideEffects.filter((e: string) => e !== "None").length;
 
 return likelyCount + lessLikelyCount + rareCount;
 };

 const handleConfirmAddNone = () => {
 // Add "None" to empty categories
 const updates: any = {};
 if (emptyCategories.includes('likely')) {
 updates.likelySideEffects = ['None'];
 }
 if (emptyCategories.includes('lessLikely')) {
 updates.lessLikelySideEffects = ['None'];
 }
 if (emptyCategories.includes('rare')) {
 updates.rareButSeriousSideEffects = ['None'];
 }
 
 // Create the complete updated formData
 const updatedFormData = { ...formData, ...updates };
 
 // Update the local state
 setFormData(updatedFormData);
 setShowConfirmDialog(false);
 setEmptyCategories([]);
 
 // Save directly with the updated data
 if (store.sessionId) {
 store.updateDiscovery({ 
 safety: {
 willCollectAESAE: updatedFormData.willCollectAESAE,
 likelySideEffects: updatedFormData.likelySideEffects,
 lessLikelySideEffects: updatedFormData.lessLikelySideEffects,
 rareButSeriousSideEffects: updatedFormData.rareButSeriousSideEffects,
 hasReproductiveRisks: updatedFormData.hasReproductiveRisks,
 reproductiveRiskDetails: updatedFormData.reproductiveRiskDetails,
 }
 });

 saveDiscovery.mutate({
 sessionId: store.sessionId,
 data: {
 safety: {
 willCollectAESAE: updatedFormData.willCollectAESAE,
 likelySideEffects: updatedFormData.likelySideEffects,
 lessLikelySideEffects: updatedFormData.lessLikelySideEffects,
 rareButSeriousSideEffects: updatedFormData.rareButSeriousSideEffects,
 hasReproductiveRisks: updatedFormData.hasReproductiveRisks,
 reproductiveRiskDetails: updatedFormData.reproductiveRiskDetails,
 }
 },
 });
 }
 };

 const handleCancelAddNone = () => {
 setShowConfirmDialog(false);
 // Highlight the empty categories as errors
 const newErrors: Record<string, boolean> = {};
 let firstErrorField: string | null = null;
 
 if (emptyCategories.includes('likely')) {
 newErrors.likelySideEffects = true;
 if (!firstErrorField) firstErrorField = "likelySideEffects";
 }
 if (emptyCategories.includes('lessLikely')) {
 newErrors.lessLikelySideEffects = true;
 if (!firstErrorField) firstErrorField = "lessLikelySideEffects";
 }
 if (emptyCategories.includes('rare')) {
 newErrors.rareButSeriousSideEffects = true;
 if (!firstErrorField) firstErrorField = "rareButSeriousSideEffects";
 }
 
 setErrors(prev => ({ ...prev, ...newErrors }));
 toast.error("Please add side effects to the empty categories");
 
 // Scroll to first error field
 if (firstErrorField) {
 const element = document.getElementById(firstErrorField);
 if (element) {
 element.scrollIntoView({ behavior: "smooth", block: "center" });
 setTimeout(() => {
 element.focus();
 }, 500);
 }
 }
 setEmptyCategories([]);
 };

 const handleContinue = (skipValidation: boolean = false) => {
 if (!store.sessionId) {
 toast.error("Session not found");
 return;
 }

 // Skip validation if we just added "None" entries
 if (skipValidation === true) {
 // Update store with comprehensive safety data
 store.updateDiscovery({ 
 safety: {
 willCollectAESAE: formData.willCollectAESAE,
 likelySideEffects: formData.likelySideEffects,
 lessLikelySideEffects: formData.lessLikelySideEffects,
 rareButSeriousSideEffects: formData.rareButSeriousSideEffects,
 hasReproductiveRisks: formData.hasReproductiveRisks,
 reproductiveRiskDetails: formData.reproductiveRiskDetails,
 }
 });

 saveDiscovery.mutate({
 sessionId: store.sessionId,
 data: {
 safety: {
 willCollectAESAE: formData.willCollectAESAE,
 likelySideEffects: formData.likelySideEffects,
 lessLikelySideEffects: formData.lessLikelySideEffects,
 rareButSeriousSideEffects: formData.rareButSeriousSideEffects,
 hasReproductiveRisks: formData.hasReproductiveRisks,
 reproductiveRiskDetails: formData.reproductiveRiskDetails,
 }
 },
 });
 return;
 }

 // Comprehensive validation
 const newErrors: Record<string, boolean> = {};
 let hasError = false;
 let firstErrorField: string | null = null;

    // Validation - require safety collection decision
    if (formData.willCollectAESAE === null) {
      newErrors.willCollectAESAE = true;
      hasError = true;
      if (!firstErrorField) firstErrorField = "willCollectAESAE";
    }

    // Validation - require reproductive risks decision
    if (formData.hasReproductiveRisks === null) {
      newErrors.hasReproductiveRisks = true;
      hasError = true;
      if (!firstErrorField) firstErrorField = "hasReproductiveRisks";
    }

    // If has reproductive risks is true, require details
    if (formData.hasReproductiveRisks === true && !formData.reproductiveRiskDetails.trim()) {
      newErrors.reproductiveRiskDetails = true;
      hasError = true;
      if (!firstErrorField) firstErrorField = "reproductiveRiskDetails";
    }
    
    // Check if we have validation errors so far
    if (hasError) {
      setErrors(newErrors);
      toast.error("Please complete all required fields");
      
      // Scroll to first error field
      if (firstErrorField) {
        const element = document.getElementById(firstErrorField);
        if (element) {
          element.scrollIntoView({ behavior: "smooth", block: "center" });
          setTimeout(() => {
            element.focus();
          }, 500);
        }
      }
      return;
    }

    // If collecting AEs/SAEs, check for empty side effect categories
    // This only happens if no validation errors exist
    if (formData.willCollectAESAE === true) {
      const emptyCats = [];
      
      if (formData.likelySideEffects.length === 0) {
        emptyCats.push('likely');
      }
      if (formData.lessLikelySideEffects.length === 0) {
        emptyCats.push('lessLikely');
      }
      if (formData.rareButSeriousSideEffects.length === 0) {
        emptyCats.push('rare');
      }
      
      // If there are empty categories, show custom confirmation dialog
      if (emptyCats.length > 0) {
        setEmptyCategories(emptyCats);
        setShowConfirmDialog(true);
        return; // Exit here and wait for dialog response
      }
    }
 // All validation passed, update store with comprehensive safety data
 store.updateDiscovery({ 
 safety: {
 willCollectAESAE: formData.willCollectAESAE,
 likelySideEffects: formData.likelySideEffects,
 lessLikelySideEffects: formData.lessLikelySideEffects,
 rareButSeriousSideEffects: formData.rareButSeriousSideEffects,
 hasReproductiveRisks: formData.hasReproductiveRisks,
 reproductiveRiskDetails: formData.reproductiveRiskDetails,
 }
 });

 saveDiscovery.mutate({
 sessionId: store.sessionId,
 data: {
 safety: {
 willCollectAESAE: formData.willCollectAESAE,
 likelySideEffects: formData.likelySideEffects,
 lessLikelySideEffects: formData.lessLikelySideEffects,
 rareButSeriousSideEffects: formData.rareButSeriousSideEffects,
 hasReproductiveRisks: formData.hasReproductiveRisks,
 reproductiveRiskDetails: formData.reproductiveRiskDetails,
 }
 },
 });
 };

 const handleApplySuggestion = (field: string, suggestion: string, actionableData?: any) => {
 if (field === "safety-profile") {
 if (actionableData) {
 const updates: any = {};
 let clearedErrors: any = {};
 
 // Handle bulk additions (Apply All)
 if (actionableData.field === 'likelySideEffects' && (actionableData.effects || actionableData.value)) {
 const effects = actionableData.effects || actionableData.value;
 updates.likelySideEffects = [...new Set([...formData.likelySideEffects, ...(Array.isArray(effects) ? effects : [effects])])];
 clearedErrors.likelySideEffects = false;
 toast.success("Likely side effects added");
 }
 if (actionableData.field === 'lessLikelySideEffects' && (actionableData.effects || actionableData.value)) {
 const effects = actionableData.effects || actionableData.value;
 updates.lessLikelySideEffects = [...new Set([...formData.lessLikelySideEffects, ...(Array.isArray(effects) ? effects : [effects])])];
 clearedErrors.lessLikelySideEffects = false;
 toast.success("Less likely side effects added");
 }
 if ((actionableData.field === 'rareButSeriousSideEffects' || actionableData.field === 'rareSeriousSideEffects') && (actionableData.effects || actionableData.value)) {
 const effects = actionableData.effects || actionableData.value;
 updates.rareButSeriousSideEffects = [...new Set([...formData.rareButSeriousSideEffects, ...(Array.isArray(effects) ? effects : [effects])])];
 clearedErrors.rareButSeriousSideEffects = false;
 toast.success("Rare serious side effects added");
 }
 
 // Handle individual side effect additions
 if (actionableData.field === 'singleLikelySideEffect' && actionableData.value) {
 // Check if effect already exists to avoid duplicates
 if (!formData.likelySideEffects.includes(actionableData.value)) {
 updates.likelySideEffects = [...formData.likelySideEffects, actionableData.value];
 clearedErrors.likelySideEffects = false;
 const category = actionableData.category || 'common';
 toast.success(`Added ${category} side effect: ${actionableData.value}`);
 } else {
 toast.info("This side effect is already in your likely effects list");
 }
 }
 if (actionableData.field === 'singleLessLikelySideEffect' && actionableData.value) {
 // Check if effect already exists to avoid duplicates
 if (!formData.lessLikelySideEffects.includes(actionableData.value)) {
 updates.lessLikelySideEffects = [...formData.lessLikelySideEffects, actionableData.value];
 clearedErrors.lessLikelySideEffects = false;
 const category = actionableData.category || 'occasional';
 toast.success(`Added ${category} side effect: ${actionableData.value}`);
 } else {
 toast.info("This side effect is already in your less likely effects list");
 }
 }
 if (actionableData.field === 'singleRareSeriousSideEffect' && actionableData.value) {
 // Check if effect already exists to avoid duplicates
 if (!formData.rareButSeriousSideEffects.includes(actionableData.value)) {
 updates.rareButSeriousSideEffects = [...formData.rareButSeriousSideEffects, actionableData.value];
 clearedErrors.rareButSeriousSideEffects = false;
 const category = actionableData.category || 'serious';
 toast.success(`Added ${category} side effect: ${actionableData.value}`);
 } else {
 toast.info("This side effect is already in your rare but serious effects list");
 }
 }
 
 if (Object.keys(updates).length > 0) {
 setFormData(prev => ({ ...prev, ...updates }));
 // Clear any errors for fields that were updated
 if (Object.keys(clearedErrors).length > 0) {
 setErrors(prev => ({ ...prev, ...clearedErrors }));
 }
 }
 }
 return;
 }
 
 if (field === "reproductive-risks") {
 if (actionableData) {
 const updates: any = {};
 if (actionableData.field === 'hasReproductiveRisks') {
 // Convert 'yes'/'no' string to boolean
 const value = actionableData.value === 'yes' ? true : actionableData.value === 'no' ? false : actionableData.value;
 updates.hasReproductiveRisks = value;
 toast.success("Reproductive risk status updated");
 }
 if (actionableData.field === 'reproductiveRisksDetails') {
 // Intelligently merge the text content
 const newContent = actionableData.value;
 const existingContent = formData.reproductiveRiskDetails || '';
 
 // Check if this is a risk profile (contains specific markers)
 const isRiskProfile = newContent.includes('ANIMAL DATA:') || 
                       newContent.includes('HUMAN DATA:') || 
                       newContent.includes('TERATOGENICITY RISK:') ||
                       newContent.includes('FERTILITY EFFECTS:');
 
 // Check if this is mitigation strategy (contains specific markers)
 const isMitigation = newContent.includes('CONTRACEPTION REQUIREMENTS:') || 
                      newContent.includes('PREGNANCY TESTING:') ||
                      newContent.includes('COUNSELING:');
 
 let finalContent = newContent;
 
 if (isRiskProfile && isMitigation) {
 // New content has both - just replace everything
 finalContent = newContent;
 } else if (isRiskProfile) {
 // New content is risk profile
 // Check if existing content has mitigation strategies
 const existingHasMitigation = existingContent.includes('CONTRACEPTION REQUIREMENTS:') || 
                                existingContent.includes('PREGNANCY TESTING:');
 if (existingHasMitigation) {
 // Extract mitigation part from existing content
 const mitigationStart = Math.min(
 existingContent.indexOf('CONTRACEPTION REQUIREMENTS:') !== -1 ? existingContent.indexOf('CONTRACEPTION REQUIREMENTS:') : Infinity,
 existingContent.indexOf('PREGNANCY TESTING:') !== -1 ? existingContent.indexOf('PREGNANCY TESTING:') : Infinity,
 existingContent.indexOf('COUNSELING:') !== -1 ? existingContent.indexOf('COUNSELING:') : Infinity
 );
 if (mitigationStart !== Infinity) {
 const mitigationContent = existingContent.substring(mitigationStart);
 // Risk profile first, then mitigation
 finalContent = newContent + '\n\n' + mitigationContent;
 }
 }
 } else if (isMitigation) {
 // New content is mitigation strategy
 // Check if existing content has risk profile
 const existingHasRiskProfile = existingContent.includes('ANIMAL DATA:') || 
                                 existingContent.includes('HUMAN DATA:') || 
                                 existingContent.includes('TERATOGENICITY RISK:');
 if (existingHasRiskProfile) {
 // Find where mitigation starts in existing content (if any)
 const existingMitigationStart = Math.min(
 existingContent.indexOf('CONTRACEPTION REQUIREMENTS:') !== -1 ? existingContent.indexOf('CONTRACEPTION REQUIREMENTS:') : Infinity,
 existingContent.indexOf('PREGNANCY TESTING:') !== -1 ? existingContent.indexOf('PREGNANCY TESTING:') : Infinity,
 existingContent.indexOf('COUNSELING:') !== -1 ? existingContent.indexOf('COUNSELING:') : Infinity
 );
 
 if (existingMitigationStart !== Infinity) {
 // Replace the mitigation part
 const riskProfileContent = existingContent.substring(0, existingMitigationStart).trim();
 finalContent = riskProfileContent + '\n\n' + newContent;
 } else {
 // No existing mitigation, just append
 finalContent = existingContent + '\n\n' + newContent;
 }
 } else {
 // No risk profile exists, just set the mitigation
 finalContent = newContent;
 }
 }
 
 updates.reproductiveRiskDetails = finalContent;
 toast.success("Reproductive risk details updated");
 }
 if (Object.keys(updates).length > 0) {
 setFormData(prev => ({ ...prev, ...updates }));
 }
 }
 return;
 }
 
 if (field === "ae-collection") {
 if (actionableData && actionableData.field === 'willCollectAESAE') {
 // Convert 'yes'/'no' string to boolean
 const value = actionableData.value === 'yes' ? true : actionableData.value === 'no' ? false : actionableData.value;
 setFormData(prev => ({ ...prev, willCollectAESAE: value }));
 toast.success("AE/SAE collection plan updated");
 }
 return;
 }
 };

 return (
 <div className="space-y-8">
 <BlurFade delay={0.01} inView>
 <div>
 <h1 className="text-3xl font-bold text-gray-900">
 Safety Assessment
 </h1>
 <p className="mt-2 text-lg text-gray-600">
 Define safety monitoring framework and expected risk profile
 </p>
 </div>
 </BlurFade>

 {/* AE/SAE Collection Framework */}
 <BlurFade delay={0.02} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <Shield className="h-5 w-5" />
 Adverse Event Collection Framework
 </CardTitle>
 <CardDescription>
 Define how safety data will be collected and monitored
 </CardDescription>
 </div>
 <InsightsButton
 onClick={() => handleGetInsights("ae-collection")}
 onRefresh={() => handleGetInsights("ae-collection", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "ae-collection"}
 hasCachedData={!!cachedInsights["ae-collection"]}
 showRefresh={!!cachedInsights["ae-collection"]}
 />
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="space-y-2">
 <Label htmlFor="willCollectAESAE">Will Adverse Events (AEs) & Serious Adverse Events (SAEs) be collected? *</Label>
 <Select 
 value={formData.willCollectAESAE === null ? "" : formData.willCollectAESAE ? "yes" : "no"}
 onValueChange={(value) => handleFieldChange("willCollectAESAE", value === "yes")}
 >
 <SelectTrigger id="willCollectAESAE" className={errors.willCollectAESAE ? "border-red-500 focus:ring-red-500" : ""}>
 <SelectValue placeholder="Select yes or no" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="yes">Yes</SelectItem>
 <SelectItem value="no">No</SelectItem>
 </SelectContent>
 </Select>
 {errors.willCollectAESAE && (
 <p className="text-sm text-red-500">Please select whether AEs/SAEs will be collected</p>
 )}
 </div> 
 {formData.willCollectAESAE && (
 <div className="rounded-lg bg-blue-50 p-4">
 <div className="flex items-center gap-2 mb-2">
 <Info className="h-4 w-4 text-blue-600" />
 <h4 className="font-medium text-blue-900 ">Safety Monitoring Active</h4>
 </div>
 <p className="text-sm text-blue-800 ">
 This study will include comprehensive adverse event collection. Please specify the expected safety profile below.
 </p>
 </div>
 )}
 
 {!formData.willCollectAESAE && (
 <div className="rounded-lg bg-amber-50 p-4">
 <div className="flex items-center gap-2 mb-2">
 <AlertTriangle className="h-4 w-4 text-amber-600" />
 <h4 className="font-medium text-amber-900 ">Limited Safety Monitoring</h4>
 </div>
 <p className="text-sm text-amber-800 ">
 Consider whether this is appropriate for your study type and regulatory requirements.
 </p>
 </div>
 )}
 </CardContent>
 </Card>
 </BlurFade>

 {/* Expected Safety Profile */}
 {formData.willCollectAESAE && (
 <>
 {/* Likely Side Effects */}
 <BlurFade delay={0.03} inView>
 <Card className={errors.likelySideEffects ? "border-red-500" : ""}>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <Zap className="h-5 w-5 text-orange-500" />
 Likely Side Effects {formData.willCollectAESAE && "*"}
 </CardTitle>
 <CardDescription>
 List side effects that are likely to occur (common, ≥10% incidence)
 </CardDescription>
 </div>
 <InsightsButton
 onClick={() => handleGetInsights("safety-profile")}
 onRefresh={() => handleGetInsights("safety-profile", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "safety-profile"}
 hasCachedData={!!cachedInsights["safety-profile"]}
 showRefresh={!!cachedInsights["safety-profile"]}
 />
 </div>
 </CardHeader>
 <CardContent className="space-y-4" id="likelySideEffects">
 {formData.likelySideEffects.map((effect, index) => (
 <div key={index} className="flex items-center gap-2 rounded-lg border border-orange-200 bg-orange-50 p-3">
 <Zap className="h-4 w-4 text-orange-500" />
 <span className="flex-1 text-sm">{effect}</span>
 <Button
 size="icon"
 variant="ghost"
 onClick={() => removeLikelySideEffect(index)}
 >
 <X className="h-4 w-4" />
 </Button>
 </div>
 ))}
 
 <div className="flex gap-2">
 <Input
 placeholder="Add likely side effect (e.g., nausea, headache, fatigue)..."
 value={newLikelySideEffect}
 onChange={(e) => setNewLikelySideEffect(e.target.value)}
 onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addLikelySideEffect())}
 />
 <Button onClick={addLikelySideEffect} size="icon">
 <Plus className="h-4 w-4" />
 </Button>
 </div>
 
              {errors.likelySideEffects && (
                <p className="text-sm text-red-500">Please add at least one side effect when collecting AEs/SAEs</p>
              )} <p className="text-xs text-muted-foreground">
 {formData.likelySideEffects.length} likely side effects defined
 </p>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Less Likely Side Effects */}
 <BlurFade delay={0.04} inView>
 <Card className={errors.lessLikelySideEffects ? "border-red-500" : ""}>
 <CardHeader>
 <CardTitle className="flex items-center gap-2">
 <AlertTriangle className="h-5 w-5 text-yellow-500" />
 Less Likely Side Effects
 </CardTitle>
 <CardDescription>
 List side effects that are less likely to occur (uncommon, 1-10% incidence)
 </CardDescription>
 </CardHeader>
 <CardContent className="space-y-4" id="lessLikelySideEffects">
 {formData.lessLikelySideEffects.map((effect, index) => (
 <div key={index} className="flex items-center gap-2 rounded-lg border border-yellow-200 bg-yellow-50 p-3">
 <AlertTriangle className="h-4 w-4 text-yellow-500" />
 <span className="flex-1 text-sm">{effect}</span>
 <Button
 size="icon"
 variant="ghost"
 onClick={() => removeLessLikelySideEffect(index)}
 >
 <X className="h-4 w-4" />
 </Button>
 </div>
 ))}
 
 <div className="flex gap-2">
 <Input
 placeholder="Add less likely side effect (e.g., dizziness, constipation)..."
 value={newLessLikelySideEffect}
 onChange={(e) => setNewLessLikelySideEffect(e.target.value)}
 onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addLessLikelySideEffect())}
 />
 <Button onClick={addLessLikelySideEffect} size="icon">
 <Plus className="h-4 w-4" />
 </Button>
 </div>
 
 {errors.lessLikelySideEffects && (
 <p className="text-sm text-red-500">Please add at least one side effect when collecting AEs/SAEs</p>
 )}
 
 <p className="text-xs text-muted-foreground">
 {formData.lessLikelySideEffects.length} less likely side effects defined
 </p>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Rare but Serious Side Effects */}
 <BlurFade delay={0.05} inView>
 <Card className={errors.rareButSeriousSideEffects ? "border-red-500" : ""}>
 <CardHeader>
 <CardTitle className="flex items-center gap-2">
 <FileWarning className="h-5 w-5 text-red-500" />
 Rare but Serious Side Effects
 </CardTitle>
 <CardDescription>
 List rare but serious side effects that require special monitoring (&lt;1% but serious)
 </CardDescription>
 </CardHeader>
 <CardContent className="space-y-4" id="rareButSeriousSideEffects">
 {formData.rareButSeriousSideEffects.map((effect, index) => (
 <div key={index} className="flex items-center gap-2 rounded-lg border border-red-200 bg-red-50 p-3">
 <FileWarning className="h-4 w-4 text-red-500" />
 <span className="flex-1 text-sm">{effect}</span>
 <Button
 size="icon"
 variant="ghost"
 onClick={() => removeRareSeriousSideEffect(index)}
 >
 <X className="h-4 w-4" />
 </Button>
 </div>
 ))}
 
 <div className="flex gap-2">
 <Input
 placeholder="Add rare serious side effect (e.g., liver toxicity, anaphylaxis)..."
 value={newRareSeriousSideEffect}
 onChange={(e) => setNewRareSeriousSideEffect(e.target.value)}
 onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addRareSeriousSideEffect())}
 />
 <Button onClick={addRareSeriousSideEffect} size="icon">
 <Plus className="h-4 w-4" />
 </Button>
 </div>
 
 {errors.rareButSeriousSideEffects && (
 <p className="text-sm text-red-500">Please add at least one side effect when collecting AEs/SAEs</p>
 )}
 
 <p className="text-xs text-muted-foreground">
 {formData.rareButSeriousSideEffects.length} rare serious side effects defined
 </p>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Safety Summary */}
 <BlurFade delay={0.06} inView>
 <Card className="bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200">
 <CardContent className="p-6">
 <div className="flex items-center gap-3 mb-4">
 <Shield className="h-5 w-5 text-gray-600" />
 <h3 className="font-medium text-gray-900">Safety Profile Summary</h3>
 </div>
 <div className="grid grid-cols-3 gap-4 text-sm">
 <div className="text-center">
 <p className="text-2xl font-bold text-orange-600">{formData.likelySideEffects.filter(e => e !== "None").length}</p>
 <p className="text-gray-600">Likely Effects</p>
 </div>
 <div className="text-center">
 <p className="text-2xl font-bold text-yellow-600">{formData.lessLikelySideEffects.filter(e => e !== "None").length}</p>
 <p className="text-gray-600">Less Likely</p>
 </div>
 <div className="text-center">
 <p className="text-2xl font-bold text-red-600">{formData.rareButSeriousSideEffects.filter(e => e !== "None").length}</p>
 <p className="text-gray-600">Rare Serious</p>
 </div>
 </div>
 <div className="mt-4 text-center">
 <p className="text-sm text-gray-600">
 Total: <strong>{getTotalSideEffects()}</strong> defined adverse events across all categories
 </p>
 </div>
 </CardContent>
 </Card>
 </BlurFade>
 </>
 )}

 {/* Reproductive Risks */}
 <BlurFade delay={0.07} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <Baby className="h-5 w-5" />
 Reproductive Risks Assessment
 </CardTitle>
 <CardDescription>
 Assess potential reproductive and pregnancy-related risks
 </CardDescription>
 </div>
 <InsightsButton
 onClick={() => handleGetInsights("reproductive-risks")}
 onRefresh={() => handleGetInsights("reproductive-risks", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "reproductive-risks"}
 hasCachedData={!!cachedInsights["reproductive-risks"]}
 showRefresh={!!cachedInsights["reproductive-risks"]}
 />
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="space-y-2">
 <Label htmlFor="hasReproductiveRisks">Are there any reproductive risks associated with this study? *</Label>
 <Select 
 value={formData.hasReproductiveRisks === null ? "" : formData.hasReproductiveRisks ? "yes" : "no"}
 onValueChange={(value) => handleFieldChange("hasReproductiveRisks", value === "yes")}
 >
 <SelectTrigger id="hasReproductiveRisks" className={errors.hasReproductiveRisks ? "border-red-500 focus:ring-red-500" : ""}>
 <SelectValue placeholder="Select yes or no" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="yes">Yes</SelectItem>
 <SelectItem value="no">No</SelectItem>
 </SelectContent>
 </Select>
 {errors.hasReproductiveRisks && (
 <p className="text-sm text-red-500">Please select whether there are reproductive risks</p>
 )}
 </div>
 {formData.hasReproductiveRisks && (
 <div className="space-y-4">
 <div className="rounded-lg bg-amber-50 p-4">
 <div className="flex items-center gap-2 mb-2">
 <AlertTriangle className="h-4 w-4 text-amber-600" />
 <h4 className="font-medium text-amber-900 ">Reproductive Risk Assessment Required</h4>
 </div>
 <p className="text-sm text-amber-800 ">
 Please provide detailed scientific information about reproductive risks and safety measures.
 </p>
 </div>

 <div className="space-y-2">
 <Label htmlFor="reproductiveRiskDetails">Scientifically Detail the Reproductive Risks *</Label>
 <Textarea
 id="reproductiveRiskDetails"
 placeholder="Provide detailed scientific information about reproductive risks, including animal reproductive toxicity studies, mechanism of action affecting reproduction, contraceptive requirements, pregnancy testing requirements, etc..."
 rows={6}
 value={formData.reproductiveRiskDetails}
 onChange={(e) => handleFieldChange("reproductiveRiskDetails", e.target.value)}
 className={errors.reproductiveRiskDetails ? "border-red-500 focus:ring-red-500" : ""}
 />
 {errors.reproductiveRiskDetails && (
 <p className="text-sm text-red-500">Please provide details about the reproductive risks</p>
 )}
 <p className="text-xs text-muted-foreground">
 Include information about: animal reproductive toxicity studies, teratogenic potential, fertility effects, 
 contraceptive requirements, pregnancy testing protocols, breastfeeding considerations, and any special 
 monitoring requirements for reproductive-aged participants.
 </p>
 </div>
 </div>
 )}

 {!formData.hasReproductiveRisks && (
 <div className="rounded-lg bg-green-50 p-4">
 <div className="flex items-center gap-2 mb-2">
 <Heart className="h-4 w-4 text-green-600" />
 <h4 className="font-medium text-green-900 ">No Reproductive Risks Identified</h4>
 </div>
 <p className="text-sm text-green-800 ">
 This study is assessed as having no significant reproductive risks. Standard safety monitoring will apply.
 </p>
 </div>
 )}
 </CardContent>
 </Card>
 </BlurFade>

 {/* Navigation */}
 <BlurFade delay={0.08} inView>
 <div className="flex justify-between pt-6">
 <Button
 variant="outline"
 onClick={() => router.push("/study/new/study-population")}
 >
 <ChevronLeft className="mr-2 h-4 w-4" />
 Back to Study Population
 </Button>
 <Button
 onClick={() => handleContinue()}
 disabled={saveDiscovery.isPending}
 >
 {saveDiscovery.isPending ? "Saving..." : "Continue to Study Procedures & Operations"}
 </Button>
 </div>
 </BlurFade>

 {/* Insights Panel */}
 {activeInsightsPanel && (
 <InsightsPanelPortal
 isOpen={true}
 onClose={() => setActiveInsightsPanel(null)}
 title={`Insights: ${activeInsightsPanel.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase())}`}
 description="Recommendations based on similar studies"
 loading={queryInsights.isPending}
 sections={insightsData[activeInsightsPanel]?.sections || cachedInsights[activeInsightsPanel]?.sections || []}
 sources={insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || []}
 progressStatus={insightsData[activeInsightsPanel]?.progressStatus}
 progressMessages={insightsData[activeInsightsPanel]?.progressMessages}
 onDocumentClick={(url) => setDocumentViewerUrl(url)}
 onApplySuggestion={(suggestion, actionableData) => handleApplySuggestion(activeInsightsPanel, suggestion, actionableData)}
 />
 )}

 {/* Document Viewer */}
 {documentViewerUrl && (
 <DocumentViewerPortal
 citations={(() => {
 // Extract all unique citations from all sections
 const allCitations = new Map<string, any>();
 Object.values(insightsData).forEach(data => {
 data.sections?.forEach((section: any) => {
 if (section.citations && Array.isArray(section.citations)) {
 section.citations.forEach((citation: any) => {
 if (citation.id && !allCitations.has(citation.id)) {
 allCitations.set(citation.id, citation);
 }
 });
 }
 });
 });
 return Array.from(allCitations.values());
 })()}
 sources={(() => {
 // Extract all unique sources from all insights data
 const allSources = new Map<string, any>();
 Object.values(insightsData).forEach(data => {
 data.sources?.forEach((source: any) => {
 if (source.nctId && !allSources.has(source.nctId)) {
 allSources.set(source.nctId, source);
 }
 });
 });
 return Array.from(allSources.values());
 })()}
 onStudySelect={(studyUrl) => setDocumentViewerUrl(studyUrl)}
 currentStudyId={(() => {
 // Extract NCT ID from the current document URL
 if (!documentViewerUrl) return undefined;
 const match = documentViewerUrl.match(/NCT\\d+/i);
 return match ? match[0] : undefined;
 })()}
 isOpen={true}
 onClose={() => setDocumentViewerUrl(null)}
 documentUrl={documentViewerUrl}
 loading={false}
 />
 )}

 {/* Confirmation Dialog for None entries */}
 <ConfirmationPortal
 isOpen={showConfirmDialog}
 onClose={handleCancelAddNone}
 onConfirm={handleConfirmAddNone}
 title="Empty Side Effect Categories"
 message="Would you like to add 'None' to these categories to indicate no effects are expected?"
 details={emptyCategories.map(cat => {
 const categoryNames = {
 likely: 'Likely Side Effects',
 lessLikely: 'Less Likely Side Effects',
 rare: 'Rare but Serious Side Effects'
 };
 return categoryNames[cat as keyof typeof categoryNames];
 })}
 confirmText="Yes"
 cancelText="No"
 variant="warning"
 />
 </div>
 );
}