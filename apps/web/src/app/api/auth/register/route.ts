import { NextRequest, NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import { z } from "zod";
import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocument } from "@aws-sdk/lib-dynamodb";
import { env } from "~/env";

// DynamoDB client configuration
const dynamoDBClient = DynamoDBDocument.from(
  new DynamoDBClient({
    region: env.AUTH_DYNAMODB_REGION || env.AWS_REGION || "us-west-2",
    ...(env.AUTH_DYNAMODB_ID && env.AUTH_DYNAMODB_SECRET && {
      credentials: {
        accessKeyId: env.AUTH_DYNAMODB_ID,
        secretAccessKey: env.AUTH_DYNAMODB_SECRET,
      },
    }),
  })
);

const registerSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  name: z.string().min(1, "Name is required"),
});

async function isAllowed(email: string): Promise<boolean> {
  try {
    const result = await dynamoDBClient.get({
      TableName: env.ALLOWLIST_TABLE_NAME || "trialynx-allowlist",
      Key: { pk: `EMAIL#${email.toLowerCase()}` },
    });
    return result.Item?.status === "active";
  } catch (error: unknown) {
    console.error("Error checking allowlist:", error);
    return false;
  }
}

async function addToWaitlist(email: string): Promise<void> {
  try {
    await dynamoDBClient.put({
      TableName: env.WAITLIST_TABLE_NAME || "trialynx-waitlist",
      Item: {
        pk: `EMAIL#${email.toLowerCase()}`,
        email: email.toLowerCase(),
        createdAt: new Date().toISOString(),
      },
      ConditionExpression: "attribute_not_exists(pk)",
    });
  } catch (error: unknown) {
    if ((error as any)?.name !== "ConditionalCheckFailedException") {
      console.error("Error adding to waitlist:", error);
    }
  }
}

async function isDomainAllowed(email: string): Promise<boolean> {
  const allowedDomains = ["trialynx.io", "trialynx.com"];
  const domain = email.split("@")[1]?.toLowerCase();
  return allowedDomains.includes(domain || "");
}

async function userExists(email: string): Promise<boolean> {
  try {
    const result = await dynamoDBClient.query({
      TableName: env.AUTH_TABLE_NAME || "next-auth",
      KeyConditionExpression: "pk = :pk",
      ExpressionAttributeValues: {
        ":pk": `USER#${email.toLowerCase()}`,
      },
    });
    return Boolean(result.Items && result.Items.length > 0);
  } catch (error: unknown) {
    console.error("Error checking if user exists:", error);
    return true; // Err on the side of caution
  }
}

async function createUser(email: string, hashedPassword: string, name: string): Promise<any> {
  try {
    const userId = crypto.randomUUID();
    const now = new Date().toISOString();
    
    const userItem = {
      pk: `USER#${email.toLowerCase()}`,
      sk: `USER#${userId}`,
      id: userId,
      email: email.toLowerCase(),
      name,
      emailVerified: null, // Will be set when email is verified
      image: null,
      hashedPassword,
      provider: "credentials",
      createdAt: now,
      updatedAt: now,
    };

    await dynamoDBClient.put({
      TableName: env.AUTH_TABLE_NAME || "next-auth",
      Item: userItem,
      ConditionExpression: "attribute_not_exists(pk)", // Prevent overwriting existing user
    });

    return {
      id: userId,
      email: email.toLowerCase(),
      name,
    };
  } catch (error) {
    console.error("Error creating user:", error);
    throw error;
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password, name } = registerSchema.parse(body);

    // Check if user already exists
    if (await userExists(email)) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 400 }
      );
    }

    // Check if user is allowed to register
    const isUserAllowed = await isAllowed(email);
    const isDomainOk = await isDomainAllowed(email);
    
    if (!isUserAllowed && !isDomainOk) {
      // Add to waitlist and reject registration
      await addToWaitlist(email);
      return NextResponse.json(
        { 
          error: "Registration is invite-only. You've been added to our waitlist.",
          waitlisted: true 
        },
        { status: 403 }
      );
    }

    // If domain is allowed but not in allowlist, auto-approve
    if (!isUserAllowed && isDomainOk) {
      await dynamoDBClient.put({
        TableName: env.ALLOWLIST_TABLE_NAME || "trialynx-allowlist",
        Item: {
          pk: `EMAIL#${email.toLowerCase()}`,
          email: email.toLowerCase(),
          status: "active",
          source: "domain-rule",
          createdAt: new Date().toISOString(),
        },
      });
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create user
    const user = await createUser(email, hashedPassword, name);

    return NextResponse.json(
      { 
        message: "Registration successful", 
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
        }
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    console.error("Registration error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid input", details: error.errors },
        { status: 400 }
      );
    }
    
    if ((error as any)?.name === "ConditionalCheckFailedException") {
      return NextResponse.json(
        { error: "User already exists" },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Registration failed" },
      { status: 500 }
    );
  }
}