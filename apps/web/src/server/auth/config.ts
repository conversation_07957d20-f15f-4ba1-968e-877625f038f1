import { type DefaultSession, type NextAuthConfig } from "next-auth";
import { DynamoDBAdapter } from "@auth/dynamodb-adapter";
import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocument } from "@aws-sdk/lib-dynamodb";
import CredentialsProvider from "next-auth/providers/credentials";
// import EmailProvider from "next-auth/providers/email"; // Commented out to avoid nodemailer dependency
import GoogleProvider from "next-auth/providers/google";
import MicrosoftEntraID from "next-auth/providers/microsoft-entra-id";
import bcrypt from "bcryptjs";
import { env } from "../../env";

// DynamoDB client configuration
const dynamoDBClient = DynamoDBDocument.from(
  new DynamoDBClient({
    region: env.AUTH_DYNAMODB_REGION || env.AWS_REGION || "us-west-2",
    ...(env.AUTH_DYNAMODB_ID && env.AUTH_DYNAMODB_SECRET && {
      credentials: {
        accessKeyId: env.AUTH_DYNAMODB_ID,
        secretAccessKey: env.AUTH_DYNAMODB_SECRET,
      },
    }),
  })
);

/**
 * Module augmentation for `next-auth` types. Allows us to add custom properties to the `session`
 * object and keep type safety.
 *
 * @see https://next-auth.js.org/getting-started/typescript#module-augmentation
 */
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      role?: "researcher" | "admin";
    } & DefaultSession["user"];
  }

  interface User {
    role?: "researcher" | "admin";
  }
}

/**
 * Helper functions for invite-only access control
 */
async function isAllowed(email: string): Promise<boolean> {
  try {
    const result = await dynamoDBClient.get({
      TableName: env.ALLOWLIST_TABLE_NAME || "trialynx-allowlist",
      Key: { pk: `EMAIL#${email.toLowerCase()}` },
    });
    return result.Item?.status === "active";
  } catch (error) {
    console.error("Error checking allowlist:", error);
    return false;
  }
}

async function addToWaitlist(email: string): Promise<void> {
  try {
    await dynamoDBClient.put({
      TableName: env.WAITLIST_TABLE_NAME || "trialynx-waitlist",
      Item: {
        pk: `EMAIL#${email.toLowerCase()}`,
        email: email.toLowerCase(),
        createdAt: new Date().toISOString(),
      },
      ConditionExpression: "attribute_not_exists(pk)",
    });
  } catch (error) {
    if (error.name !== "ConditionalCheckFailedException") {
      console.error("Error adding to waitlist:", error);
    }
  }
}

async function isDomainAllowed(email: string): Promise<boolean> {
  const allowedDomains = ["trialynx.io", "trialynx.com"];
  const domain = email.split("@")[1]?.toLowerCase();
  return allowedDomains.includes(domain || "");
}

async function getUserByEmail(email: string): Promise<any | null> {
  try {
    // Note: This is a simplified approach. In production, you'd want a separate users table
    // For now, we'll check if a user exists in the NextAuth adapter's User table
    const result = await dynamoDBClient.query({
      TableName: env.AUTH_TABLE_NAME || "next-auth",
      KeyConditionExpression: "pk = :pk AND begins_with(sk, :sk)",
      ExpressionAttributeValues: {
        ":pk": `USER#${email.toLowerCase()}`,
        ":sk": "USER#",
      },
    });
    
    if (result.Items && result.Items.length > 0) {
      return result.Items[0];
    }
    return null;
  } catch (error: unknown) {
    console.error("Error getting user by email:", error);
    return null;
  }
}

async function createLocalUser(email: string, hashedPassword: string, name?: string): Promise<any> {
  try {
    const userId = crypto.randomUUID();
    const now = new Date().toISOString();
    
    const userItem = {
      pk: `USER#${email.toLowerCase()}`,
      sk: `USER#${userId}`,
      id: userId,
      email: email.toLowerCase(),
      name: name || email.split("@")[0],
      emailVerified: null, // Will be set when email is verified
      image: null,
      hashedPassword,
      provider: "credentials",
      createdAt: now,
      updatedAt: now,
    };

    await dynamoDBClient.put({
      TableName: env.AUTH_TABLE_NAME || "next-auth",
      Item: userItem,
    });

    return {
      id: userId,
      email: email.toLowerCase(),
      name: name || email.split("@")[0],
    };
  } catch (error: unknown) {
    console.error("Error creating local user:", error);
    throw error;
  }
}

/**
 * Options for NextAuth.js used to configure adapters, providers, callbacks, etc.
 *
 * @see https://next-auth.js.org/configuration/options
 */
export const authConfig = {
  adapter: DynamoDBAdapter(dynamoDBClient, {
    tableName: env.AUTH_TABLE_NAME || "next-auth",
  }),
  
  providers: [
    // Credentials provider for email/password authentication
    CredentialsProvider({
      id: "credentials",
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const user = await getUserByEmail(credentials.email);
          
          if (!user?.hashedPassword) {
            return null;
          }

          const isValid = await bcrypt.compare(credentials.password, user.hashedPassword);
          if (!isValid) {
            return null;
          }

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image || null,
          };
        } catch (error: unknown) {
          console.error("Error during authentication:", error);
          return null;
        }
      },
    }),

    // Email magic link provider disabled to avoid nodemailer dependency
    // Can be re-enabled when SES email sending is implemented
    // ...(env.EMAIL_SERVER ? [EmailProvider({
    //   server: env.EMAIL_SERVER,
    //   from: env.EMAIL_FROM || "Auth <<EMAIL>>",
    //   // ... email provider configuration
    // })] : []),
    
    // Google OAuth
    ...(env.AUTH_GOOGLE_ID && env.AUTH_GOOGLE_SECRET
      ? [
          GoogleProvider({
            clientId: env.AUTH_GOOGLE_ID,
            clientSecret: env.AUTH_GOOGLE_SECRET,
          }),
        ]
      : []),
    
    // Microsoft Entra ID (Azure AD)
    ...(env.AUTH_AZURE_AD_ID && env.AUTH_AZURE_AD_SECRET
      ? [
          MicrosoftEntraID({
            clientId: env.AUTH_AZURE_AD_ID,
            clientSecret: env.AUTH_AZURE_AD_SECRET,
            profile(profile) {
              const email = profile.email || profile.preferred_username || profile.upn;
              return {
                id: profile.sub || profile.oid,
                name: profile.name,
                email,
                image: null, // Microsoft profile doesn't have picture field in this version
              };
            },
          }),
        ]
      : []),
  ],
  
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
    verifyRequest: "/auth/verify-request",
  },
  
  callbacks: {
    async signIn({ user, profile }) {
      const email = (user.email || (profile as any)?.preferred_username || (profile as any)?.upn || "").toLowerCase();
      if (!email) return false;

      const isUserAllowed = await isAllowed(email);
      const isDomainOk = await isDomainAllowed(email);
      
      if (isUserAllowed || isDomainOk) {
        // If domain is allowed but not in allowlist, auto-approve
        if (!isUserAllowed && isDomainOk) {
          await dynamoDBClient.put({
            TableName: env.ALLOWLIST_TABLE_NAME || "trialynx-allowlist",
            Item: {
              pk: `EMAIL#${email}`,
              email,
              status: "active",
              source: "domain-rule",
              createdAt: new Date().toISOString(),
            },
          });
        }
        return true;
      }

      // Add to waitlist and block sign-in
      await addToWaitlist(email);
      return false;
    },
    
    async redirect({ url, baseUrl }) {
      // Redirect to invite-required page if sign-in was blocked
      if (url.includes("/api/auth/error")) {
        return `${baseUrl}/auth/invite-required`;
      }
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
    
    session: ({ session, user }) => ({
      ...session,
      user: {
        ...session.user,
        id: user.id,
        role: (user as any).role ?? "researcher",
      },
    }),
  },
  
  events: {
    async signIn({ user, profile }) {
      console.log(`User signed in: ${user.email}`);
    },
    async signOut(message) {
      console.log(`User signed out:`, message);
    },
  },
} satisfies NextAuthConfig;
