#!/bin/bash

# User management script for Trialynx Insights allowlist/waitlist

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

ENVIRONMENT=${ENVIRONMENT:-dev}
AWS_REGION=${AWS_REGION:-us-west-2}

ALLOWLIST_TABLE="trialynx-insights-$ENVIRONMENT-allowlist"
WAITLIST_TABLE="trialynx-insights-$ENVIRONMENT-waitlist"

# Function to add user to allowlist
add_user() {
    local email="$1"
    local source="${2:-manual}"
    
    echo -e "${BLUE}Adding $email to allowlist...${NC}"
    
    aws dynamodb put-item \
        --table-name "$ALLOWLIST_TABLE" \
        --item "{
            \"pk\": {\"S\": \"EMAIL#$email\"},
            \"email\": {\"S\": \"$email\"},
            \"status\": {\"S\": \"active\"},
            \"source\": {\"S\": \"$source\"},
            \"createdAt\": {\"S\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"}
        }"
    
    echo -e "${GREEN}✅ User $email added to allowlist${NC}"
}

# Function to remove user from allowlist
remove_user() {
    local email="$1"
    
    echo -e "${BLUE}Removing $email from allowlist...${NC}"
    
    aws dynamodb delete-item \
        --table-name "$ALLOWLIST_TABLE" \
        --key "{
            \"pk\": {\"S\": \"EMAIL#$email\"}
        }"
    
    echo -e "${GREEN}✅ User $email removed from allowlist${NC}"
}

# Function to list allowlist users
list_allowlist() {
    echo -e "${BLUE}Allowlist users:${NC}"
    
    aws dynamodb scan \
        --table-name "$ALLOWLIST_TABLE" \
        --projection-expression "email, #s, source, createdAt" \
        --expression-attribute-names '{"#s": "status"}' \
        --output table
}

# Function to list waitlist users
list_waitlist() {
    echo -e "${BLUE}Waitlist users:${NC}"
    
    aws dynamodb scan \
        --table-name "$WAITLIST_TABLE" \
        --projection-expression "email, createdAt" \
        --output table
}

# Function to approve waitlist user
approve_waitlist_user() {
    local email="$1"
    
    echo -e "${BLUE}Approving waitlist user $email...${NC}"
    
    # Add to allowlist
    add_user "$email" "waitlist-approval"
    
    # Remove from waitlist
    aws dynamodb delete-item \
        --table-name "$WAITLIST_TABLE" \
        --key "{
            \"pk\": {\"S\": \"EMAIL#$email\"}
        }"
    
    echo -e "${GREEN}✅ User $email approved and moved to allowlist${NC}"
}

# Function to check user status
check_user() {
    local email="$1"
    
    echo -e "${BLUE}Checking status for $email...${NC}"
    
    # Check allowlist
    ALLOWLIST_RESULT=$(aws dynamodb get-item \
        --table-name "$ALLOWLIST_TABLE" \
        --key "{\"pk\": {\"S\": \"EMAIL#$email\"}}" \
        --output json 2>/dev/null || echo "{}")
    
    if [ "$(echo "$ALLOWLIST_RESULT" | jq -r '.Item')" != "null" ]; then
        STATUS=$(echo "$ALLOWLIST_RESULT" | jq -r '.Item.status.S')
        SOURCE=$(echo "$ALLOWLIST_RESULT" | jq -r '.Item.source.S')
        CREATED=$(echo "$ALLOWLIST_RESULT" | jq -r '.Item.createdAt.S')
        echo -e "${GREEN}✅ User is in allowlist${NC}"
        echo -e "${BLUE}Status: $STATUS${NC}"
        echo -e "${BLUE}Source: $SOURCE${NC}"
        echo -e "${BLUE}Created: $CREATED${NC}"
        return
    fi
    
    # Check waitlist
    WAITLIST_RESULT=$(aws dynamodb get-item \
        --table-name "$WAITLIST_TABLE" \
        --key "{\"pk\": {\"S\": \"EMAIL#$email\"}}" \
        --output json 2>/dev/null || echo "{}")
    
    if [ "$(echo "$WAITLIST_RESULT" | jq -r '.Item')" != "null" ]; then
        CREATED=$(echo "$WAITLIST_RESULT" | jq -r '.Item.createdAt.S')
        echo -e "${YELLOW}⏳ User is in waitlist${NC}"
        echo -e "${BLUE}Created: $CREATED${NC}"
        return
    fi
    
    echo -e "${RED}❌ User not found in allowlist or waitlist${NC}"
}

# Function to show usage
show_usage() {
    echo -e "${BLUE}Trialynx Insights User Management${NC}"
    echo -e "${BLUE}Environment: $ENVIRONMENT${NC}"
    echo ""
    echo "Usage: $0 <command> [arguments]"
    echo ""
    echo "Commands:"
    echo "  add <email>              Add user to allowlist"
    echo "  remove <email>           Remove user from allowlist"
    echo "  check <email>            Check user status"
    echo "  approve <email>          Approve waitlist user"
    echo "  list-allowlist           List all allowlist users"
    echo "  list-waitlist            List all waitlist users"
    echo ""
    echo "Examples:"
    echo "  $0 add <EMAIL>"
    echo "  $0 check <EMAIL>"
    echo "  $0 approve <EMAIL>"
}

# Main script logic
case "${1:-}" in
    "add")
        if [ -z "${2:-}" ]; then
            echo -e "${RED}Error: Email address required${NC}"
            show_usage
            exit 1
        fi
        add_user "$2"
        ;;
    "remove")
        if [ -z "${2:-}" ]; then
            echo -e "${RED}Error: Email address required${NC}"
            show_usage
            exit 1
        fi
        remove_user "$2"
        ;;
    "check")
        if [ -z "${2:-}" ]; then
            echo -e "${RED}Error: Email address required${NC}"
            show_usage
            exit 1
        fi
        check_user "$2"
        ;;
    "approve")
        if [ -z "${2:-}" ]; then
            echo -e "${RED}Error: Email address required${NC}"
            show_usage
            exit 1
        fi
        approve_waitlist_user "$2"
        ;;
    "list-allowlist")
        list_allowlist
        ;;
    "list-waitlist")
        list_waitlist
        ;;
    *)
        show_usage
        ;;
esac