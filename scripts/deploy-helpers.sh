#!/bin/bash

# Deployment helper scripts for Trialynx Insights
# Usage: ./scripts/deploy-helpers.sh [command]

set -e

AWS_PROFILE=${AWS_PROFILE:-"AdministratorAccess-030430646103"}
TERRAFORM_DIR="infra/terraform"

function show_help() {
    echo "Trialynx Insights Deployment Helpers"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  build           Build and push Docker image to ECR"
    echo "  deploy          Deploy latest changes to ECS"
    echo "  logs            Show recent application logs"
    echo "  status          Show ECS service status"
    echo "  unlock          Force unlock Terraform state (use carefully!)"
    echo "  add-user EMAIL  Add user to allowlist"
    echo "  test-app        Test if application is responding"
    echo ""
}

function build_and_push() {
    echo "Building and pushing Docker image..."
    cd apps/web
    
    # Build image
    docker build -t trialynx-insights-dev-app:latest .
    
    # Login to ECR
    aws ecr get-login-password --region us-west-2 --profile "$AWS_PROFILE" | \
        docker login --username AWS --password-stdin 030430646103.dkr.ecr.us-west-2.amazonaws.com
    
    # Tag and push
    docker tag trialynx-insights-dev-app:latest 030430646103.dkr.ecr.us-west-2.amazonaws.com/trialynx-insights-dev-app:latest
    docker push 030430646103.dkr.ecr.us-west-2.amazonaws.com/trialynx-insights-dev-app:latest
    
    echo "✅ Image pushed successfully"
    cd ../..
}

function deploy_ecs() {
    echo "Deploying to ECS..."
    aws ecs update-service \
        --cluster trialynx-insights-dev-cluster \
        --service trialynx-insights-dev-service \
        --force-new-deployment \
        --profile "$AWS_PROFILE"
    echo "✅ Deployment initiated"
}

function show_logs() {
    echo "Fetching recent application logs..."
    
    # Get latest log stream
    STREAM=$(aws logs describe-log-streams \
        --log-group-name "/ecs/trialynx-insights-dev" \
        --order-by LastEventTime \
        --descending \
        --max-items 1 \
        --query 'logStreams[0].logStreamName' \
        --output text \
        --profile "$AWS_PROFILE")
    
    if [ "$STREAM" != "None" ]; then
        aws logs get-log-events \
            --log-group-name "/ecs/trialynx-insights-dev" \
            --log-stream-name "$STREAM" \
            --start-time $(date -d '30 minutes ago' +%s)000 \
            --query 'events[].message' \
            --output text \
            --profile "$AWS_PROFILE"
    else
        echo "No log streams found"
    fi
}

function show_status() {
    echo "ECS Service Status:"
    aws ecs describe-services \
        --cluster trialynx-insights-dev-cluster \
        --services trialynx-insights-dev-service \
        --query 'services[0].{Status:status,Running:runningCount,Desired:desiredCount,TaskDef:taskDefinition}' \
        --output table \
        --profile "$AWS_PROFILE"
}

function unlock_terraform() {
    echo "⚠️  Force unlocking Terraform state..."
    read -p "Are you sure you want to force unlock? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cd "$TERRAFORM_DIR"
        AWS_PROFILE="$AWS_PROFILE" terraform force-unlock -force $(terraform show -json 2>/dev/null | jq -r '.values.root_module.resources[] | select(.type=="terraform_remote_state") | .values.lock_id' 2>/dev/null || echo "")
        cd ../..
    fi
}

function add_user() {
    local email="$1"
    if [ -z "$email" ]; then
        echo "❌ Please provide an email address"
        echo "Usage: $0 add-user <EMAIL>"
        exit 1
    fi
    
    echo "Adding $email to allowlist..."
    aws dynamodb put-item \
        --table-name trialynx-insights-dev-allowlist \
        --item "{\"pk\":{\"S\":\"EMAIL#${email}\"},\"email\":{\"S\":\"${email}\"},\"role\":{\"S\":\"admin\"},\"status\":{\"S\":\"active\"},\"createdAt\":{\"S\":\"$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)\"},\"createdBy\":{\"S\":\"deploy-script\"}}" \
        --profile "$AWS_PROFILE"
    echo "✅ User added successfully"
}

function test_app() {
    echo "Testing application health..."
    curl -I http://trialynx-insights-dev-alb-162105436.us-west-2.elb.amazonaws.com/api/health
    echo "✅ Application is responding"
}

# Main command handling
case "${1:-help}" in
    "build")
        build_and_push
        ;;
    "deploy")
        deploy_ecs
        ;;
    "logs")
        show_logs
        ;;
    "status")
        show_status
        ;;
    "unlock")
        unlock_terraform
        ;;
    "add-user")
        add_user "$2"
        ;;
    "test-app")
        test_app
        ;;
    "help"|*)
        show_help
        ;;
esac