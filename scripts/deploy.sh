#!/bin/bash

# Trialynx Insights Deployment Script
# This script deploys the application to AWS ECS Fargate

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${ENVIRONMENT:-dev}
AWS_REGION=${AWS_REGION:-us-west-2}
APP_NAME="trialynx-insights"

# Directories
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TERRAFORM_DIR="$PROJECT_ROOT/infra/terraform"
WEB_APP_DIR="$PROJECT_ROOT/apps/web"

echo -e "${BLUE}🚀 Starting Trialynx Insights Deployment${NC}"
echo -e "${BLUE}Environment: $ENVIRONMENT${NC}"
echo -e "${BLUE}Region: $AWS_REGION${NC}"

# Function to print step headers
print_step() {
    echo -e "\n${GREEN}==== $1 ====${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
print_step "Checking Prerequisites"

if ! command_exists aws; then
    echo -e "${RED}❌ AWS CLI is not installed${NC}"
    exit 1
fi

if ! command_exists terraform; then
    echo -e "${RED}❌ Terraform is not installed${NC}"
    exit 1
fi

if ! command_exists docker; then
    echo -e "${RED}❌ Docker is not installed${NC}"
    exit 1
fi

# Check AWS credentials
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo -e "${RED}❌ AWS credentials not configured or invalid${NC}"
    exit 1
fi

echo -e "${GREEN}✅ All prerequisites met${NC}"

# Get AWS account ID
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
echo -e "${BLUE}AWS Account ID: $AWS_ACCOUNT_ID${NC}"

# Deploy infrastructure
print_step "Deploying Infrastructure with Terraform"

cd "$TERRAFORM_DIR"

# Initialize Terraform if needed
if [ ! -d ".terraform" ]; then
    terraform init
fi

# Plan and apply Terraform
terraform plan -var="environment=$ENVIRONMENT" -var="aws_region=$AWS_REGION"
read -p "Do you want to apply this Terraform plan? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    terraform apply -var="environment=$ENVIRONMENT" -var="aws_region=$AWS_REGION" -auto-approve
else
    echo -e "${YELLOW}⚠️ Terraform apply cancelled${NC}"
    exit 1
fi

# Get Terraform outputs
ECR_REPOSITORY_URL=$(terraform output -raw ecr_repository_url)
ECS_CLUSTER_NAME=$(terraform output -raw ecs_cluster_name)
ECS_SERVICE_NAME=$(terraform output -raw ecs_service_name)

echo -e "${GREEN}✅ Infrastructure deployed${NC}"
echo -e "${BLUE}ECR Repository: $ECR_REPOSITORY_URL${NC}"

# Build and push Docker image
print_step "Building and Pushing Docker Image"

cd "$WEB_APP_DIR"

# Login to ECR
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REPOSITORY_URL

# Build Docker image
IMAGE_TAG="${ENVIRONMENT}-$(date +%Y%m%d%H%M%S)"
FULL_IMAGE_NAME="$ECR_REPOSITORY_URL:$IMAGE_TAG"

echo -e "${BLUE}Building Docker image: $FULL_IMAGE_NAME${NC}"
docker build -t $FULL_IMAGE_NAME .

# Push image to ECR
echo -e "${BLUE}Pushing image to ECR...${NC}"
docker push $FULL_IMAGE_NAME

# Tag as latest
docker tag $FULL_IMAGE_NAME $ECR_REPOSITORY_URL:latest
docker push $ECR_REPOSITORY_URL:latest

echo -e "${GREEN}✅ Docker image built and pushed${NC}"

# Update ECS service
print_step "Updating ECS Service"

# Get current task definition
TASK_DEFINITION_ARN=$(aws ecs describe-services \
    --cluster $ECS_CLUSTER_NAME \
    --services $ECS_SERVICE_NAME \
    --query 'services[0].taskDefinition' \
    --output text)

# Get task definition details
TASK_DEFINITION=$(aws ecs describe-task-definition \
    --task-definition $TASK_DEFINITION_ARN \
    --query 'taskDefinition')

# Update the image in the task definition
NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | jq --arg IMAGE "$FULL_IMAGE_NAME" '
    .containerDefinitions[0].image = $IMAGE |
    del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .placementConstraints, .compatibilities, .registeredAt, .registeredBy)
')

# Register new task definition
NEW_TASK_DEFINITION_ARN=$(aws ecs register-task-definition \
    --cli-input-json "$NEW_TASK_DEFINITION" \
    --query 'taskDefinition.taskDefinitionArn' \
    --output text)

echo -e "${BLUE}New task definition: $NEW_TASK_DEFINITION_ARN${NC}"

# Update the service
aws ecs update-service \
    --cluster $ECS_CLUSTER_NAME \
    --service $ECS_SERVICE_NAME \
    --task-definition $NEW_TASK_DEFINITION_ARN \
    --force-new-deployment > /dev/null

echo -e "${GREEN}✅ ECS service updated${NC}"

# Wait for deployment to complete
print_step "Waiting for Deployment to Complete"

echo -e "${BLUE}Waiting for service to stabilize...${NC}"
aws ecs wait services-stable \
    --cluster $ECS_CLUSTER_NAME \
    --services $ECS_SERVICE_NAME

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"

# Get application URL
APPLICATION_URL=$(cd "$TERRAFORM_DIR" && terraform output -raw application_url)
echo -e "\n${GREEN}🎉 Application deployed successfully!${NC}"
echo -e "${GREEN}URL: $APPLICATION_URL${NC}"

# Display next steps
print_step "Next Steps"
echo -e "${YELLOW}1. Update DNS records in production account (see terraform-prod-dns/)${NC}"
echo -e "${YELLOW}2. Configure Secrets Manager with OAuth and email credentials${NC}"
echo -e "${YELLOW}3. Test the application and auth flows${NC}"
echo -e "${YELLOW}4. Set up monitoring alerts and notifications${NC}"

echo -e "\n${GREEN}Deployment script completed!${NC}"