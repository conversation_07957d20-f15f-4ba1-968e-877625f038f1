# GoDaddy DNS + AWS Deployment Guide

This guide covers deploying Trialynx Insights when your domain (`trialynx.io`) is managed by GoDaddy instead of Route 53.

## 🎯 Deployment Options

### Option A: Manual DNS Management (Simpler)
- Create DNS records manually in GoDaddy
- Suitable for single environment (dev)
- Less automation, more manual steps

### Option B: Subdomain Delegation (Recommended)
- Delegate `insights.trialynx.io` to Route 53
- Full automation for all insights subdomains
- Easier to manage multiple environments

## 🚀 Option B: Subdomain Delegation (Recommended)

### Step 1: Deploy AWS Infrastructure

```bash
# Set up dev account
export AWS_PROFILE=trialynx-dev
cd infra/terraform

# Configure terraform.tfvars
cp terraform.tfvars.example terraform.tfvars
```

Update `terraform.tfvars`:
```hcl
aws_region     = "us-west-2"
environment    = "dev"
domain_name    = "dev.insights.trialynx.io"

# Remove route53_zone_id - we'll create our own zone
# route53_zone_id = "..." # Comment this out

# Your existing values
bedrock_knowledge_base_id = "YOUR_KB_ID"
ses_domain = "trialynx.io"  # Keep using existing SES setup
```

```bash
# Deploy infrastructure
terraform init
terraform plan
terraform apply
```

### Step 2: Set Up Subdomain Delegation in GoDaddy

After Terraform completes, get the name servers:
```bash
terraform output route53_name_servers
```

**In GoDaddy DNS Management:**
1. Log into GoDaddy
2. Go to DNS Management for `trialynx.io`
3. Add NS (Name Server) records:

```
Type: NS
Name: insights
Value: ns-123.awsdns-12.com
TTL: 600

Type: NS  
Name: insights
Value: ns-456.awsdns-34.net
TTL: 600

Type: NS
Name: insights  
Value: ns-789.awsdns-56.org
TTL: 600

Type: NS
Name: insights
Value: ns-012.awsdns-78.co.uk  
TTL: 600
```

### Step 3: Verify and Test

```bash
# Test DNS delegation (may take 5-15 minutes)
dig insights.trialynx.io NS
dig dev.insights.trialynx.io

# Check certificate status
aws acm list-certificates --region us-west-2
```

## 🛠 Option A: Manual DNS Management

If you prefer to manage DNS records manually in GoDaddy:

### Step 1: Deploy Without Route 53

Comment out or remove the `route53-subdomain.tf` file:
```bash
mv infra/terraform/route53-subdomain.tf infra/terraform/route53-subdomain.tf.disabled
```

Deploy infrastructure:
```bash
terraform apply
```

### Step 2: Get DNS Records to Create

```bash
# Get certificate validation requirements
terraform output acm_domain_validation_options

# Get ALB endpoint
terraform output alb_dns_name
```

### Step 3: Create Records in GoDaddy

**Certificate Validation CNAME:**
```
Type: CNAME
Name: _abc123def.dev.insights
Value: _xyz789.acm-validations.aws.
TTL: 600
```

**Application CNAME:**
```
Type: CNAME
Name: dev.insights
Value: trialynx-insights-dev-alb-123456.us-west-2.elb.amazonaws.com
TTL: 600
```

## 🔄 Updated Variable Configuration

Update your `terraform.tfvars` file:

```hcl
# Basic Configuration
aws_region     = "us-west-2"
environment    = "dev"
project_owner  = "<EMAIL>"
domain_name    = "dev.insights.trialynx.io"

# Remove this line since we're not using existing Route 53
# route53_zone_id = "..."

# SES Configuration (keep existing)
ses_domain          = "trialynx.io"
email_from_address  = "<EMAIL>"

# Bedrock Configuration
bedrock_knowledge_base_id = "YOUR_KB_ID"

# Container Configuration  
container_image = "nginx:latest"  # Will be updated after ECR creation
```

## 📧 Email Configuration (Unchanged)

Since you already have SES set up for `trialynx.io`:

1. **No changes needed** to SES domain verification
2. **Keep existing email configuration** in your application
3. **Update email secrets** as normal:

```bash
./scripts/setup-secrets.sh
```

## 🚦 Deployment Steps Summary

### For Subdomain Delegation (Option B):
1. Deploy AWS infrastructure → Get name servers
2. Add NS records in GoDaddy for `insights.trialynx.io`
3. Wait for DNS propagation (5-15 minutes)
4. Certificate validates automatically
5. Build and deploy application

### For Manual DNS (Option A):
1. Deploy AWS infrastructure → Get validation requirements
2. Add CNAME records manually in GoDaddy
3. Wait for certificate validation (5-10 minutes)
4. Add application CNAME record
5. Build and deploy application

## 🎯 Recommendations

**Use Option B (Subdomain Delegation) if:**
- You plan to have multiple environments (dev, staging, prod)
- You want automated certificate management
- You prefer infrastructure-as-code approach

**Use Option A (Manual DNS) if:**
- You only need one environment
- You prefer to keep all DNS in GoDaddy
- You want minimal changes to existing setup

## 🔍 Troubleshooting

**Certificate not validating:**
- Check CNAME records in GoDaddy match exactly
- Wait 10-15 minutes for DNS propagation
- Verify no typos in validation values

**DNS not resolving:**
- Test with `dig dev.insights.trialynx.io`
- Check TTL values (use 600 seconds)
- Verify GoDaddy propagation status

**Application not accessible:**
- Confirm ALB is healthy in AWS console
- Check ECS service status
- Verify security groups allow traffic

## 📞 Next Steps After DNS Setup

Once DNS is working:

1. **Build and deploy application:**
   ```bash
   ./scripts/deploy.sh
   ```

2. **Configure OAuth providers** with callback URLs:
   - Google: `https://dev.insights.trialynx.io/api/auth/callback/google`
   - Microsoft: `https://dev.insights.trialynx.io/api/auth/callback/azure-ad`

3. **Set up secrets and test authentication**

The rest of the deployment process remains exactly the same!