# Trialynx Insights Deployment Guide

This guide walks you through deploying the Trialynx Insights application to AWS using ECS Fargate with Auth.js v5, DynamoDB sessions, and cross-account DNS setup.

## Architecture Overview

- **Frontend + API**: Next.js app deployed to ECS Fargate behind an ALB
- **Authentication**: Auth.js v5 with Email (magic links), Google, and Microsoft providers
- **Session Storage**: DynamoDB with TTL
- **Email**: SES for magic link delivery
- **DNS**: Cross-account setup (prod account owns domain, dev account owns infrastructure)
- **Security**: WAF, HTTPS-only, invite-only access control

## Prerequisites

### Required Tools
- AWS CLI v2
- Terraform >= 1.0
- Docker
- Node.js 20+
- jq (for JSON processing)

### AWS Accounts
- **Dev Account**: Where the application infrastructure will be deployed
- **Prod Account**: Where the `trialynx.io` Route 53 zone is managed

### Credentials Setup
```bash
# Configure AWS CLI for dev account
aws configure --profile dev-account
aws configure set region us-west-2 --profile dev-account

# Configure AWS CLI for prod account (if different)
aws configure --profile prod-account
aws configure set region us-east-1 --profile prod-account
```

## Deployment Steps

### 1. Prepare the Application

#### Update Environment Variables Schema
The application has been updated to support the new authentication system. Review the environment variables in `apps/web/src/env.js`.

#### Build and Test Locally
```bash
cd apps/web
npm install
npm run build
npm run dev
```

### 2. Deploy Infrastructure (Dev Account)

#### Configure Terraform Variables
```bash
cd infra/terraform
cp terraform.tfvars.example terraform.tfvars
```

Edit `terraform.tfvars`:
```hcl
aws_region     = "us-west-2"
environment    = "dev"
project_owner  = "<EMAIL>"
domain_name    = "dev.insights.trialynx.io"
container_image = "nginx:latest"  # Will be updated by deploy script

# These will be provided by your organization
bedrock_knowledge_base_id = "YOUR_KB_ID"
route53_zone_id          = "YOUR_ZONE_ID"  # From prod account
```

#### Deploy Infrastructure
```bash
# Option 1: Use automated script
../scripts/deploy.sh

# Option 2: Manual deployment
terraform init
terraform plan
terraform apply
```

#### Capture Outputs for DNS Setup
```bash
terraform output > ../terraform-outputs.txt
```

### 3. Configure DNS Records (Prod Account)

#### Switch to Prod Account
```bash
export AWS_PROFILE=prod-account
cd ../terraform-prod-dns
```

#### Configure Variables
```bash
cp terraform.tfvars.example terraform.tfvars
```

Fill in values from dev account Terraform outputs:
```hcl
# From dev account outputs
dev_alb_dns_name = "trialynx-insights-dev-alb-xxx.us-west-2.elb.amazonaws.com"
dev_alb_zone_id  = "Z1D633PJN98FT9"

# Copy the entire acm_domain_validation_options output
acm_domain_validation_options = [
  {
    domain_name           = "dev.insights.trialynx.io"
    resource_record_name  = "_abc123.dev.insights.trialynx.io."
    resource_record_type  = "CNAME"
    resource_record_value = "_xyz789.acm-validations.aws."
  }
]

# SES tokens from dev account
ses_verification_token = "your-verification-token"
ses_dkim_tokens       = ["token1", "token2", "token3"]
```

#### Deploy DNS Records
```bash
terraform init
terraform plan
terraform apply
```

### 4. Configure Secrets (Dev Account)

Switch back to dev account and configure authentication secrets:

```bash
export AWS_PROFILE=dev-account
```

#### Auth.js Secret
```bash
aws secretsmanager put-secret-value \
  --secret-id trialynx-insights-dev-auth-secret \
  --secret-string "$(openssl rand -base64 32)"
```

#### OAuth Secrets
```bash
aws secretsmanager put-secret-value \
  --secret-id trialynx-insights-dev-oauth-secrets \
  --secret-string '{
    "AUTH_GOOGLE_ID": "your-google-client-id",
    "AUTH_GOOGLE_SECRET": "your-google-client-secret",
    "AUTH_AZURE_AD_ID": "your-azure-app-id",
    "AUTH_AZURE_AD_SECRET": "your-azure-app-secret",
    "AUTH_AZURE_AD_TENANT_ID": "your-azure-tenant-id"
  }'
```

#### Email Configuration
```bash
aws secretsmanager put-secret-value \
  --secret-id trialynx-insights-dev-email-secret \
  --secret-string "smtps://username:<EMAIL>:465"
```

### 5. Build and Deploy Application

#### Update Container Image
```bash
cd ../../apps/web

# Get ECR repository URL
ECR_REPO=$(aws sts get-caller-identity --query Account --output text).dkr.ecr.us-west-2.amazonaws.com/trialynx-insights-dev-app

# Login to ECR
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin $ECR_REPO

# Build and push
docker build -t $ECR_REPO:latest .
docker push $ECR_REPO:latest
```

#### Update Terraform with Real Image
```bash
cd ../../infra/terraform

# Update terraform.tfvars
container_image = "*********.dkr.ecr.us-west-2.amazonaws.com/trialynx-insights-dev-app:latest"

# Apply changes
terraform apply
```

### 6. Configure OAuth Providers

#### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create OAuth 2.0 Client ID
3. Add authorized redirect URI: `https://dev.insights.trialynx.io/api/auth/callback/google`

#### Microsoft Entra ID Setup
1. Go to [Azure Portal](https://portal.azure.com/)
2. Create App Registration
3. Add redirect URI: `https://dev.insights.trialynx.io/api/auth/callback/azure-ad`

### 7. Initial User Setup

#### Add Users to Allowlist
```bash
aws dynamodb put-item \
  --table-name trialynx-insights-dev-allowlist \
  --item '{
    "pk": {"S": "EMAIL#<EMAIL>"},
    "email": {"S": "<EMAIL>"}, 
    "status": {"S": "active"},
    "source": {"S": "manual"},
    "createdAt": {"S": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"}
  }'
```

### 8. Verification

#### Check Certificate Status
```bash
aws acm list-certificates --region us-west-2
aws acm describe-certificate --certificate-arn YOUR_CERT_ARN
```

#### Test Application
1. Visit `https://dev.insights.trialynx.io`
2. Test sign-in flows
3. Check health endpoint: `https://dev.insights.trialynx.io/api/health`

#### Verify SES
```bash
aws ses get-identity-verification-attributes --identities trialynx.io
```

## Monitoring and Maintenance

### CloudWatch Dashboard
Access the monitoring dashboard:
```
https://us-west-2.console.aws.amazon.com/cloudwatch/home?region=us-west-2#dashboards:name=trialynx-insights-dev-dashboard
```

### Key Metrics to Monitor
- ALB 5XX errors
- ECS CPU/Memory utilization
- DynamoDB throttles
- Certificate expiration

### Scaling Configuration
The application automatically scales between 1-3 instances based on:
- CPU utilization (70% target)
- Memory utilization (80% target)
- Request count per target (100 requests/target)

### Log Access
```bash
# ECS logs
aws logs describe-log-groups --log-group-name-prefix "/ecs/trialynx-insights-dev"

# WAF logs
aws logs describe-log-groups --log-group-name-prefix "/aws/wafv2/trialynx-insights-dev"
```

## Troubleshooting

### Certificate Not Validating
1. Check DNS records in prod account
2. Wait 5-10 minutes for propagation
3. Verify domain validation options haven't changed

### ECS Tasks Not Starting
1. Check ECS service events
2. Verify container image exists in ECR
3. Check IAM permissions for task role
4. Review CloudWatch logs

### Authentication Issues
1. Verify OAuth callback URLs
2. Check Secrets Manager values
3. Confirm DynamoDB table permissions
4. Test email delivery with SES

### DNS Resolution Issues
1. Test DNS propagation: `dig dev.insights.trialynx.io`
2. Verify Route 53 records in prod account
3. Check ALB health in dev account

## Security Considerations

### WAF Rules
- Rate limiting on auth endpoints (100 req/min per IP)
- General rate limiting (1000 req/min per IP)
- AWS managed rules for common attacks

### Secrets Management
- All sensitive data stored in AWS Secrets Manager
- Secrets rotated regularly
- No hardcoded credentials

### Network Security
- Private subnets for ECS tasks
- Security groups with minimal access
- HTTPS-only with HTTP redirect

### Access Control
- Invite-only system with allowlist/waitlist
- Domain-based auto-approval for @trialynx.io emails
- Session TTL management

## Next Steps

1. **Production Environment**: Replicate setup for production
2. **CI/CD Pipeline**: Automate deployments with GitHub Actions
3. **Backup Strategy**: Configure DynamoDB backups
4. **Disaster Recovery**: Plan for multi-region deployment
5. **Performance Tuning**: Optimize based on usage patterns